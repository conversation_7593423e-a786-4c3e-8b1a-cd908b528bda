<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug - Crypto Tracker</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <h1>Crypto Tracker Debug Page</h1>
    <div id="status"></div>
    
    <script>
        const statusDiv = document.getElementById('status');
        
        function addStatus(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.textContent = message;
            statusDiv.appendChild(div);
        }
        
        addStatus('Debug page loaded successfully', 'success');
        addStatus('Checking if React can load...', 'info');
        
        // Test if we can load React
        try {
            const script = document.createElement('script');
            script.src = '/src/main.tsx';
            script.type = 'module';
            script.onload = () => addStatus('Main script loaded', 'success');
            script.onerror = (e) => addStatus('Main script failed to load: ' + e.message, 'error');
            document.head.appendChild(script);
        } catch (e) {
            addStatus('Error loading main script: ' + e.message, 'error');
        }
        
        // Check for common issues
        addStatus('Browser: ' + navigator.userAgent, 'info');
        addStatus('Current URL: ' + window.location.href, 'info');
        
        // Listen for errors
        window.addEventListener('error', (e) => {
            addStatus('JavaScript Error: ' + e.message + ' at ' + e.filename + ':' + e.lineno, 'error');
        });
        
        window.addEventListener('unhandledrejection', (e) => {
            addStatus('Unhandled Promise Rejection: ' + e.reason, 'error');
        });
    </script>
</body>
</html>
