const mongoose = require('mongoose');
const { v4: uuidv4 } = require('uuid');

// MongoDB Connection String
const MONGODB_URI = 'mongodb+srv://ziqra:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0';

// Connect to MongoDB
mongoose.connect(MONGODB_URI)
  .then(() => console.log('Connected to MongoDB'))
  .catch(err => console.error('MongoDB connection error:', err));

// Define schemas and models
const cryptoSchema = new mongoose.Schema({
  name: { type: String, required: true },
  symbol: { type: String, required: true },
  currentPrice: { type: Number, required: true },
  marketCap: { type: Number, required: true },
  volume24h: { type: Number, required: true },
  priceChangePercentage24h: { type: Number, required: true },
  image: { type: String },
  rank: { type: Number },
});

const Crypto = mongoose.model('Crypto', cryptoSchema);

// Generate random price change percentage between -10% and +10%
const getRandomPriceChange = () => {
  return parseFloat((Math.random() * 20 - 10).toFixed(2));
};

// Generate random volume between 100M and 10B
const getRandomVolume = () => {
  return Math.floor(Math.random() * 9900000000 + 100000000);
};

// Mock cryptocurrencies data
const mockCryptos = [
  {
    name: 'Bitcoin',
    symbol: 'BTC',
    currentPrice: 50000 + Math.random() * 5000,
    priceChangePercentage24h: getRandomPriceChange(),
    volume24h: getRandomVolume(),
    marketCap: 1000000000000,
    rank: 1,
    image: 'https://cryptologos.cc/logos/bitcoin-btc-logo.png',
  },
  {
    name: 'Ethereum',
    symbol: 'ETH',
    currentPrice: 3000 + Math.random() * 300,
    priceChangePercentage24h: getRandomPriceChange(),
    volume24h: getRandomVolume(),
    marketCap: 350000000000,
    rank: 2,
    image: 'https://cryptologos.cc/logos/ethereum-eth-logo.png',
  },
  {
    name: 'Tether',
    symbol: 'USDT',
    currentPrice: 0.99 + Math.random() * 0.02,
    priceChangePercentage24h: parseFloat((Math.random() * 0.5 - 0.25).toFixed(2)),
    volume24h: getRandomVolume(),
    marketCap: 80000000000,
    rank: 3,
    image: 'https://cryptologos.cc/logos/tether-usdt-logo.png',
  },
  {
    name: 'BNB',
    symbol: 'BNB',
    currentPrice: 400 + Math.random() * 40,
    priceChangePercentage24h: getRandomPriceChange(),
    volume24h: getRandomVolume(),
    marketCap: 65000000000,
    rank: 4,
    image: 'https://cryptologos.cc/logos/bnb-bnb-logo.png',
  },
  {
    name: 'Solana',
    symbol: 'SOL',
    currentPrice: 100 + Math.random() * 20,
    priceChangePercentage24h: getRandomPriceChange(),
    volume24h: getRandomVolume(),
    marketCap: 40000000000,
    rank: 5,
    image: 'https://cryptologos.cc/logos/solana-sol-logo.png',
  },
  {
    name: 'XRP',
    symbol: 'XRP',
    currentPrice: 0.5 + Math.random() * 0.1,
    priceChangePercentage24h: getRandomPriceChange(),
    volume24h: getRandomVolume(),
    marketCap: 25000000000,
    rank: 6,
    image: 'https://cryptologos.cc/logos/xrp-xrp-logo.png',
  },
  {
    name: 'Cardano',
    symbol: 'ADA',
    currentPrice: 0.4 + Math.random() * 0.1,
    priceChangePercentage24h: getRandomPriceChange(),
    volume24h: getRandomVolume(),
    marketCap: 15000000000,
    rank: 7,
    image: 'https://cryptologos.cc/logos/cardano-ada-logo.png',
  },
  {
    name: 'Dogecoin',
    symbol: 'DOGE',
    currentPrice: 0.08 + Math.random() * 0.02,
    priceChangePercentage24h: getRandomPriceChange(),
    volume24h: getRandomVolume(),
    marketCap: 12000000000,
    rank: 8,
    image: 'https://cryptologos.cc/logos/dogecoin-doge-logo.png',
  },
  {
    name: 'Polkadot',
    symbol: 'DOT',
    currentPrice: 6 + Math.random() * 1,
    priceChangePercentage24h: getRandomPriceChange(),
    volume24h: getRandomVolume(),
    marketCap: 8000000000,
    rank: 9,
    image: 'https://cryptologos.cc/logos/polkadot-new-dot-logo.png',
  },
  {
    name: 'Polygon',
    symbol: 'MATIC',
    currentPrice: 0.8 + Math.random() * 0.2,
    priceChangePercentage24h: getRandomPriceChange(),
    volume24h: getRandomVolume(),
    marketCap: 7500000000,
    rank: 10,
    image: 'https://cryptologos.cc/logos/polygon-matic-logo.png',
  },
  {
    name: 'Litecoin',
    symbol: 'LTC',
    currentPrice: 70 + Math.random() * 10,
    priceChangePercentage24h: getRandomPriceChange(),
    volume24h: getRandomVolume(),
    marketCap: 5000000000,
    rank: 11,
    image: 'https://cryptologos.cc/logos/litecoin-ltc-logo.png',
  },
  {
    name: 'Chainlink',
    symbol: 'LINK',
    currentPrice: 12 + Math.random() * 2,
    priceChangePercentage24h: getRandomPriceChange(),
    volume24h: getRandomVolume(),
    marketCap: 6000000000,
    rank: 12,
    image: 'https://cryptologos.cc/logos/chainlink-link-logo.png',
  },
  {
    name: 'Uniswap',
    symbol: 'UNI',
    currentPrice: 5 + Math.random() * 1,
    priceChangePercentage24h: getRandomPriceChange(),
    volume24h: getRandomVolume(),
    marketCap: 4000000000,
    rank: 13,
    image: 'https://cryptologos.cc/logos/uniswap-uni-logo.png',
  },
  {
    name: 'Avalanche',
    symbol: 'AVAX',
    currentPrice: 20 + Math.random() * 5,
    priceChangePercentage24h: getRandomPriceChange(),
    volume24h: getRandomVolume(),
    marketCap: 7000000000,
    rank: 14,
    image: 'https://cryptologos.cc/logos/avalanche-avax-logo.png',
  },
  {
    name: 'Stellar',
    symbol: 'XLM',
    currentPrice: 0.1 + Math.random() * 0.02,
    priceChangePercentage24h: getRandomPriceChange(),
    volume24h: getRandomVolume(),
    marketCap: 3000000000,
    rank: 15,
    image: 'https://cryptologos.cc/logos/stellar-xlm-logo.png',
  },
];

// Generate more mock cryptocurrencies to have a total of 100
for (let i = mockCryptos.length; i < 100; i++) {
  const price = Math.random() * 10 + 0.1;
  mockCryptos.push({
    name: `Crypto ${i + 1}`,
    symbol: `CRY${i + 1}`,
    currentPrice: price,
    priceChangePercentage24h: getRandomPriceChange(),
    volume24h: getRandomVolume(),
    marketCap: price * Math.floor(Math.random() * 990000000 + 10000000),
    rank: i + 1,
    image: `https://via.placeholder.com/32/2196f3/ffffff?text=${i + 1}`,
  });
}

// Seed the database
async function seedDatabase() {
  try {
    // Clear existing data
    await Crypto.deleteMany({});
    console.log('Cleared existing data');
    
    // Insert new data
    await Crypto.insertMany(mockCryptos);
    console.log(`Inserted ${mockCryptos.length} cryptocurrencies`);
    
    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    
    console.log('Database seeding completed successfully');
  } catch (error) {
    console.error('Error seeding database:', error);
  }
}

// Run the seed function
seedDatabase();
