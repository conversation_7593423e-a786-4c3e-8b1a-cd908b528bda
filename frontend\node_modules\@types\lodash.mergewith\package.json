{"name": "@types/lodash.mergewith", "version": "4.6.9", "description": "TypeScript definitions for lodash.mergewith", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/lodash.mergewith", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "b<PERSON><PERSON><PERSON>", "url": "https://github.com/bczengel"}, {"name": "<PERSON><PERSON>", "githubUsername": "ch<PERSON>u", "url": "https://github.com/chrootsu"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/lodash.mergewith"}, "scripts": {}, "dependencies": {"@types/lodash": "*"}, "typesPublisherContentHash": "b84eeab81b7612c5fc07dba0c038cac02aa7db2648382baab5788b3c71391eb1", "typeScriptVersion": "4.5"}