/**
 * Cryptocurrency Routes
 * 
 * This file defines all routes related to cryptocurrency data.
 */

const express = require('express');
const router = express.Router();
const cryptoController = require('../controllers/cryptoController');
const { protect, admin } = require('../middleware/authMiddleware');

/**
 * @route   GET /api/cryptos
 * @desc    Get all cryptocurrencies with pagination and filters
 * @access  Public
 */
router.get('/', cryptoController.getCryptos);

/**
 * @route   GET /api/cryptos/trending
 * @desc    Get trending cryptocurrencies
 * @access  Public
 */
router.get('/trending', cryptoController.getTrendingCryptos);

/**
 * @route   GET /api/cryptos/global
 * @desc    Get global cryptocurrency market data
 * @access  Public
 */
router.get('/global', cryptoController.getGlobalMarketData);

/**
 * @route   GET /api/cryptos/search/:query
 * @desc    Search for cryptocurrencies
 * @access  Public
 */
router.get('/search/:query', cryptoController.searchCryptos);

/**
 * @route   GET /api/cryptos/:id
 * @desc    Get detailed data for a specific cryptocurrency
 * @access  Public
 */
router.get('/:id', cryptoController.getCryptoById);

/**
 * @route   POST /api/cryptos/clear-cache
 * @desc    Clear cache for CoinGecko API
 * @access  Admin
 */
router.post('/clear-cache', protect, admin, cryptoController.clearCache);

/**
 * @route   GET /api/cryptos/cache-stats
 * @desc    Get cache statistics
 * @access  Admin
 */
router.get('/cache-stats', protect, admin, cryptoController.getCacheStats);

module.exports = router;
