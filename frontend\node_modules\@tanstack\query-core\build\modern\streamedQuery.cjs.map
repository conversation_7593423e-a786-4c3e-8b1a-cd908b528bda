{"version": 3, "sources": ["../../src/streamedQuery.ts"], "sourcesContent": ["import type { QueryFunction, QueryFunctionContext, QueryKey } from './types'\n\n/**\n * This is a helper function to create a query function that streams data from an AsyncIterable.\n * Data will be an Array of all the chunks received.\n * The query will be in a 'pending' state until the first chunk of data is received, but will go to 'success' after that.\n * The query will stay in fetchStatus 'fetching' until the stream ends.\n * @param queryFn - The function that returns an AsyncIterable to stream data from.\n * @param refetchMode - Defaults to 'reset', which replaces data when a refetch happens. Set to 'append' to append new data to the existing data.\n */\nexport function streamedQuery<\n  TQueryFnData = unknown,\n  TQueryKey extends QueryKey = QueryKey,\n>({\n  queryFn,\n  refetchMode,\n}: {\n  queryFn: (\n    context: QueryFunctionContext<TQueryKey>,\n  ) => AsyncIterable<TQueryFnData> | Promise<AsyncIterable<TQueryFnData>>\n  refetchMode?: 'append' | 'reset'\n}): QueryFunction<Array<TQueryFnData>, TQueryKey> {\n  return async (context) => {\n    if (refetchMode !== 'append') {\n      const query = context.client\n        .getQueryCache()\n        .find({ queryKey: context.queryKey, exact: true })\n      if (query && query.state.data !== undefined) {\n        query.setState({\n          status: 'pending',\n          data: undefined,\n          error: null,\n          fetchStatus: 'fetching',\n        })\n      }\n    }\n    const stream = await queryFn(context)\n    for await (const chunk of stream) {\n      if (context.signal.aborted) {\n        break\n      }\n      context.client.setQueryData<Array<TQueryFnData>>(\n        context.queryKey,\n        (prev = []) => {\n          return prev.concat(chunk)\n        },\n      )\n    }\n    return context.client.getQueryData(context.queryKey)!\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAUO,SAAS,cAGd;AAAA,EACA;AAAA,EACA;AACF,GAKkD;AAChD,SAAO,OAAO,YAAY;AACxB,QAAI,gBAAgB,UAAU;AAC5B,YAAM,QAAQ,QAAQ,OACnB,cAAc,EACd,KAAK,EAAE,UAAU,QAAQ,UAAU,OAAO,KAAK,CAAC;AACnD,UAAI,SAAS,MAAM,MAAM,SAAS,QAAW;AAC3C,cAAM,SAAS;AAAA,UACb,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,QACf,CAAC;AAAA,MACH;AAAA,IACF;AACA,UAAM,SAAS,MAAM,QAAQ,OAAO;AACpC,qBAAiB,SAAS,QAAQ;AAChC,UAAI,QAAQ,OAAO,SAAS;AAC1B;AAAA,MACF;AACA,cAAQ,OAAO;AAAA,QACb,QAAQ;AAAA,QACR,CAAC,OAAO,CAAC,MAAM;AACb,iBAAO,KAAK,OAAO,KAAK;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AACA,WAAO,QAAQ,OAAO,aAAa,QAAQ,QAAQ;AAAA,EACrD;AACF;", "names": []}