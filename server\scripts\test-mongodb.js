/**
 * MongoDB Connection Test Script
 * 
 * This script tests the connection to MongoDB and displays the connection status.
 * Run with: node server/scripts/test-mongodb.js
 */

require('dotenv').config();
const mongoose = require('mongoose');
const connectDB = require('../config/db');

console.log('MongoDB Connection Test');
console.log('======================');
console.log(`MongoDB URI: ${process.env.MONGO_URI.replace(/\/\/([^:]+):([^@]+)@/, '//***:***@')}`);

// Connect to MongoDB
connectDB()
  .then((conn) => {
    console.log('\n✅ MongoDB Connection Successful!');
    console.log(`Connected to: ${conn.connection.host}`);
    console.log(`Database name: ${conn.connection.name}`);
    console.log(`Connection state: ${mongoose.STATES[conn.connection.readyState]}`);
    
    // List all collections
    return conn.connection.db.listCollections().toArray();
  })
  .then((collections) => {
    console.log('\nCollections in database:');
    if (collections.length === 0) {
      console.log('- No collections found (database is empty)');
    } else {
      collections.forEach((collection) => {
        console.log(`- ${collection.name}`);
      });
    }
    
    console.log('\nConnection test completed successfully.');
    process.exit(0);
  })
  .catch((err) => {
    console.error('\n❌ MongoDB Connection Failed!');
    console.error(`Error: ${err.message}`);
    
    // Provide more helpful error messages based on common issues
    if (err.message.includes('ECONNREFUSED')) {
      console.error('\nPossible causes:');
      console.error('- MongoDB server is not running');
      console.error('- Connection string has incorrect host or port');
    } else if (err.message.includes('Authentication failed')) {
      console.error('\nPossible causes:');
      console.error('- Username or password in connection string is incorrect');
      console.error('- User does not have access to the database');
    } else if (err.message.includes('ETIMEDOUT')) {
      console.error('\nPossible causes:');
      console.error('- Network connectivity issues');
      console.error('- Firewall blocking the connection');
      console.error('- MongoDB Atlas IP whitelist restrictions');
    }
    
    console.error('\nPlease check your connection string in the .env file and try again.');
    process.exit(1);
  });
