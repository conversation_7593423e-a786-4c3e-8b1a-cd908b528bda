require('dotenv').config();
const mongoose = require('mongoose');
const dns = require('dns');
const { promisify } = require('util');
const dnsLookup = promisify(dns.lookup);

// Get the MongoDB URI from environment variables
const MONGO_URI = process.env.MONGO_URI;

// Extract hostname from MongoDB URI
function extractHostname(uri) {
  try {
    const match = uri.match(/mongodb\+srv:\/\/[^@]+@([^\/\?]+)/);
    return match ? match[1] : null;
  } catch (err) {
    return null;
  }
}

// Test DNS resolution
async function testDNS(hostname) {
  console.log(`\nTesting DNS resolution for ${hostname}...`);
  try {
    const result = await dnsLookup(hostname);
    console.log(`✅ DNS resolution successful: ${hostname} -> ${result.address}`);
    return true;
  } catch (err) {
    console.error(`❌ DNS resolution failed: ${err.message}`);
    return false;
  }
}

// Test MongoDB connection with detailed error handling
async function testMongoDBConnection() {
  console.log('\n=== MongoDB Connection Test ===');
  console.log(`\nTesting MongoDB connection with URI: ${MONGO_URI?.replace(/\/\/([^:]+):([^@]+)@/, '//***:***@')}`);
  
  // Extract hostname for DNS test
  const hostname = extractHostname(MONGO_URI);
  if (hostname) {
    await testDNS(hostname);
  }
  
  // Connection options
  const options = {
    serverSelectionTimeoutMS: 10000, // 10 seconds
    socketTimeoutMS: 45000, // 45 seconds
    connectTimeoutMS: 10000, // 10 seconds
    maxPoolSize: 5,
    minPoolSize: 1,
  };
  
  try {
    console.log('\nAttempting to connect to MongoDB...');
    const startTime = Date.now();
    
    const conn = await mongoose.connect(MONGO_URI, options);
    
    const endTime = Date.now();
    const connectionTime = (endTime - startTime) / 1000;
    
    console.log(`\n✅ MongoDB Connected Successfully in ${connectionTime.toFixed(2)} seconds!`);
    console.log(`Connected to: ${conn.connection.host}`);
    console.log(`Database name: ${conn.connection.name}`);
    console.log(`Connection state: ${mongoose.connection.readyState}`);
    
    // Test creating a collection
    console.log('\nTesting database operations...');
    try {
      const db = conn.connection.db;
      const collections = await db.listCollections().toArray();
      console.log(`✅ Database operations successful. Found ${collections.length} collections.`);
      collections.forEach(collection => {
        console.log(`- ${collection.name}`);
      });
    } catch (err) {
      console.error(`❌ Database operations failed: ${err.message}`);
    }
    
    // Close the connection
    await mongoose.connection.close();
    console.log('\nConnection closed.');
    
    return true;
  } catch (err) {
    console.error(`\n❌ MongoDB Connection Failed: ${err.message}`);
    
    // Detailed error analysis
    if (err.name === 'MongoServerSelectionError') {
      console.error('\nServer Selection Error Details:');
      if (err.message.includes('getaddrinfo ENOTFOUND')) {
        console.error('- DNS resolution failed. The hostname could not be resolved.');
        console.error('- Check if the cluster name in your connection string is correct.');
      } else if (err.message.includes('connection timed out')) {
        console.error('- Connection timed out. The server did not respond in time.');
        console.error('- This could be due to network issues or firewall restrictions.');
      }
    } else if (err.name === 'MongoError' || err.name === 'MongoServerError') {
      if (err.message.includes('Authentication failed')) {
        console.error('\nAuthentication Error Details:');
        console.error('- Username or password is incorrect.');
        console.error('- Make sure the username and password in your connection string are correct.');
        console.error('- Check if the database user exists and has the correct permissions.');
      }
    }
    
    console.error('\nTroubleshooting Steps:');
    console.error('1. Verify the username and password in your connection string');
    console.error('2. Check if your IP address is whitelisted in MongoDB Atlas');
    console.error('3. Ensure the cluster name and database name are correct');
    console.error('4. Try connecting from a different network');
    console.error('5. Check MongoDB Atlas status page for any ongoing issues');
    
    return false;
  }
}

// Run the test
testMongoDBConnection()
  .then(success => {
    console.log(`\n=== Test ${success ? 'Passed' : 'Failed'} ===`);
    process.exit(success ? 0 : 1);
  })
  .catch(err => {
    console.error('Unexpected error during test:', err);
    process.exit(1);
  });
