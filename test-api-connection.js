/**
 * Simple script to test the API connection
 */

// Test the API connection
async function testApiConnection() {
  console.log('Testing API connection...');

  try {
    // Use the full URL to ensure we're testing the correct endpoint
    const response = await fetch('http://localhost:3001/api');

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const data = await response.json();
    console.log('API Response:', data);

    return {
      success: true,
      status: response.status,
      data
    };
  } catch (error) {
    console.error('API Connection Error:', error.message);

    return {
      success: false,
      error: error.message
    };
  }
}

// Test the cryptos endpoint
async function testCryptosEndpoint() {
  console.log('Testing cryptos endpoint...');

  try {
    const response = await fetch('http://localhost:3001/api/cryptos');

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const data = await response.json();
    console.log('Cryptos Response:', data);

    return {
      success: true,
      status: response.status,
      data
    };
  } catch (error) {
    console.error('Cryptos Endpoint Error:', error.message);

    return {
      success: false,
      error: error.message
    };
  }
}

// Test the trending endpoint
async function testTrendingEndpoint() {
  console.log('Testing trending endpoint...');

  try {
    const response = await fetch('http://localhost:3001/api/cryptos/trending');

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const data = await response.json();
    console.log('Trending Response:', data);

    return {
      success: true,
      status: response.status,
      data
    };
  } catch (error) {
    console.error('Trending Endpoint Error:', error.message);

    return {
      success: false,
      error: error.message
    };
  }
}

// Run all tests
async function runAllTests() {
  console.log('Running all API tests...');
  
  const apiResult = await testApiConnection();
  const cryptosResult = await testCryptosEndpoint();
  const trendingResult = await testTrendingEndpoint();
  
  console.log('Test Results:');
  console.log('API Connection:', apiResult.success ? 'SUCCESS' : 'FAILED');
  console.log('Cryptos Endpoint:', cryptosResult.success ? 'SUCCESS' : 'FAILED');
  console.log('Trending Endpoint:', trendingResult.success ? 'SUCCESS' : 'FAILED');
  
  return {
    api: apiResult,
    cryptos: cryptosResult,
    trending: trendingResult
  };
}

// Export the test functions
window.testApiConnection = {
  testApi: testApiConnection,
  testCryptos: testCryptosEndpoint,
  testTrending: testTrendingEndpoint,
  runAll: runAllTests
};

// Run tests automatically
runAllTests();
