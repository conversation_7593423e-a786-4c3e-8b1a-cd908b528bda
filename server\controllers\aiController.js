/**
 * AI Controller
 * 
 * This controller handles AI-powered features for cryptocurrency analysis,
 * including price predictions, trading signals, sentiment analysis,
 * and portfolio recommendations.
 */

const aiService = require('../services/aiService');
const enhancedCoinGeckoService = require('../services/enhancedCoinGeckoService');

/**
 * Get price prediction for a cryptocurrency
 * @route GET /api/ai/price-prediction/:id
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} - JSON response with price prediction
 */
const getPricePrediction = async (req, res) => {
  try {
    const { id } = req.params;
    const { timeframe } = req.query;
    
    // Get cryptocurrency data
    const crypto = await enhancedCoinGeckoService.getCoinDetails(id);
    
    // Format crypto data for AI service
    const formattedCrypto = {
      _id: crypto.id,
      name: crypto.name,
      symbol: crypto.symbol,
      currentPrice: crypto.market_data?.current_price?.usd || 0,
      priceChangePercentage24h: crypto.market_data?.price_change_percentage_24h || 0,
    };
    
    // Generate price prediction
    const prediction = aiService.generatePricePrediction(formattedCrypto, timeframe);
    
    return res.json({
      success: true,
      data: prediction,
    });
  } catch (error) {
    console.error(`Error in getPricePrediction controller for ${req.params.id}:`, error);
    return res.status(500).json({
      success: false,
      message: 'Failed to generate price prediction',
      error: error.message,
    });
  }
};

/**
 * Get trading signals for a cryptocurrency
 * @route GET /api/ai/trading-signals/:id
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} - JSON response with trading signals
 */
const getTradingSignals = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Get cryptocurrency data
    const crypto = await enhancedCoinGeckoService.getCoinDetails(id);
    
    // Get market chart data for historical prices
    const marketChart = await enhancedCoinGeckoService.getCoinMarketChart(id, {
      days: '30',
      interval: 'daily',
    });
    
    // Extract prices from market chart
    const prices = marketChart.prices.map(price => price[1]);
    
    // Format crypto data for AI service
    const formattedCrypto = {
      _id: crypto.id,
      name: crypto.name,
      symbol: crypto.symbol,
      currentPrice: crypto.market_data?.current_price?.usd || 0,
      priceChangePercentage24h: crypto.market_data?.price_change_percentage_24h || 0,
    };
    
    // Generate trading signals
    const signals = aiService.generateTradingSignals(formattedCrypto, prices);
    
    return res.json({
      success: true,
      data: signals,
    });
  } catch (error) {
    console.error(`Error in getTradingSignals controller for ${req.params.id}:`, error);
    return res.status(500).json({
      success: false,
      message: 'Failed to generate trading signals',
      error: error.message,
    });
  }
};

/**
 * Get sentiment analysis for a cryptocurrency
 * @route GET /api/ai/sentiment/:id
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} - JSON response with sentiment analysis
 */
const getSentimentAnalysis = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Get cryptocurrency data
    const crypto = await enhancedCoinGeckoService.getCoinDetails(id);
    
    // Format crypto data for AI service
    const formattedCrypto = {
      _id: crypto.id,
      name: crypto.name,
      symbol: crypto.symbol,
      currentPrice: crypto.market_data?.current_price?.usd || 0,
      priceChangePercentage24h: crypto.market_data?.price_change_percentage_24h || 0,
    };
    
    // Generate sentiment analysis
    const sentiment = aiService.generateSentimentAnalysis(formattedCrypto);
    
    return res.json({
      success: true,
      data: sentiment,
    });
  } catch (error) {
    console.error(`Error in getSentimentAnalysis controller for ${req.params.id}:`, error);
    return res.status(500).json({
      success: false,
      message: 'Failed to generate sentiment analysis',
      error: error.message,
    });
  }
};

/**
 * Get portfolio recommendations
 * @route GET /api/ai/portfolio-recommendations
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} - JSON response with portfolio recommendations
 */
const getPortfolioRecommendations = async (req, res) => {
  try {
    const { riskTolerance } = req.query;
    
    // Get top 100 cryptocurrencies
    const cryptos = await enhancedCoinGeckoService.getCoinsMarkets({
      perPage: 100,
      page: 1,
    });
    
    // Format crypto data for AI service
    const formattedCryptos = cryptos.map(crypto => ({
      _id: crypto.id,
      name: crypto.name,
      symbol: crypto.symbol,
      currentPrice: crypto.current_price,
      marketCap: crypto.market_cap,
      priceChangePercentage24h: crypto.price_change_percentage_24h,
    }));
    
    // Generate portfolio recommendations
    const recommendations = aiService.generatePortfolioRecommendations(formattedCryptos, riskTolerance);
    
    return res.json({
      success: true,
      data: recommendations,
    });
  } catch (error) {
    console.error('Error in getPortfolioRecommendations controller:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to generate portfolio recommendations',
      error: error.message,
    });
  }
};

/**
 * Get AI insights dashboard data
 * @route GET /api/ai/dashboard
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} - JSON response with AI insights dashboard data
 */
const getAiDashboard = async (req, res) => {
  try {
    // Get top 10 cryptocurrencies
    const cryptos = await enhancedCoinGeckoService.getCoinsMarkets({
      perPage: 10,
      page: 1,
    });
    
    // Format crypto data for AI service
    const formattedCryptos = cryptos.map(crypto => ({
      _id: crypto.id,
      name: crypto.name,
      symbol: crypto.symbol,
      currentPrice: crypto.current_price,
      marketCap: crypto.market_cap,
      priceChangePercentage24h: crypto.price_change_percentage_24h,
    }));
    
    // Generate insights for top 3 cryptocurrencies
    const insights = [];
    
    for (let i = 0; i < 3; i++) {
      const crypto = formattedCryptos[i];
      
      // Generate price prediction
      const prediction = aiService.generatePricePrediction(crypto);
      
      // Generate trading signals
      const signals = aiService.generateTradingSignals(crypto);
      
      // Generate sentiment analysis
      const sentiment = aiService.generateSentimentAnalysis(crypto);
      
      insights.push({
        crypto,
        prediction,
        signals,
        sentiment,
      });
    }
    
    // Generate portfolio recommendations
    const recommendations = aiService.generatePortfolioRecommendations(formattedCryptos, 'moderate');
    
    return res.json({
      success: true,
      data: {
        insights,
        recommendations,
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error('Error in getAiDashboard controller:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to generate AI insights dashboard',
      error: error.message,
    });
  }
};

module.exports = {
  getPricePrediction,
  getTradingSignals,
  getSentimentAnalysis,
  getPortfolioRecommendations,
  getAiDashboard,
};
