export declare const accordionAnatomy: import("./create-anatomy").AnatomyInstance<"container" | "root" | "button" | "panel" | "icon">;
export declare const alertAnatomy: import("./create-anatomy").AnatomyInstance<"container" | "icon" | "title" | "description" | "spinner">;
export declare const avatarAnatomy: import("./create-anatomy").AnatomyInstance<"container" | "label" | "badge" | "excessLabel" | "group">;
export declare const breadcrumbAnatomy: import("./create-anatomy").AnatomyInstance<"container" | "link" | "item" | "separator">;
export declare const buttonAnatomy: import("./create-anatomy").AnatomyInstance<string>;
export declare const checkboxAnatomy: import("./create-anatomy").AnatomyInstance<"container" | "icon" | "label" | "control">;
export declare const circularProgressAnatomy: import("./create-anatomy").AnatomyInstance<"label" | "track" | "filledTrack">;
export declare const drawerAnatomy: import("./create-anatomy").AnatomyInstance<"overlay" | "dialogContainer" | "dialog" | "header" | "closeButton" | "body" | "footer">;
export declare const editableAnatomy: import("./create-anatomy").AnatomyInstance<"preview" | "input" | "textarea">;
export declare const formAnatomy: import("./create-anatomy").AnatomyInstance<"container" | "requiredIndicator" | "helperText">;
export declare const formErrorAnatomy: import("./create-anatomy").AnatomyInstance<"icon" | "text">;
export declare const inputAnatomy: import("./create-anatomy").AnatomyInstance<"group" | "addon" | "field" | "element">;
export declare const listAnatomy: import("./create-anatomy").AnatomyInstance<"container" | "icon" | "item">;
export declare const menuAnatomy: import("./create-anatomy").AnatomyInstance<"button" | "icon" | "item" | "list" | "groupTitle" | "command" | "divider">;
export declare const modalAnatomy: import("./create-anatomy").AnatomyInstance<"overlay" | "dialogContainer" | "dialog" | "header" | "closeButton" | "body" | "footer">;
export declare const numberInputAnatomy: import("./create-anatomy").AnatomyInstance<"root" | "field" | "stepperGroup" | "stepper">;
export declare const pinInputAnatomy: import("./create-anatomy").AnatomyInstance<"field">;
export declare const popoverAnatomy: import("./create-anatomy").AnatomyInstance<"header" | "closeButton" | "body" | "footer" | "content" | "popper" | "arrow">;
export declare const progressAnatomy: import("./create-anatomy").AnatomyInstance<"label" | "track" | "filledTrack">;
export declare const radioAnatomy: import("./create-anatomy").AnatomyInstance<"container" | "label" | "control">;
export declare const selectAnatomy: import("./create-anatomy").AnatomyInstance<"icon" | "field">;
export declare const sliderAnatomy: import("./create-anatomy").AnatomyInstance<"container" | "track" | "filledTrack" | "thumb" | "mark">;
export declare const statAnatomy: import("./create-anatomy").AnatomyInstance<"number" | "container" | "icon" | "label" | "helpText">;
export declare const switchAnatomy: import("./create-anatomy").AnatomyInstance<"container" | "label" | "track" | "thumb">;
export declare const tableAnatomy: import("./create-anatomy").AnatomyInstance<"table" | "thead" | "tbody" | "tr" | "th" | "td" | "tfoot" | "caption">;
export declare const tabsAnatomy: import("./create-anatomy").AnatomyInstance<"root" | "tab" | "tablist" | "tabpanel" | "tabpanels" | "indicator">;
/**
 * **Tag anatomy**
 * - Container: the container for the tag
 * - Label: the text content of the tag
 * - closeButton: the close button for the tag
 */
export declare const tagAnatomy: import("./create-anatomy").AnatomyInstance<"container" | "label" | "closeButton">;
export declare const cardAnatomy: import("./create-anatomy").AnatomyInstance<"container" | "header" | "body" | "footer">;
export declare const stepperAnatomy: import("./create-anatomy").AnatomyInstance<"number" | "icon" | "title" | "description" | "separator" | "stepper" | "indicator" | "step">;
