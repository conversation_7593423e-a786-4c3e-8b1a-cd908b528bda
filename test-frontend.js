const http = require('http');

const testFrontend = () => {
  const options = {
    hostname: 'localhost',
    port: 5173,
    path: '/',
    method: 'GET'
  };

  const req = http.request(options, (res) => {
    console.log(`Frontend Status: ${res.statusCode}`);
    console.log(`Headers:`, res.headers);
    
    if (res.statusCode === 200) {
      console.log('✅ Frontend is running successfully!');
      console.log('🌐 Access your application at: http://localhost:5173');
    } else {
      console.log('❌ Frontend returned status:', res.statusCode);
    }
  });

  req.on('error', (e) => {
    console.error('❌ Frontend connection failed:', e.message);
    console.log('Make sure the frontend development server is running');
  });

  req.end();
};

console.log('Testing frontend connection...');
testFrontend();
