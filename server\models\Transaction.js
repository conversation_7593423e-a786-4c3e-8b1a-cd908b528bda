/**
 * Transaction Model
 * 
 * This model defines the schema for portfolio transactions in MongoDB.
 */

const mongoose = require('mongoose');

const transactionSchema = new mongoose.Schema(
  {
    portfolio: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Portfolio',
      required: true,
    },
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    cryptoId: {
      type: String,
      required: [true, 'Cryptocurrency ID is required'],
    },
    cryptoName: {
      type: String,
      required: [true, 'Cryptocurrency name is required'],
    },
    cryptoSymbol: {
      type: String,
      required: [true, 'Cryptocurrency symbol is required'],
    },
    type: {
      type: String,
      enum: ['buy', 'sell'],
      required: [true, 'Transaction type is required'],
    },
    quantity: {
      type: Number,
      required: [true, 'Quantity is required'],
      min: [0, 'Quantity must be greater than 0'],
    },
    price: {
      type: Number,
      required: [true, 'Price is required'],
      min: [0, 'Price must be greater than 0'],
    },
    total: {
      type: Number,
      required: [true, 'Total is required'],
    },
    fee: {
      type: Number,
      default: 0,
    },
    date: {
      type: Date,
      default: Date.now,
    },
    notes: {
      type: String,
      trim: true,
      maxlength: [500, 'Notes cannot exceed 500 characters'],
      default: '',
    },
  },
  {
    timestamps: true,
  }
);

// Create indexes for faster queries
transactionSchema.index({ portfolio: 1 });
transactionSchema.index({ user: 1 });
transactionSchema.index({ cryptoId: 1 });
transactionSchema.index({ date: -1 });

const Transaction = mongoose.model('Transaction', transactionSchema);

module.exports = Transaction;
