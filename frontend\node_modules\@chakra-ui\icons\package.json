{"name": "@chakra-ui/icons", "version": "2.2.4", "description": "Chakra UI icons", "author": "<PERSON><PERSON> <<EMAIL>>", "homepage": "https://github.com/chakra-ui/chakra-ui#readme", "license": "MIT", "main": "dist/cjs/index.cjs", "module": "dist/esm/index.mjs", "types": "dist/types/index.d.ts", "sideEffects": false, "files": ["dist"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/chakra-ui/chakra-ui.git", "directory": "packages/components/icons"}, "bugs": {"url": "https://github.com/chakra-ui/chakra-ui/issues"}, "peerDependencies": {"@chakra-ui/react": ">=2.0.0", "react": ">=18"}, "devDependencies": {"@types/react": "^18.3.11", "react": "^18.2.0", "@chakra-ui/react": "2.10.1"}, "exports": {".": {"import": {"types": "./dist/types/index.d.ts", "default": "./dist/esm/index.mjs"}, "require": {"types": "./dist/types/index.d.ts", "default": "./dist/cjs/index.cjs"}}, "./Add": {"import": {"types": "./dist/types/Add.d.ts", "default": "./dist/esm/Add.mjs"}, "require": {"types": "./dist/types/Add.d.ts", "default": "./dist/cjs/Add.cjs"}}, "./ArrowBack": {"import": {"types": "./dist/types/ArrowBack.d.ts", "default": "./dist/esm/ArrowBack.mjs"}, "require": {"types": "./dist/types/ArrowBack.d.ts", "default": "./dist/cjs/ArrowBack.cjs"}}, "./ArrowDown": {"import": {"types": "./dist/types/ArrowDown.d.ts", "default": "./dist/esm/ArrowDown.mjs"}, "require": {"types": "./dist/types/ArrowDown.d.ts", "default": "./dist/cjs/ArrowDown.cjs"}}, "./ArrowForward": {"import": {"types": "./dist/types/ArrowForward.d.ts", "default": "./dist/esm/ArrowForward.mjs"}, "require": {"types": "./dist/types/ArrowForward.d.ts", "default": "./dist/cjs/ArrowForward.cjs"}}, "./ArrowLeft": {"import": {"types": "./dist/types/ArrowLeft.d.ts", "default": "./dist/esm/ArrowLeft.mjs"}, "require": {"types": "./dist/types/ArrowLeft.d.ts", "default": "./dist/cjs/ArrowLeft.cjs"}}, "./ArrowRight": {"import": {"types": "./dist/types/ArrowRight.d.ts", "default": "./dist/esm/ArrowRight.mjs"}, "require": {"types": "./dist/types/ArrowRight.d.ts", "default": "./dist/cjs/ArrowRight.cjs"}}, "./ArrowUp": {"import": {"types": "./dist/types/ArrowUp.d.ts", "default": "./dist/esm/ArrowUp.mjs"}, "require": {"types": "./dist/types/ArrowUp.d.ts", "default": "./dist/cjs/ArrowUp.cjs"}}, "./ArrowUpDown": {"import": {"types": "./dist/types/ArrowUpDown.d.ts", "default": "./dist/esm/ArrowUpDown.mjs"}, "require": {"types": "./dist/types/ArrowUpDown.d.ts", "default": "./dist/cjs/ArrowUpDown.cjs"}}, "./AtSign": {"import": {"types": "./dist/types/AtSign.d.ts", "default": "./dist/esm/AtSign.mjs"}, "require": {"types": "./dist/types/AtSign.d.ts", "default": "./dist/cjs/AtSign.cjs"}}, "./Attachment": {"import": {"types": "./dist/types/Attachment.d.ts", "default": "./dist/esm/Attachment.mjs"}, "require": {"types": "./dist/types/Attachment.d.ts", "default": "./dist/cjs/Attachment.cjs"}}, "./Bell": {"import": {"types": "./dist/types/Bell.d.ts", "default": "./dist/esm/Bell.mjs"}, "require": {"types": "./dist/types/Bell.d.ts", "default": "./dist/cjs/Bell.cjs"}}, "./Calendar": {"import": {"types": "./dist/types/Calendar.d.ts", "default": "./dist/esm/Calendar.mjs"}, "require": {"types": "./dist/types/Calendar.d.ts", "default": "./dist/cjs/Calendar.cjs"}}, "./Chat": {"import": {"types": "./dist/types/Chat.d.ts", "default": "./dist/esm/Chat.mjs"}, "require": {"types": "./dist/types/Chat.d.ts", "default": "./dist/cjs/Chat.cjs"}}, "./Check": {"import": {"types": "./dist/types/Check.d.ts", "default": "./dist/esm/Check.mjs"}, "require": {"types": "./dist/types/Check.d.ts", "default": "./dist/cjs/Check.cjs"}}, "./CheckCircle": {"import": {"types": "./dist/types/CheckCircle.d.ts", "default": "./dist/esm/CheckCircle.mjs"}, "require": {"types": "./dist/types/CheckCircle.d.ts", "default": "./dist/cjs/CheckCircle.cjs"}}, "./ChevronDown": {"import": {"types": "./dist/types/ChevronDown.d.ts", "default": "./dist/esm/ChevronDown.mjs"}, "require": {"types": "./dist/types/ChevronDown.d.ts", "default": "./dist/cjs/ChevronDown.cjs"}}, "./ChevronLeft": {"import": {"types": "./dist/types/ChevronLeft.d.ts", "default": "./dist/esm/ChevronLeft.mjs"}, "require": {"types": "./dist/types/ChevronLeft.d.ts", "default": "./dist/cjs/ChevronLeft.cjs"}}, "./ChevronRight": {"import": {"types": "./dist/types/ChevronRight.d.ts", "default": "./dist/esm/ChevronRight.mjs"}, "require": {"types": "./dist/types/ChevronRight.d.ts", "default": "./dist/cjs/ChevronRight.cjs"}}, "./ChevronUp": {"import": {"types": "./dist/types/ChevronUp.d.ts", "default": "./dist/esm/ChevronUp.mjs"}, "require": {"types": "./dist/types/ChevronUp.d.ts", "default": "./dist/cjs/ChevronUp.cjs"}}, "./Close": {"import": {"types": "./dist/types/Close.d.ts", "default": "./dist/esm/Close.mjs"}, "require": {"types": "./dist/types/Close.d.ts", "default": "./dist/cjs/Close.cjs"}}, "./Copy": {"import": {"types": "./dist/types/Copy.d.ts", "default": "./dist/esm/Copy.mjs"}, "require": {"types": "./dist/types/Copy.d.ts", "default": "./dist/cjs/Copy.cjs"}}, "./Delete": {"import": {"types": "./dist/types/Delete.d.ts", "default": "./dist/esm/Delete.mjs"}, "require": {"types": "./dist/types/Delete.d.ts", "default": "./dist/cjs/Delete.cjs"}}, "./Download": {"import": {"types": "./dist/types/Download.d.ts", "default": "./dist/esm/Download.mjs"}, "require": {"types": "./dist/types/Download.d.ts", "default": "./dist/cjs/Download.cjs"}}, "./DragHandle": {"import": {"types": "./dist/types/DragHandle.d.ts", "default": "./dist/esm/DragHandle.mjs"}, "require": {"types": "./dist/types/DragHandle.d.ts", "default": "./dist/cjs/DragHandle.cjs"}}, "./Edit": {"import": {"types": "./dist/types/Edit.d.ts", "default": "./dist/esm/Edit.mjs"}, "require": {"types": "./dist/types/Edit.d.ts", "default": "./dist/cjs/Edit.cjs"}}, "./Email": {"import": {"types": "./dist/types/Email.d.ts", "default": "./dist/esm/Email.mjs"}, "require": {"types": "./dist/types/Email.d.ts", "default": "./dist/cjs/Email.cjs"}}, "./ExternalLink": {"import": {"types": "./dist/types/ExternalLink.d.ts", "default": "./dist/esm/ExternalLink.mjs"}, "require": {"types": "./dist/types/ExternalLink.d.ts", "default": "./dist/cjs/ExternalLink.cjs"}}, "./Hamburger": {"import": {"types": "./dist/types/Hamburger.d.ts", "default": "./dist/esm/Hamburger.mjs"}, "require": {"types": "./dist/types/Hamburger.d.ts", "default": "./dist/cjs/Hamburger.cjs"}}, "./Info": {"import": {"types": "./dist/types/Info.d.ts", "default": "./dist/esm/Info.mjs"}, "require": {"types": "./dist/types/Info.d.ts", "default": "./dist/cjs/Info.cjs"}}, "./InfoOutline": {"import": {"types": "./dist/types/InfoOutline.d.ts", "default": "./dist/esm/InfoOutline.mjs"}, "require": {"types": "./dist/types/InfoOutline.d.ts", "default": "./dist/cjs/InfoOutline.cjs"}}, "./Link": {"import": {"types": "./dist/types/Link.d.ts", "default": "./dist/esm/Link.mjs"}, "require": {"types": "./dist/types/Link.d.ts", "default": "./dist/cjs/Link.cjs"}}, "./Lock": {"import": {"types": "./dist/types/Lock.d.ts", "default": "./dist/esm/Lock.mjs"}, "require": {"types": "./dist/types/Lock.d.ts", "default": "./dist/cjs/Lock.cjs"}}, "./Minus": {"import": {"types": "./dist/types/Minus.d.ts", "default": "./dist/esm/Minus.mjs"}, "require": {"types": "./dist/types/Minus.d.ts", "default": "./dist/cjs/Minus.cjs"}}, "./Moon": {"import": {"types": "./dist/types/Moon.d.ts", "default": "./dist/esm/Moon.mjs"}, "require": {"types": "./dist/types/Moon.d.ts", "default": "./dist/cjs/Moon.cjs"}}, "./NotAllowed": {"import": {"types": "./dist/types/NotAllowed.d.ts", "default": "./dist/esm/NotAllowed.mjs"}, "require": {"types": "./dist/types/NotAllowed.d.ts", "default": "./dist/cjs/NotAllowed.cjs"}}, "./Phone": {"import": {"types": "./dist/types/Phone.d.ts", "default": "./dist/esm/Phone.mjs"}, "require": {"types": "./dist/types/Phone.d.ts", "default": "./dist/cjs/Phone.cjs"}}, "./PlusSquare": {"import": {"types": "./dist/types/PlusSquare.d.ts", "default": "./dist/esm/PlusSquare.mjs"}, "require": {"types": "./dist/types/PlusSquare.d.ts", "default": "./dist/cjs/PlusSquare.cjs"}}, "./Question": {"import": {"types": "./dist/types/Question.d.ts", "default": "./dist/esm/Question.mjs"}, "require": {"types": "./dist/types/Question.d.ts", "default": "./dist/cjs/Question.cjs"}}, "./QuestionOutline": {"import": {"types": "./dist/types/QuestionOutline.d.ts", "default": "./dist/esm/QuestionOutline.mjs"}, "require": {"types": "./dist/types/QuestionOutline.d.ts", "default": "./dist/cjs/QuestionOutline.cjs"}}, "./React": {"import": {"types": "./dist/types/React.d.ts", "default": "./dist/esm/React.mjs"}, "require": {"types": "./dist/types/React.d.ts", "default": "./dist/cjs/React.cjs"}}, "./Repeat": {"import": {"types": "./dist/types/Repeat.d.ts", "default": "./dist/esm/Repeat.mjs"}, "require": {"types": "./dist/types/Repeat.d.ts", "default": "./dist/cjs/Repeat.cjs"}}, "./RepeatClock": {"import": {"types": "./dist/types/RepeatClock.d.ts", "default": "./dist/esm/RepeatClock.mjs"}, "require": {"types": "./dist/types/RepeatClock.d.ts", "default": "./dist/cjs/RepeatClock.cjs"}}, "./Search": {"import": {"types": "./dist/types/Search.d.ts", "default": "./dist/esm/Search.mjs"}, "require": {"types": "./dist/types/Search.d.ts", "default": "./dist/cjs/Search.cjs"}}, "./Search2": {"import": {"types": "./dist/types/Search2.d.ts", "default": "./dist/esm/Search2.mjs"}, "require": {"types": "./dist/types/Search2.d.ts", "default": "./dist/cjs/Search2.cjs"}}, "./Settings": {"import": {"types": "./dist/types/Settings.d.ts", "default": "./dist/esm/Settings.mjs"}, "require": {"types": "./dist/types/Settings.d.ts", "default": "./dist/cjs/Settings.cjs"}}, "./SmallAdd": {"import": {"types": "./dist/types/SmallAdd.d.ts", "default": "./dist/esm/SmallAdd.mjs"}, "require": {"types": "./dist/types/SmallAdd.d.ts", "default": "./dist/cjs/SmallAdd.cjs"}}, "./SmallClose": {"import": {"types": "./dist/types/SmallClose.d.ts", "default": "./dist/esm/SmallClose.mjs"}, "require": {"types": "./dist/types/SmallClose.d.ts", "default": "./dist/cjs/SmallClose.cjs"}}, "./Spinner": {"import": {"types": "./dist/types/Spinner.d.ts", "default": "./dist/esm/Spinner.mjs"}, "require": {"types": "./dist/types/Spinner.d.ts", "default": "./dist/cjs/Spinner.cjs"}}, "./Star": {"import": {"types": "./dist/types/Star.d.ts", "default": "./dist/esm/Star.mjs"}, "require": {"types": "./dist/types/Star.d.ts", "default": "./dist/cjs/Star.cjs"}}, "./Sun": {"import": {"types": "./dist/types/Sun.d.ts", "default": "./dist/esm/Sun.mjs"}, "require": {"types": "./dist/types/Sun.d.ts", "default": "./dist/cjs/Sun.cjs"}}, "./Time": {"import": {"types": "./dist/types/Time.d.ts", "default": "./dist/esm/Time.mjs"}, "require": {"types": "./dist/types/Time.d.ts", "default": "./dist/cjs/Time.cjs"}}, "./TriangleDown": {"import": {"types": "./dist/types/TriangleDown.d.ts", "default": "./dist/esm/TriangleDown.mjs"}, "require": {"types": "./dist/types/TriangleDown.d.ts", "default": "./dist/cjs/TriangleDown.cjs"}}, "./TriangleUp": {"import": {"types": "./dist/types/TriangleUp.d.ts", "default": "./dist/esm/TriangleUp.mjs"}, "require": {"types": "./dist/types/TriangleUp.d.ts", "default": "./dist/cjs/TriangleUp.cjs"}}, "./Unlock": {"import": {"types": "./dist/types/Unlock.d.ts", "default": "./dist/esm/Unlock.mjs"}, "require": {"types": "./dist/types/Unlock.d.ts", "default": "./dist/cjs/Unlock.cjs"}}, "./UpDown": {"import": {"types": "./dist/types/UpDown.d.ts", "default": "./dist/esm/UpDown.mjs"}, "require": {"types": "./dist/types/UpDown.d.ts", "default": "./dist/cjs/UpDown.cjs"}}, "./View": {"import": {"types": "./dist/types/View.d.ts", "default": "./dist/esm/View.mjs"}, "require": {"types": "./dist/types/View.d.ts", "default": "./dist/cjs/View.cjs"}}, "./ViewOff": {"import": {"types": "./dist/types/ViewOff.d.ts", "default": "./dist/esm/ViewOff.mjs"}, "require": {"types": "./dist/types/ViewOff.d.ts", "default": "./dist/cjs/ViewOff.cjs"}}, "./Warning": {"import": {"types": "./dist/types/Warning.d.ts", "default": "./dist/esm/Warning.mjs"}, "require": {"types": "./dist/types/Warning.d.ts", "default": "./dist/cjs/Warning.cjs"}}, "./WarningTwo": {"import": {"types": "./dist/types/WarningTwo.d.ts", "default": "./dist/esm/WarningTwo.mjs"}, "require": {"types": "./dist/types/WarningTwo.d.ts", "default": "./dist/cjs/WarningTwo.cjs"}}, "./package.json": "./package.json"}, "scripts": {"build": "tsup src --dts", "dev": "pnpm build:fast --watch", "clean": "rimraf dist .turbo", "typecheck": "tsc --noEmit", "build:fast": "tsup src", "typedocs": "tsx ../../../scripts/generate-type-docs.ts"}}