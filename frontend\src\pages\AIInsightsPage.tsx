import React, { useState } from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  Flex,
  Select,
  Button,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  SimpleGrid,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  useColorModeValue,
  Divider,
  Icon,
} from '@chakra-ui/react';
import { FaRobot, FaChartLine, FaSignal, FaNewspaper, FaBriefcase } from 'react-icons/fa';
import { useCryptos } from '../hooks/useCryptos';
import PricePredictionChart from '../components/ai/PricePredictionChart';
import TradingSignals from '../components/ai/TradingSignals';
import SentimentAnalysis from '../components/ai/SentimentAnalysis';
import PortfolioRecommendations from '../components/ai/PortfolioRecommendations';
import ApiHealthCheck from '../components/ui/ApiHealthCheck';

const AIInsightsPage: React.FC = () => {
  const [selectedCryptoId, setSelectedCryptoId] = useState<string>('1'); // Default to Bitcoin
  
  // Fetch crypto data
  const { data: cryptosData, isLoading, error } = useCryptos();
  const cryptos = cryptosData?.data?.cryptos || [];
  
  // Get selected crypto
  const selectedCrypto = cryptos.find(crypto => crypto._id === selectedCryptoId);
  
  return (
    <Container maxW="container.xl" py={8}>
      <Box mb={8}>
        <Flex align="center" mb={2}>
          <Icon as={FaRobot} mr={2} boxSize={6} color="brand.500" />
          <Heading as="h1" size="xl">
            AI Crypto Insights
          </Heading>
        </Flex>
        <Text color={useColorModeValue('gray.600', 'gray.400')}>
          Advanced AI-powered analytics and predictions for cryptocurrency markets
        </Text>
      </Box>
      
      {/* API Health Check */}
      <Box mb={6}>
        <ApiHealthCheck showDetails={true} />
      </Box>
      
      {/* Crypto Selector */}
      <Flex 
        mb={6} 
        direction={{ base: 'column', md: 'row' }} 
        align={{ base: 'stretch', md: 'center' }}
        gap={4}
      >
        <Box flex="1">
          <Text mb={2} fontWeight="medium">Select Cryptocurrency</Text>
          <Select
            value={selectedCryptoId}
            onChange={(e) => setSelectedCryptoId(e.target.value)}
            isDisabled={isLoading}
          >
            {cryptos.map(crypto => (
              <option key={crypto._id} value={crypto._id}>
                {crypto.name} ({crypto.symbol})
              </option>
            ))}
          </Select>
        </Box>
        
        <Box>
          <Text mb={2} fontWeight="medium">Risk Tolerance</Text>
          <Tabs variant="soft-rounded" colorScheme="brand" size="sm">
            <TabList>
              <Tab>Conservative</Tab>
              <Tab>Moderate</Tab>
              <Tab>Aggressive</Tab>
            </TabList>
          </Tabs>
        </Box>
      </Flex>
      
      {error ? (
        <Alert status="error" mb={6} borderRadius="md">
          <AlertIcon />
          <AlertTitle>Error loading data</AlertTitle>
          <AlertDescription>
            {error instanceof Error ? error.message : 'An unknown error occurred'}
          </AlertDescription>
        </Alert>
      ) : null}
      
      {/* Main Content */}
      {selectedCrypto && (
        <Tabs colorScheme="brand" isLazy>
          <TabList>
            <Tab>
              <Flex align="center">
                <Icon as={FaChartLine} mr={2} />
                <Text>Price Predictions</Text>
              </Flex>
            </Tab>
            <Tab>
              <Flex align="center">
                <Icon as={FaSignal} mr={2} />
                <Text>Trading Signals</Text>
              </Flex>
            </Tab>
            <Tab>
              <Flex align="center">
                <Icon as={FaNewspaper} mr={2} />
                <Text>Sentiment Analysis</Text>
              </Flex>
            </Tab>
            <Tab>
              <Flex align="center">
                <Icon as={FaBriefcase} mr={2} />
                <Text>Portfolio</Text>
              </Flex>
            </Tab>
          </TabList>
          
          <TabPanels>
            {/* Price Predictions Tab */}
            <TabPanel>
              <Box mb={6}>
                <Heading size="md" mb={4}>
                  AI Price Predictions for {selectedCrypto.name}
                </Heading>
                <Text mb={4}>
                  Our advanced AI models analyze historical price data, market trends, and various technical 
                  indicators to generate price predictions for the next 7 days. These predictions are updated 
                  daily and should be used as one of many tools in your investment decision-making process.
                </Text>
                <PricePredictionChart 
                  cryptoId={selectedCrypto._id} 
                  symbol={selectedCrypto.symbol} 
                />
              </Box>
              
              <Alert status="info" borderRadius="md">
                <AlertIcon />
                <Box>
                  <AlertTitle>Disclaimer</AlertTitle>
                  <AlertDescription>
                    Price predictions are generated by AI models and should not be considered financial advice. 
                    Cryptocurrency markets are highly volatile and past performance is not indicative of future results.
                  </AlertDescription>
                </Box>
              </Alert>
            </TabPanel>
            
            {/* Trading Signals Tab */}
            <TabPanel>
              <Box mb={6}>
                <Heading size="md" mb={4}>
                  Trading Signals for {selectedCrypto.name}
                </Heading>
                <Text mb={4}>
                  Our AI analyzes multiple technical indicators to generate trading signals. These signals 
                  can help identify potential entry and exit points based on technical analysis principles.
                </Text>
                <TradingSignals 
                  cryptoId={selectedCrypto._id} 
                  symbol={selectedCrypto.symbol} 
                />
              </Box>
              
              <Alert status="info" borderRadius="md">
                <AlertIcon />
                <Box>
                  <AlertTitle>Disclaimer</AlertTitle>
                  <AlertDescription>
                    Trading signals are generated by AI models based on technical analysis and should not be 
                    considered financial advice. Always conduct your own research before making investment decisions.
                  </AlertDescription>
                </Box>
              </Alert>
            </TabPanel>
            
            {/* Sentiment Analysis Tab */}
            <TabPanel>
              <Box mb={6}>
                <Heading size="md" mb={4}>
                  Market Sentiment for {selectedCrypto.name}
                </Heading>
                <Text mb={4}>
                  Our AI analyzes news articles, social media posts, and forum discussions to gauge the 
                  overall market sentiment for {selectedCrypto.name}. This can provide valuable insights 
                  into market psychology and potential price movements.
                </Text>
                <SentimentAnalysis 
                  cryptoId={selectedCrypto._id} 
                  symbol={selectedCrypto.symbol} 
                />
              </Box>
              
              <Alert status="info" borderRadius="md">
                <AlertIcon />
                <Box>
                  <AlertTitle>Disclaimer</AlertTitle>
                  <AlertDescription>
                    Sentiment analysis is based on AI processing of public information sources and may not 
                    reflect all market opinions. Market sentiment can change rapidly and should be considered 
                    alongside other analysis methods.
                  </AlertDescription>
                </Box>
              </Alert>
            </TabPanel>
            
            {/* Portfolio Recommendations Tab */}
            <TabPanel>
              <Box mb={6}>
                <Heading size="md" mb={4}>
                  AI Portfolio Recommendations
                </Heading>
                <Text mb={4}>
                  Our AI analyzes market data, correlations, and risk factors to generate portfolio 
                  recommendations based on your risk tolerance. These recommendations aim to optimize 
                  the risk-reward profile of your cryptocurrency investments.
                </Text>
                <PortfolioRecommendations initialRiskTolerance="moderate" />
              </Box>
              
              <Alert status="info" borderRadius="md">
                <AlertIcon />
                <Box>
                  <AlertTitle>Disclaimer</AlertTitle>
                  <AlertDescription>
                    Portfolio recommendations are generated by AI models and should not be considered financial 
                    advice. These recommendations do not take into account your personal financial situation, 
                    goals, or risk tolerance. Always consult with a financial advisor before making investment decisions.
                  </AlertDescription>
                </Box>
              </Alert>
            </TabPanel>
          </TabPanels>
        </Tabs>
      )}
    </Container>
  );
};

export default AIInsightsPage;
