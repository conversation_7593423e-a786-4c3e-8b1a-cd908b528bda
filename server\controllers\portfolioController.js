/**
 * Portfolio Controller
 *
 * This controller handles user portfolio management, including
 * creating, updating, and deleting portfolios and transactions.
 */

const asyncHandler = require('express-async-handler');
const Portfolio = require('../models/Portfolio');
const Transaction = require('../models/Transaction');
const enhancedCoinGeckoService = require('../services/enhancedCoinGeckoService');

/**
 * Get all portfolios for a user
 * @route GET /api/portfolios
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} - JSON response with portfolios
 */
const getPortfolios = asyncHandler(async (req, res) => {
  // Get user ID from authenticated request
  const userId = req.user.id;

  // Find portfolios for user
  const userPortfolios = await Portfolio.find({ user: userId }).sort({ createdAt: -1 });

  res.json({
    success: true,
    data: userPortfolios,
  });
});

/**
 * Create a new portfolio
 * @route POST /api/portfolios
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} - JSON response with created portfolio
 */
const createPortfolio = asyncHandler(async (req, res) => {
  const { name, description } = req.body;

  // Validate input
  if (!name) {
    res.status(400);
    throw new Error('Portfolio name is required');
  }

  // Check if this is the first portfolio (to set as default)
  const portfolioCount = await Portfolio.countDocuments({ user: req.user.id });
  const isDefault = portfolioCount === 0;

  // Create new portfolio
  const portfolio = await Portfolio.create({
    user: req.user.id,
    name,
    description: description || '',
    totalValue: 0,
    totalCost: 0,
    profitLoss: 0,
    profitLossPercentage: 0,
    isDefault,
  });

  res.status(201).json({
    success: true,
    data: portfolio,
  });
});

/**
 * Get a specific portfolio by ID
 * @route GET /api/portfolios/:id
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} - JSON response with portfolio
 */
const getPortfolioById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  // Find portfolio by ID and user ID
  const portfolio = await Portfolio.findOne({ _id: id, user: req.user.id });

  if (!portfolio) {
    res.status(404);
    throw new Error('Portfolio not found');
  }

  // Get transactions for this portfolio
  const portfolioTransactions = await Transaction.find({ portfolio: id }).sort({ date: -1 });

  // Return portfolio with transactions
  res.json({
    success: true,
    data: {
      ...portfolio.toObject(),
      transactions: portfolioTransactions,
    },
  });
});

/**
 * Update a portfolio
 * @route PUT /api/portfolios/:id
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} - JSON response with updated portfolio
 */
const updatePortfolio = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { name, description } = req.body;

  // Find portfolio by ID and user ID
  const portfolio = await Portfolio.findOne({ _id: id, user: req.user.id });

  if (!portfolio) {
    res.status(404);
    throw new Error('Portfolio not found');
  }

  // Update portfolio fields
  if (name) portfolio.name = name;
  if (description !== undefined) portfolio.description = description;

  // Save updated portfolio
  const updatedPortfolio = await portfolio.save();

  res.json({
    success: true,
    data: updatedPortfolio,
  });
});

/**
 * Delete a portfolio
 * @route DELETE /api/portfolios/:id
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} - JSON response with success message
 */
const deletePortfolio = asyncHandler(async (req, res) => {
  const { id } = req.params;

  // Find portfolio by ID and user ID
  const portfolio = await Portfolio.findOne({ _id: id, user: req.user.id });

  if (!portfolio) {
    res.status(404);
    throw new Error('Portfolio not found');
  }

  // Check if it's the default portfolio
  if (portfolio.isDefault) {
    res.status(400);
    throw new Error('Cannot delete the default portfolio');
  }

  // Delete all transactions for this portfolio
  await Transaction.deleteMany({ portfolio: id });

  // Delete the portfolio
  await portfolio.deleteOne();

  res.json({
    success: true,
    message: 'Portfolio deleted successfully',
  });
});

/**
 * Add a transaction to a portfolio
 * @route POST /api/portfolios/:id/transactions
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} - JSON response with created transaction
 */
const addTransaction = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { cryptoId, type, quantity, price, date, notes } = req.body;

  // Validate input
  if (!cryptoId || !type || !quantity || !price) {
    res.status(400);
    throw new Error('Crypto ID, type, quantity, and price are required');
  }

  // Check if type is valid
  if (type !== 'buy' && type !== 'sell') {
    res.status(400);
    throw new Error('Transaction type must be "buy" or "sell"');
  }

  // Find portfolio by ID and user ID
  const portfolio = await Portfolio.findOne({ _id: id, user: req.user.id });

  if (!portfolio) {
    res.status(404);
    throw new Error('Portfolio not found');
  }

  // Get crypto details from CoinGecko
  let cryptoName = '';
  let cryptoSymbol = '';

  try {
    const cryptoData = await enhancedCoinGeckoService.getCoinDetails(cryptoId);
    cryptoName = cryptoData.name;
    cryptoSymbol = cryptoData.symbol.toUpperCase();
  } catch (error) {
    console.error(`Error fetching crypto details for ${cryptoId}:`, error);
    // Use placeholder values if API fails
    cryptoName = 'Unknown';
    cryptoSymbol = 'UNKNOWN';
  }

  // Create new transaction
  const transaction = await Transaction.create({
    portfolio: id,
    user: req.user.id,
    cryptoId,
    cryptoName,
    cryptoSymbol,
    type,
    quantity: parseFloat(quantity),
    price: parseFloat(price),
    total: parseFloat(quantity) * parseFloat(price),
    date: date ? new Date(date) : new Date(),
    notes: notes || '',
  });

  // Update portfolio totals
  await updatePortfolioTotals(id);

  res.status(201).json({
    success: true,
    data: transaction,
  });
});

/**
 * Update a transaction
 * @route PUT /api/portfolios/:portfolioId/transactions/:transactionId
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} - JSON response with updated transaction
 */
const updateTransaction = async (req, res) => {
  try {
    const { portfolioId, transactionId } = req.params;
    const { cryptoId, type, quantity, price, date, notes } = req.body;

    // Find portfolio by ID
    const portfolio = portfolios.find(p => p.id === portfolioId && p.userId === req.user.id);

    if (!portfolio) {
      return res.status(404).json({
        success: false,
        message: 'Portfolio not found',
      });
    }

    // Find transaction by ID
    const transactionIndex = transactions.findIndex(t => t.id === transactionId && t.portfolioId === portfolioId);

    if (transactionIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Transaction not found',
      });
    }

    // Update transaction
    const transaction = transactions[transactionIndex];

    if (cryptoId) {
      transaction.cryptoId = cryptoId;
    }

    if (type) {
      // Check if type is valid
      if (type !== 'buy' && type !== 'sell') {
        return res.status(400).json({
          success: false,
          message: 'Transaction type must be "buy" or "sell"',
        });
      }

      transaction.type = type;
    }

    if (quantity) {
      transaction.quantity = parseFloat(quantity);
    }

    if (price) {
      transaction.price = parseFloat(price);
    }

    if (quantity || price) {
      transaction.total = transaction.quantity * transaction.price;
    }

    if (date) {
      transaction.date = new Date(date);
    }

    if (notes !== undefined) {
      transaction.notes = notes;
    }

    transaction.updatedAt = new Date();

    // Update transaction in mock database
    transactions[transactionIndex] = transaction;

    // Update portfolio totals
    updatePortfolioTotals(portfolioId);

    return res.json({
      success: true,
      data: transaction,
    });
  } catch (error) {
    console.error('Error in updateTransaction controller:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to update transaction',
      error: error.message,
    });
  }
};

/**
 * Delete a transaction
 * @route DELETE /api/portfolios/:portfolioId/transactions/:transactionId
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} - JSON response with success message
 */
const deleteTransaction = async (req, res) => {
  try {
    const { portfolioId, transactionId } = req.params;

    // Find portfolio by ID
    const portfolio = portfolios.find(p => p.id === portfolioId && p.userId === req.user.id);

    if (!portfolio) {
      return res.status(404).json({
        success: false,
        message: 'Portfolio not found',
      });
    }

    // Find transaction by ID
    const transactionIndex = transactions.findIndex(t => t.id === transactionId && t.portfolioId === portfolioId);

    if (transactionIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Transaction not found',
      });
    }

    // Delete transaction from mock database
    transactions.splice(transactionIndex, 1);

    // Update portfolio totals
    updatePortfolioTotals(portfolioId);

    return res.json({
      success: true,
      message: 'Transaction deleted successfully',
    });
  } catch (error) {
    console.error('Error in deleteTransaction controller:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to delete transaction',
      error: error.message,
    });
  }
};

/**
 * Get portfolio performance
 * @route GET /api/portfolios/:id/performance
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} - JSON response with portfolio performance
 */
const getPortfolioPerformance = async (req, res) => {
  try {
    const { id } = req.params;
    const { period } = req.query;

    // Find portfolio by ID
    const portfolio = portfolios.find(p => p.id === id && p.userId === req.user.id);

    if (!portfolio) {
      return res.status(404).json({
        success: false,
        message: 'Portfolio not found',
      });
    }

    // Get transactions for this portfolio
    const portfolioTransactions = transactions.filter(t => t.portfolioId === id);

    // Calculate performance based on period
    const now = new Date();
    let startDate;

    switch (period) {
      case '1d':
        startDate = new Date(now.setDate(now.getDate() - 1));
        break;
      case '1w':
        startDate = new Date(now.setDate(now.getDate() - 7));
        break;
      case '1m':
        startDate = new Date(now.setMonth(now.getMonth() - 1));
        break;
      case '3m':
        startDate = new Date(now.setMonth(now.getMonth() - 3));
        break;
      case '6m':
        startDate = new Date(now.setMonth(now.getMonth() - 6));
        break;
      case '1y':
        startDate = new Date(now.setFullYear(now.getFullYear() - 1));
        break;
      case 'all':
      default:
        startDate = new Date(0); // Beginning of time
        break;
    }

    // Filter transactions by date
    const filteredTransactions = portfolioTransactions.filter(t => new Date(t.date) >= startDate);

    // Generate mock performance data
    const performanceData = generateMockPerformanceData(startDate, new Date(), portfolio.totalValue);

    return res.json({
      success: true,
      data: {
        portfolio,
        transactions: filteredTransactions,
        performance: performanceData,
      },
    });
  } catch (error) {
    console.error('Error in getPortfolioPerformance controller:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to get portfolio performance',
      error: error.message,
    });
  }
};

/**
 * Helper function to update portfolio totals
 * @param {string} portfolioId - Portfolio ID
 */
const updatePortfolioTotals = async (portfolioId) => {
  // Find portfolio by ID
  const portfolio = await Portfolio.findById(portfolioId);

  if (!portfolio) {
    return;
  }

  // Get transactions for this portfolio
  const portfolioTransactions = await Transaction.find({ portfolio: portfolioId });

  // Calculate totals
  let totalCost = 0;
  let totalValue = 0;

  // Group transactions by crypto
  const cryptoHoldings = {};

  portfolioTransactions.forEach(transaction => {
    if (!cryptoHoldings[transaction.cryptoId]) {
      cryptoHoldings[transaction.cryptoId] = {
        quantity: 0,
        totalCost: 0,
        cryptoName: transaction.cryptoName,
        cryptoSymbol: transaction.cryptoSymbol,
      };
    }

    if (transaction.type === 'buy') {
      cryptoHoldings[transaction.cryptoId].quantity += transaction.quantity;
      cryptoHoldings[transaction.cryptoId].totalCost += transaction.total;
    } else if (transaction.type === 'sell') {
      cryptoHoldings[transaction.cryptoId].quantity -= transaction.quantity;
      cryptoHoldings[transaction.cryptoId].totalCost -= transaction.total;
    }
  });

  // Calculate total cost and value
  const holdings = [];

  for (const cryptoId of Object.keys(cryptoHoldings)) {
    const holding = cryptoHoldings[cryptoId];

    if (holding.quantity > 0) {
      totalCost += holding.totalCost;

      // Try to get current price from CoinGecko
      let currentPrice = 0;
      try {
        const cryptoData = await enhancedCoinGeckoService.getCoinDetails(cryptoId);
        currentPrice = cryptoData.market_data?.current_price?.usd || 0;
      } catch (error) {
        console.error(`Error fetching current price for ${cryptoId}:`, error);
        // Fallback to last transaction price
        const lastTransaction = await Transaction.findOne(
          { portfolio: portfolioId, cryptoId },
          {},
          { sort: { date: -1 } }
        );
        currentPrice = lastTransaction ? lastTransaction.price : 0;
      }

      const value = holding.quantity * currentPrice;
      totalValue += value;

      holdings.push({
        cryptoId,
        cryptoName: holding.cryptoName,
        cryptoSymbol: holding.cryptoSymbol,
        quantity: holding.quantity,
        totalCost: holding.totalCost,
        currentPrice,
        value,
        profitLoss: value - holding.totalCost,
        profitLossPercentage: holding.totalCost > 0 ? ((value - holding.totalCost) / holding.totalCost) * 100 : 0,
      });
    }
  }

  // Update portfolio
  portfolio.totalCost = totalCost;
  portfolio.totalValue = totalValue;
  portfolio.profitLoss = totalValue - totalCost;
  portfolio.profitLossPercentage = totalCost > 0 ? (portfolio.profitLoss / totalCost) * 100 : 0;

  // Save updated portfolio
  await portfolio.save();

  return holdings;
};

/**
 * Helper function to generate mock performance data
 * @param {Date} startDate - Start date
 * @param {Date} endDate - End date
 * @param {number} currentValue - Current portfolio value
 * @returns {Array} - Array of performance data points
 */
const generateMockPerformanceData = (startDate, endDate, currentValue) => {
  const performanceData = [];
  const days = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
  const startValue = currentValue * 0.8; // Assume 20% growth over the period

  // Generate data points
  for (let i = 0; i <= days; i++) {
    const date = new Date(startDate);
    date.setDate(date.getDate() + i);

    // Calculate value with some random fluctuation
    const progress = i / days;
    const baseValue = startValue + (currentValue - startValue) * progress;
    const randomFactor = 0.95 + Math.random() * 0.1; // Random between 0.95 and 1.05
    const value = baseValue * randomFactor;

    performanceData.push({
      date: date.toISOString(),
      value,
    });
  }

  return performanceData;
};

module.exports = {
  getPortfolios,
  createPortfolio,
  getPortfolioById,
  updatePortfolio,
  deletePortfolio,
  addTransaction,
  updateTransaction,
  deleteTransaction,
  getPortfolioPerformance,
};
