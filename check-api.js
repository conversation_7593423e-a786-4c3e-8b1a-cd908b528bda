const http = require('http');

// Function to make an HTTP request
function makeRequest(options) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: jsonData
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: data,
            error: 'Failed to parse JSON'
          });
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.end();
  });
}

// Check API endpoint
async function checkApi() {
  console.log('Checking API endpoint...');
  
  try {
    const response = await makeRequest({
      hostname: 'localhost',
      port: 3001,
      path: '/api',
      method: 'GET'
    });
    
    console.log('API Response:');
    console.log('Status Code:', response.statusCode);
    console.log('Headers:', response.headers);
    console.log('Data:', response.data);
    
    return response;
  } catch (error) {
    console.error('Error checking API:', error.message);
    return { error: error.message };
  }
}

// Check cryptos endpoint
async function checkCryptos() {
  console.log('Checking cryptos endpoint...');
  
  try {
    const response = await makeRequest({
      hostname: 'localhost',
      port: 3001,
      path: '/api/cryptos',
      method: 'GET'
    });
    
    console.log('Cryptos Response:');
    console.log('Status Code:', response.statusCode);
    console.log('Data:', response.data);
    
    return response;
  } catch (error) {
    console.error('Error checking cryptos:', error.message);
    return { error: error.message };
  }
}

// Check trending endpoint
async function checkTrending() {
  console.log('Checking trending endpoint...');
  
  try {
    const response = await makeRequest({
      hostname: 'localhost',
      port: 3001,
      path: '/api/cryptos/trending',
      method: 'GET'
    });
    
    console.log('Trending Response:');
    console.log('Status Code:', response.statusCode);
    console.log('Data:', response.data);
    
    return response;
  } catch (error) {
    console.error('Error checking trending:', error.message);
    return { error: error.message };
  }
}

// Run all checks
async function runAllChecks() {
  console.log('Running all API checks...');
  
  try {
    const apiResponse = await checkApi();
    const cryptosResponse = await checkCryptos();
    const trendingResponse = await checkTrending();
    
    console.log('All checks completed.');
    
    return {
      api: apiResponse,
      cryptos: cryptosResponse,
      trending: trendingResponse
    };
  } catch (error) {
    console.error('Error running checks:', error.message);
    return { error: error.message };
  }
}

// Run the checks
runAllChecks();
