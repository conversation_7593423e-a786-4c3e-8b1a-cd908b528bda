import type { ThemeConfig } from "./theme.types";
export declare const theme: {
    components: {
        Accordion: {
            baseStyle?: {
                container: {
                    borderTopWidth: string;
                    borderColor: string;
                    _last: {
                        borderBottomWidth: string;
                    };
                };
                button: {
                    transitionProperty: string;
                    transitionDuration: string;
                    fontSize: string;
                    _focusVisible: {
                        boxShadow: string;
                    };
                    _hover: {
                        bg: string;
                    };
                    _disabled: {
                        opacity: number;
                        cursor: string;
                    };
                    px: string;
                    py: string;
                };
                panel: {
                    pt: string;
                    px: string;
                    pb: string;
                };
                icon: {
                    fontSize: string;
                };
            } | undefined;
            sizes?: {
                [key: string]: import("@chakra-ui/styled-system").PartsStyleInterpolation<{
                    keys: ("container" | "root" | "button" | "panel" | "icon")[];
                }>;
            } | undefined;
            variants?: {
                [key: string]: import("@chakra-ui/styled-system").PartsStyleInterpolation<{
                    keys: ("container" | "root" | "button" | "panel" | "icon")[];
                }>;
            } | undefined;
            defaultProps?: {
                size?: string | number | undefined;
                variant?: string | number | undefined;
                colorScheme?: string | undefined;
            } | undefined;
            parts: ("container" | "root" | "button" | "panel" | "icon")[];
        };
        Alert: {
            baseStyle?: {
                container: {
                    bg: string;
                    px: string;
                    py: string;
                };
                title: {
                    fontWeight: string;
                    lineHeight: string;
                    marginEnd: string;
                };
                description: {
                    lineHeight: string;
                };
                icon: {
                    color: string;
                    flexShrink: number;
                    marginEnd: string;
                    w: string;
                    h: string;
                };
                spinner: {
                    color: string;
                    flexShrink: number;
                    marginEnd: string;
                    w: string;
                    h: string;
                };
            } | undefined;
            sizes?: {
                [key: string]: import("@chakra-ui/styled-system").PartsStyleInterpolation<{
                    keys: ("container" | "icon" | "spinner" | "title" | "description")[];
                }>;
            } | undefined;
            variants?: {
                subtle: (props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                    container: {
                        [x: string]: string | {
                            [x: string]: string;
                        };
                        _dark: {
                            [x: string]: string;
                        };
                    };
                };
                "left-accent": (props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                    container: {
                        [x: string]: string | {
                            [x: string]: string;
                        };
                        _dark: {
                            [x: string]: string;
                        };
                        paddingStart: string;
                        borderStartWidth: string;
                        borderStartColor: string;
                    };
                };
                "top-accent": (props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                    container: {
                        [x: string]: string | {
                            [x: string]: string;
                        };
                        _dark: {
                            [x: string]: string;
                        };
                        pt: string;
                        borderTopWidth: string;
                        borderTopColor: string;
                    };
                };
                solid: (props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                    container: {
                        [x: string]: string | {
                            [x: string]: string;
                        };
                        _dark: {
                            [x: string]: string;
                        };
                        color: string;
                    };
                };
            } | undefined;
            defaultProps?: {
                size?: string | number | undefined;
                variant?: "solid" | "subtle" | "left-accent" | "top-accent" | undefined;
                colorScheme?: string | undefined;
            } | undefined;
            parts: ("container" | "icon" | "spinner" | "title" | "description")[];
        };
        Avatar: {
            baseStyle?: ((props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                badge: {
                    [x: string]: string | {
                        [x: string]: string;
                    };
                    borderRadius: string;
                    border: string;
                    borderColor: string;
                    _dark: {
                        [x: string]: string;
                    };
                };
                excessLabel: {
                    [x: string]: string | {
                        [x: string]: string;
                    };
                    bg: string;
                    fontSize: string;
                    width: string;
                    height: string;
                    lineHeight: string;
                    _dark: {
                        [x: string]: string;
                    };
                };
                container: {
                    [x: string]: string | {
                        [x: string]: string;
                    };
                    bg: string;
                    fontSize: string;
                    color: string;
                    borderColor: string;
                    verticalAlign: string;
                    width: string;
                    height: string;
                    "&:not([data-loaded])": {
                        [x: string]: string;
                    };
                    _dark: {
                        [x: string]: string;
                    };
                };
                label: {
                    fontSize: string;
                    lineHeight: string;
                };
            }) | undefined;
            sizes?: {
                "2xs": {
                    container: {
                        [x: string]: string | 1 | {
                            sm: string;
                            md: string;
                            lg: string;
                            xl: string;
                        } | 0.5 | 1.5 | 2 | 2.5 | 3 | 3.5 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 12 | 14 | 16 | 20 | 24 | 28 | 32 | 36 | 40 | 44 | 48 | 52 | 56 | 60 | 64 | 72 | 80 | 96;
                    };
                    excessLabel: {
                        [x: string]: string | 1 | {
                            sm: string;
                            md: string;
                            lg: string;
                            xl: string;
                        } | 0.5 | 1.5 | 2 | 2.5 | 3 | 3.5 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 12 | 14 | 16 | 20 | 24 | 28 | 32 | 36 | 40 | 44 | 48 | 52 | 56 | 60 | 64 | 72 | 80 | 96;
                    };
                };
                xs: {
                    container: {
                        [x: string]: string | 1 | {
                            sm: string;
                            md: string;
                            lg: string;
                            xl: string;
                        } | 0.5 | 1.5 | 2 | 2.5 | 3 | 3.5 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 12 | 14 | 16 | 20 | 24 | 28 | 32 | 36 | 40 | 44 | 48 | 52 | 56 | 60 | 64 | 72 | 80 | 96;
                    };
                    excessLabel: {
                        [x: string]: string | 1 | {
                            sm: string;
                            md: string;
                            lg: string;
                            xl: string;
                        } | 0.5 | 1.5 | 2 | 2.5 | 3 | 3.5 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 12 | 14 | 16 | 20 | 24 | 28 | 32 | 36 | 40 | 44 | 48 | 52 | 56 | 60 | 64 | 72 | 80 | 96;
                    };
                };
                sm: {
                    container: {
                        [x: string]: string | 1 | {
                            sm: string;
                            md: string;
                            lg: string;
                            xl: string;
                        } | 0.5 | 1.5 | 2 | 2.5 | 3 | 3.5 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 12 | 14 | 16 | 20 | 24 | 28 | 32 | 36 | 40 | 44 | 48 | 52 | 56 | 60 | 64 | 72 | 80 | 96;
                    };
                    excessLabel: {
                        [x: string]: string | 1 | {
                            sm: string;
                            md: string;
                            lg: string;
                            xl: string;
                        } | 0.5 | 1.5 | 2 | 2.5 | 3 | 3.5 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 12 | 14 | 16 | 20 | 24 | 28 | 32 | 36 | 40 | 44 | 48 | 52 | 56 | 60 | 64 | 72 | 80 | 96;
                    };
                };
                md: {
                    container: {
                        [x: string]: string | 1 | {
                            sm: string;
                            md: string;
                            lg: string;
                            xl: string;
                        } | 0.5 | 1.5 | 2 | 2.5 | 3 | 3.5 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 12 | 14 | 16 | 20 | 24 | 28 | 32 | 36 | 40 | 44 | 48 | 52 | 56 | 60 | 64 | 72 | 80 | 96;
                    };
                    excessLabel: {
                        [x: string]: string | 1 | {
                            sm: string;
                            md: string;
                            lg: string;
                            xl: string;
                        } | 0.5 | 1.5 | 2 | 2.5 | 3 | 3.5 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 12 | 14 | 16 | 20 | 24 | 28 | 32 | 36 | 40 | 44 | 48 | 52 | 56 | 60 | 64 | 72 | 80 | 96;
                    };
                };
                lg: {
                    container: {
                        [x: string]: string | 1 | {
                            sm: string;
                            md: string;
                            lg: string;
                            xl: string;
                        } | 0.5 | 1.5 | 2 | 2.5 | 3 | 3.5 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 12 | 14 | 16 | 20 | 24 | 28 | 32 | 36 | 40 | 44 | 48 | 52 | 56 | 60 | 64 | 72 | 80 | 96;
                    };
                    excessLabel: {
                        [x: string]: string | 1 | {
                            sm: string;
                            md: string;
                            lg: string;
                            xl: string;
                        } | 0.5 | 1.5 | 2 | 2.5 | 3 | 3.5 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 12 | 14 | 16 | 20 | 24 | 28 | 32 | 36 | 40 | 44 | 48 | 52 | 56 | 60 | 64 | 72 | 80 | 96;
                    };
                };
                xl: {
                    container: {
                        [x: string]: string | 1 | {
                            sm: string;
                            md: string;
                            lg: string;
                            xl: string;
                        } | 0.5 | 1.5 | 2 | 2.5 | 3 | 3.5 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 12 | 14 | 16 | 20 | 24 | 28 | 32 | 36 | 40 | 44 | 48 | 52 | 56 | 60 | 64 | 72 | 80 | 96;
                    };
                    excessLabel: {
                        [x: string]: string | 1 | {
                            sm: string;
                            md: string;
                            lg: string;
                            xl: string;
                        } | 0.5 | 1.5 | 2 | 2.5 | 3 | 3.5 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 12 | 14 | 16 | 20 | 24 | 28 | 32 | 36 | 40 | 44 | 48 | 52 | 56 | 60 | 64 | 72 | 80 | 96;
                    };
                };
                "2xl": {
                    container: {
                        [x: string]: string | 1 | {
                            sm: string;
                            md: string;
                            lg: string;
                            xl: string;
                        } | 0.5 | 1.5 | 2 | 2.5 | 3 | 3.5 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 12 | 14 | 16 | 20 | 24 | 28 | 32 | 36 | 40 | 44 | 48 | 52 | 56 | 60 | 64 | 72 | 80 | 96;
                    };
                    excessLabel: {
                        [x: string]: string | 1 | {
                            sm: string;
                            md: string;
                            lg: string;
                            xl: string;
                        } | 0.5 | 1.5 | 2 | 2.5 | 3 | 3.5 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 12 | 14 | 16 | 20 | 24 | 28 | 32 | 36 | 40 | 44 | 48 | 52 | 56 | 60 | 64 | 72 | 80 | 96;
                    };
                };
                full: {
                    container: {
                        [x: string]: string | 1 | {
                            sm: string;
                            md: string;
                            lg: string;
                            xl: string;
                        } | 0.5 | 1.5 | 2 | 2.5 | 3 | 3.5 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 12 | 14 | 16 | 20 | 24 | 28 | 32 | 36 | 40 | 44 | 48 | 52 | 56 | 60 | 64 | 72 | 80 | 96;
                    };
                    excessLabel: {
                        [x: string]: string | 1 | {
                            sm: string;
                            md: string;
                            lg: string;
                            xl: string;
                        } | 0.5 | 1.5 | 2 | 2.5 | 3 | 3.5 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 12 | 14 | 16 | 20 | 24 | 28 | 32 | 36 | 40 | 44 | 48 | 52 | 56 | 60 | 64 | 72 | 80 | 96;
                    };
                };
            } | undefined;
            variants?: {
                [key: string]: import("@chakra-ui/styled-system").PartsStyleInterpolation<{
                    keys: ("container" | "label" | "badge" | "excessLabel" | "group")[];
                }>;
            } | undefined;
            defaultProps?: {
                size?: "md" | "full" | "2xs" | "xs" | "sm" | "lg" | "xl" | "2xl" | undefined;
                variant?: string | number | undefined;
                colorScheme?: string | undefined;
            } | undefined;
            parts: ("container" | "label" | "badge" | "excessLabel" | "group")[];
        };
        Badge: {
            baseStyle?: {
                px: number;
                textTransform: string;
                fontSize: string;
                borderRadius: string;
                fontWeight: string;
                bg: string;
                color: string;
                boxShadow: string;
            } | undefined;
            sizes?: {
                [key: string]: import("@chakra-ui/styled-system").SystemStyleInterpolation;
            } | undefined;
            variants?: {
                solid: (props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                    [x: string]: string | {
                        [x: string]: string;
                    };
                    _dark: {
                        [x: string]: string;
                    };
                };
                subtle: (props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                    [x: string]: string | {
                        [x: string]: string;
                    };
                    _dark: {
                        [x: string]: string;
                    };
                };
                outline: (props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                    [x: string]: string | {
                        [x: string]: string;
                    };
                    _dark: {
                        [x: string]: string;
                    };
                };
            } | undefined;
            defaultProps?: {
                size?: string | number | undefined;
                variant?: "outline" | "solid" | "subtle" | undefined;
                colorScheme?: string | undefined;
            } | undefined;
        };
        Breadcrumb: {
            baseStyle?: {
                link: {
                    [x: string]: string | {
                        cursor: string;
                        _hover: {
                            [x: string]: string;
                        };
                        _focusVisible: {
                            boxShadow: string;
                        };
                    };
                    transitionProperty: string;
                    transitionDuration: string;
                    transitionTimingFunction: string;
                    outline: string;
                    color: string;
                    textDecoration: string;
                    "&:not([aria-current=page])": {
                        cursor: string;
                        _hover: {
                            [x: string]: string;
                        };
                        _focusVisible: {
                            boxShadow: string;
                        };
                    };
                };
            } | undefined;
            sizes?: {
                [key: string]: import("@chakra-ui/styled-system").PartsStyleInterpolation<{
                    keys: ("container" | "link" | "separator" | "item")[];
                }>;
            } | undefined;
            variants?: {
                [key: string]: import("@chakra-ui/styled-system").PartsStyleInterpolation<{
                    keys: ("container" | "link" | "separator" | "item")[];
                }>;
            } | undefined;
            defaultProps?: {
                size?: string | number | undefined;
                variant?: string | number | undefined;
                colorScheme?: string | undefined;
            } | undefined;
            parts: ("container" | "link" | "separator" | "item")[];
        };
        Button: {
            baseStyle?: {
                lineHeight: string;
                borderRadius: string;
                fontWeight: string;
                transitionProperty: string;
                transitionDuration: string;
                _focusVisible: {
                    boxShadow: string;
                };
                _disabled: {
                    opacity: number;
                    cursor: string;
                    boxShadow: string;
                };
                _hover: {
                    _disabled: {
                        bg: string;
                    };
                };
            } | undefined;
            sizes?: {
                lg: {
                    h: string;
                    minW: string;
                    fontSize: string;
                    px: string;
                };
                md: {
                    h: string;
                    minW: string;
                    fontSize: string;
                    px: string;
                };
                sm: {
                    h: string;
                    minW: string;
                    fontSize: string;
                    px: string;
                };
                xs: {
                    h: string;
                    minW: string;
                    fontSize: string;
                    px: string;
                };
            } | undefined;
            variants?: {
                ghost: (props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                    color: string;
                    _hover: {
                        bg: string;
                    };
                    _active: {
                        bg: string;
                    };
                    bg?: undefined;
                } | {
                    color: string;
                    bg: string;
                    _hover: {
                        bg: string;
                    };
                    _active: {
                        bg: string;
                    };
                };
                outline: (props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                    color: string;
                    _hover: {
                        bg: string;
                    };
                    _active: {
                        bg: string;
                    };
                    bg?: undefined;
                    border: string;
                    borderColor: string;
                    ".chakra-button__group[data-attached][data-orientation=horizontal] > &:not(:last-of-type)": {
                        marginEnd: string;
                    };
                    ".chakra-button__group[data-attached][data-orientation=vertical] > &:not(:last-of-type)": {
                        marginBottom: string;
                    };
                } | {
                    color: string;
                    bg: string;
                    _hover: {
                        bg: string;
                    };
                    _active: {
                        bg: string;
                    };
                    border: string;
                    borderColor: string;
                    ".chakra-button__group[data-attached][data-orientation=horizontal] > &:not(:last-of-type)": {
                        marginEnd: string;
                    };
                    ".chakra-button__group[data-attached][data-orientation=vertical] > &:not(:last-of-type)": {
                        marginBottom: string;
                    };
                };
                solid: (props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                    bg: string;
                    color: string;
                    _hover: {
                        bg: string;
                        _disabled: {
                            bg: string;
                        };
                    };
                    _active: {
                        bg: string;
                    };
                };
                link: (props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                    padding: number;
                    height: string;
                    lineHeight: string;
                    verticalAlign: string;
                    color: string;
                    _hover: {
                        textDecoration: string;
                        _disabled: {
                            textDecoration: string;
                        };
                    };
                    _active: {
                        color: string;
                    };
                };
                unstyled: {
                    bg: string;
                    color: string;
                    display: string;
                    lineHeight: string;
                    m: string;
                    p: string;
                };
            } | undefined;
            defaultProps?: {
                size?: "md" | "xs" | "sm" | "lg" | undefined;
                variant?: "link" | "outline" | "solid" | "ghost" | "unstyled" | undefined;
                colorScheme?: string | undefined;
            } | undefined;
        };
        Checkbox: {
            baseStyle?: ((props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                icon: {
                    transitionProperty: string;
                    transitionDuration: string;
                };
                container: {
                    _disabled: {
                        cursor: string;
                    };
                };
                control: {
                    w: string;
                    h: string;
                    transitionProperty: string;
                    transitionDuration: string;
                    border: string;
                    borderRadius: string;
                    borderColor: string;
                    color: string;
                    _checked: {
                        bg: string;
                        borderColor: string;
                        color: string;
                        _hover: {
                            bg: string;
                            borderColor: string;
                        };
                        _disabled: {
                            borderColor: string;
                            bg: string;
                            color: string;
                        };
                    };
                    _indeterminate: {
                        bg: string;
                        borderColor: string;
                        color: string;
                    };
                    _disabled: {
                        bg: string;
                        borderColor: string;
                    };
                    _focusVisible: {
                        boxShadow: string;
                    };
                    _invalid: {
                        borderColor: string;
                    };
                };
                label: {
                    userSelect: string;
                    _disabled: {
                        opacity: number;
                    };
                };
            }) | undefined;
            sizes?: {
                sm: {
                    control: {
                        [x: string]: string;
                    };
                    label: {
                        fontSize: string;
                    };
                    icon: {
                        fontSize: string;
                    };
                };
                md: {
                    control: {
                        [x: string]: string;
                    };
                    label: {
                        fontSize: string;
                    };
                    icon: {
                        fontSize: string;
                    };
                };
                lg: {
                    control: {
                        [x: string]: string;
                    };
                    label: {
                        fontSize: string;
                    };
                    icon: {
                        fontSize: string;
                    };
                };
            } | undefined;
            variants?: {
                [key: string]: import("@chakra-ui/styled-system").PartsStyleInterpolation<{
                    keys: ("container" | "icon" | "label" | "control")[];
                }>;
            } | undefined;
            defaultProps?: {
                size?: "md" | "sm" | "lg" | undefined;
                variant?: string | number | undefined;
                colorScheme?: string | undefined;
            } | undefined;
            parts: ("container" | "icon" | "label" | "control")[];
        };
        CloseButton: {
            baseStyle?: {
                w: string[];
                h: string[];
                borderRadius: string;
                transitionProperty: string;
                transitionDuration: string;
                _disabled: {
                    opacity: number;
                    cursor: string;
                    boxShadow: string;
                };
                _hover: {
                    [x: string]: string | {
                        [x: string]: string;
                    };
                    _dark: {
                        [x: string]: string;
                    };
                };
                _active: {
                    [x: string]: string | {
                        [x: string]: string;
                    };
                    _dark: {
                        [x: string]: string;
                    };
                };
                _focusVisible: {
                    boxShadow: string;
                };
                bg: string;
            } | undefined;
            sizes?: {
                lg: {
                    [x: string]: string;
                    fontSize: string;
                };
                md: {
                    [x: string]: string;
                    fontSize: string;
                };
                sm: {
                    [x: string]: string;
                    fontSize: string;
                };
            } | undefined;
            variants?: {
                [key: string]: import("@chakra-ui/styled-system").SystemStyleInterpolation;
            } | undefined;
            defaultProps?: {
                size?: "md" | "sm" | "lg" | undefined;
                variant?: string | number | undefined;
                colorScheme?: string | undefined;
            } | undefined;
        };
        Code: {
            baseStyle?: {
                fontFamily: string;
                fontSize: string;
                px: string;
                borderRadius: string;
                bg: string;
                color: string;
                boxShadow: string;
            } | undefined;
            sizes?: {
                [x: string]: {};
            } | undefined;
            variants?: {
                solid: (props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                    [x: string]: string | {
                        [x: string]: string;
                    };
                    _dark: {
                        [x: string]: string;
                    };
                };
                subtle: (props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                    [x: string]: string | {
                        [x: string]: string;
                    };
                    _dark: {
                        [x: string]: string;
                    };
                };
                outline: (props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                    [x: string]: string | {
                        [x: string]: string;
                    };
                    _dark: {
                        [x: string]: string;
                    };
                };
            } | undefined;
            defaultProps?: {
                size?: string | number | undefined;
                variant?: "outline" | "solid" | "subtle" | undefined;
                colorScheme?: string | undefined;
            } | undefined;
        };
        Container: {
            baseStyle?: {
                w: string;
                mx: string;
                maxW: string;
                px: string;
            } | undefined;
            sizes?: {
                [key: string]: import("@chakra-ui/styled-system").SystemStyleInterpolation;
            } | undefined;
            variants?: {
                [key: string]: import("@chakra-ui/styled-system").SystemStyleInterpolation;
            } | undefined;
            defaultProps?: {
                size?: string | number | undefined;
                variant?: string | number | undefined;
                colorScheme?: string | undefined;
            } | undefined;
        };
        Divider: {
            baseStyle?: {
                opacity: number;
                borderColor: string;
            } | undefined;
            sizes?: {
                [key: string]: import("@chakra-ui/styled-system").SystemStyleInterpolation;
            } | undefined;
            variants?: {
                solid: {
                    borderStyle: string;
                };
                dashed: {
                    borderStyle: string;
                };
            } | undefined;
            defaultProps?: {
                size?: string | number | undefined;
                variant?: "dashed" | "solid" | undefined;
                colorScheme?: string | undefined;
            } | undefined;
        };
        Drawer: {
            baseStyle?: ((props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                overlay: {
                    bg: string;
                    zIndex: string;
                };
                dialogContainer: {
                    display: string;
                    zIndex: string;
                    justifyContent: string;
                };
                dialog: any;
                header: {
                    px: string;
                    py: string;
                    fontSize: string;
                    fontWeight: string;
                };
                closeButton: {
                    position: string;
                    top: string;
                    insetEnd: string;
                };
                body: {
                    px: string;
                    py: string;
                    flex: string;
                    overflow: string;
                };
                footer: {
                    px: string;
                    py: string;
                };
            }) | undefined;
            sizes?: {
                xs: {
                    dialog: {
                        maxW: string;
                    };
                };
                sm: {
                    dialog: {
                        maxW: string;
                    };
                };
                md: {
                    dialog: {
                        maxW: string;
                    };
                };
                lg: {
                    dialog: {
                        maxW: string;
                    };
                };
                xl: {
                    dialog: {
                        maxW: string;
                    };
                };
                full: {
                    dialog: {
                        maxW: string;
                    };
                };
            } | undefined;
            variants?: {
                [key: string]: import("@chakra-ui/styled-system").PartsStyleInterpolation<{
                    keys: ("overlay" | "header" | "body" | "footer" | "dialogContainer" | "dialog" | "closeButton")[];
                }>;
            } | undefined;
            defaultProps?: {
                size?: "md" | "full" | "xs" | "sm" | "lg" | "xl" | undefined;
                variant?: string | number | undefined;
                colorScheme?: string | undefined;
            } | undefined;
            parts: ("overlay" | "header" | "body" | "footer" | "dialogContainer" | "dialog" | "closeButton")[];
        };
        Editable: {
            baseStyle?: {
                preview: {
                    borderRadius: string;
                    py: string;
                    transitionProperty: string;
                    transitionDuration: string;
                };
                input: {
                    borderRadius: string;
                    py: string;
                    transitionProperty: string;
                    transitionDuration: string;
                    width: string;
                    _focusVisible: {
                        boxShadow: string;
                    };
                    _placeholder: {
                        opacity: number;
                    };
                };
                textarea: {
                    borderRadius: string;
                    py: string;
                    transitionProperty: string;
                    transitionDuration: string;
                    width: string;
                    _focusVisible: {
                        boxShadow: string;
                    };
                    _placeholder: {
                        opacity: number;
                    };
                };
            } | undefined;
            sizes?: {
                [key: string]: import("@chakra-ui/styled-system").PartsStyleInterpolation<{
                    keys: ("textarea" | "preview" | "input")[];
                }>;
            } | undefined;
            variants?: {
                [key: string]: import("@chakra-ui/styled-system").PartsStyleInterpolation<{
                    keys: ("textarea" | "preview" | "input")[];
                }>;
            } | undefined;
            defaultProps?: {
                size?: string | number | undefined;
                variant?: string | number | undefined;
                colorScheme?: string | undefined;
            } | undefined;
            parts: ("textarea" | "preview" | "input")[];
        };
        Form: {
            baseStyle?: {
                container: {
                    width: string;
                    position: string;
                };
                requiredIndicator: {
                    [x: string]: string | {
                        [x: string]: string;
                    };
                    marginStart: string;
                    _dark: {
                        [x: string]: string;
                    };
                    color: string;
                };
                helperText: {
                    [x: string]: string | {
                        [x: string]: string;
                    };
                    mt: string;
                    _dark: {
                        [x: string]: string;
                    };
                    color: string;
                    lineHeight: string;
                    fontSize: string;
                };
            } | undefined;
            sizes?: {
                [key: string]: import("@chakra-ui/styled-system").PartsStyleInterpolation<{
                    keys: ("container" | "requiredIndicator" | "helperText")[];
                }>;
            } | undefined;
            variants?: {
                [key: string]: import("@chakra-ui/styled-system").PartsStyleInterpolation<{
                    keys: ("container" | "requiredIndicator" | "helperText")[];
                }>;
            } | undefined;
            defaultProps?: {
                size?: string | number | undefined;
                variant?: string | number | undefined;
                colorScheme?: string | undefined;
            } | undefined;
            parts: ("container" | "requiredIndicator" | "helperText")[];
        };
        FormError: {
            baseStyle?: {
                text: {
                    [x: string]: string | {
                        [x: string]: string;
                    };
                    _dark: {
                        [x: string]: string;
                    };
                    color: string;
                    mt: string;
                    fontSize: string;
                    lineHeight: string;
                };
                icon: {
                    [x: string]: string | {
                        [x: string]: string;
                    };
                    marginEnd: string;
                    _dark: {
                        [x: string]: string;
                    };
                    color: string;
                };
            } | undefined;
            sizes?: {
                [key: string]: import("@chakra-ui/styled-system").PartsStyleInterpolation<{
                    keys: ("icon" | "text")[];
                }>;
            } | undefined;
            variants?: {
                [key: string]: import("@chakra-ui/styled-system").PartsStyleInterpolation<{
                    keys: ("icon" | "text")[];
                }>;
            } | undefined;
            defaultProps?: {
                size?: string | number | undefined;
                variant?: string | number | undefined;
                colorScheme?: string | undefined;
            } | undefined;
            parts: ("icon" | "text")[];
        };
        FormLabel: {
            baseStyle?: {
                fontSize: string;
                marginEnd: string;
                mb: string;
                fontWeight: string;
                transitionProperty: string;
                transitionDuration: string;
                opacity: number;
                _disabled: {
                    opacity: number;
                };
            } | undefined;
            sizes?: {
                [key: string]: import("@chakra-ui/styled-system").SystemStyleInterpolation;
            } | undefined;
            variants?: {
                [key: string]: import("@chakra-ui/styled-system").SystemStyleInterpolation;
            } | undefined;
            defaultProps?: {
                size?: string | number | undefined;
                variant?: string | number | undefined;
                colorScheme?: string | undefined;
            } | undefined;
        };
        Heading: {
            baseStyle?: {
                fontFamily: string;
                fontWeight: string;
            } | undefined;
            sizes?: {
                "4xl": {
                    fontSize: (string | null)[];
                    lineHeight: number;
                };
                "3xl": {
                    fontSize: (string | null)[];
                    lineHeight: number;
                };
                "2xl": {
                    fontSize: (string | null)[];
                    lineHeight: (number | null)[];
                };
                xl: {
                    fontSize: (string | null)[];
                    lineHeight: (number | null)[];
                };
                lg: {
                    fontSize: (string | null)[];
                    lineHeight: (number | null)[];
                };
                md: {
                    fontSize: string;
                    lineHeight: number;
                };
                sm: {
                    fontSize: string;
                    lineHeight: number;
                };
                xs: {
                    fontSize: string;
                    lineHeight: number;
                };
            } | undefined;
            variants?: {
                [key: string]: import("@chakra-ui/styled-system").SystemStyleInterpolation;
            } | undefined;
            defaultProps?: {
                size?: "md" | "xs" | "sm" | "lg" | "xl" | "2xl" | "3xl" | "4xl" | undefined;
                variant?: string | number | undefined;
                colorScheme?: string | undefined;
            } | undefined;
        };
        Input: {
            baseStyle?: {
                addon: {
                    height: string;
                    fontSize: string;
                    px: string;
                    borderRadius: string;
                };
                field: {
                    width: string;
                    height: string;
                    fontSize: string;
                    px: string;
                    borderRadius: string;
                    minWidth: number;
                    outline: number;
                    position: string;
                    appearance: string;
                    transitionProperty: string;
                    transitionDuration: string;
                    _disabled: {
                        opacity: number;
                        cursor: string;
                    };
                };
            } | undefined;
            sizes?: {
                lg: {
                    field: {
                        [x: string]: string;
                    };
                    group: {
                        [x: string]: string;
                    };
                };
                md: {
                    field: {
                        [x: string]: string;
                    };
                    group: {
                        [x: string]: string;
                    };
                };
                sm: {
                    field: {
                        [x: string]: string;
                    };
                    group: {
                        [x: string]: string;
                    };
                };
                xs: {
                    field: {
                        [x: string]: string;
                    };
                    group: {
                        [x: string]: string;
                    };
                };
            } | undefined;
            variants?: {
                outline: (props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                    field: {
                        border: string;
                        borderColor: string;
                        bg: string;
                        _hover: {
                            borderColor: string;
                        };
                        _readOnly: {
                            boxShadow: string;
                            userSelect: string;
                        };
                        _invalid: {
                            borderColor: any;
                            boxShadow: string;
                        };
                        _focusVisible: {
                            zIndex: number;
                            borderColor: any;
                            boxShadow: string;
                        };
                    };
                    addon: {
                        border: string;
                        borderColor: string;
                        bg: string;
                    };
                };
                filled: (props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                    field: {
                        border: string;
                        borderColor: string;
                        bg: string;
                        _hover: {
                            bg: string;
                        };
                        _readOnly: {
                            boxShadow: string;
                            userSelect: string;
                        };
                        _invalid: {
                            borderColor: any;
                        };
                        _focusVisible: {
                            bg: string;
                            borderColor: any;
                        };
                    };
                    addon: {
                        border: string;
                        borderColor: string;
                        bg: string;
                    };
                };
                flushed: (props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                    field: {
                        borderBottom: string;
                        borderColor: string;
                        borderRadius: string;
                        px: string;
                        bg: string;
                        _readOnly: {
                            boxShadow: string;
                            userSelect: string;
                        };
                        _invalid: {
                            borderColor: any;
                            boxShadow: string;
                        };
                        _focusVisible: {
                            borderColor: any;
                            boxShadow: string;
                        };
                    };
                    addon: {
                        borderBottom: string;
                        borderColor: string;
                        borderRadius: string;
                        px: string;
                        bg: string;
                    };
                };
                unstyled: {
                    field: {
                        bg: string;
                        px: string;
                        height: string;
                    };
                    addon: {
                        bg: string;
                        px: string;
                        height: string;
                    };
                };
            } | undefined;
            defaultProps?: {
                size?: "md" | "xs" | "sm" | "lg" | undefined;
                variant?: "outline" | "filled" | "unstyled" | "flushed" | undefined;
                colorScheme?: string | undefined;
            } | undefined;
            parts: ("element" | "group" | "addon" | "field")[];
        };
        Kbd: {
            baseStyle?: {
                [x: string]: string | {
                    [x: string]: string;
                };
                _dark: {
                    [x: string]: string;
                };
                bg: string;
                borderRadius: string;
                borderWidth: string;
                borderBottomWidth: string;
                fontSize: string;
                fontWeight: string;
                lineHeight: string;
                px: string;
                whiteSpace: string;
            } | undefined;
            sizes?: {
                [key: string]: import("@chakra-ui/styled-system").SystemStyleInterpolation;
            } | undefined;
            variants?: {
                [key: string]: import("@chakra-ui/styled-system").SystemStyleInterpolation;
            } | undefined;
            defaultProps?: {
                size?: string | number | undefined;
                variant?: string | number | undefined;
                colorScheme?: string | undefined;
            } | undefined;
        };
        Link: {
            baseStyle?: {
                transitionProperty: string;
                transitionDuration: string;
                transitionTimingFunction: string;
                cursor: string;
                textDecoration: string;
                outline: string;
                color: string;
                _hover: {
                    textDecoration: string;
                };
                _focusVisible: {
                    boxShadow: string;
                };
            } | undefined;
            sizes?: {
                [key: string]: import("@chakra-ui/styled-system").SystemStyleInterpolation;
            } | undefined;
            variants?: {
                [key: string]: import("@chakra-ui/styled-system").SystemStyleInterpolation;
            } | undefined;
            defaultProps?: {
                size?: string | number | undefined;
                variant?: string | number | undefined;
                colorScheme?: string | undefined;
            } | undefined;
        };
        List: {
            baseStyle?: {
                icon: {
                    marginEnd: string;
                    display: string;
                    verticalAlign: string;
                };
            } | undefined;
            sizes?: {
                [key: string]: import("@chakra-ui/styled-system").PartsStyleInterpolation<{
                    keys: ("container" | "icon" | "item")[];
                }>;
            } | undefined;
            variants?: {
                [key: string]: import("@chakra-ui/styled-system").PartsStyleInterpolation<{
                    keys: ("container" | "icon" | "item")[];
                }>;
            } | undefined;
            defaultProps?: {
                size?: string | number | undefined;
                variant?: string | number | undefined;
                colorScheme?: string | undefined;
            } | undefined;
            parts: ("container" | "icon" | "item")[];
        };
        Menu: {
            baseStyle?: {
                button: {
                    transitionProperty: string;
                    transitionDuration: string;
                };
                list: {
                    [x: string]: string | {
                        [x: string]: string;
                    };
                    _dark: {
                        [x: string]: string;
                    };
                    color: string;
                    minW: string;
                    py: string;
                    zIndex: string;
                    borderRadius: string;
                    borderWidth: string;
                    bg: string;
                    boxShadow: string;
                };
                item: {
                    py: string;
                    px: string;
                    transitionProperty: string;
                    transitionDuration: string;
                    transitionTimingFunction: string;
                    _focus: {
                        [x: string]: string | {
                            [x: string]: string;
                        };
                        _dark: {
                            [x: string]: string;
                        };
                    };
                    _active: {
                        [x: string]: string | {
                            [x: string]: string;
                        };
                        _dark: {
                            [x: string]: string;
                        };
                    };
                    _expanded: {
                        [x: string]: string | {
                            [x: string]: string;
                        };
                        _dark: {
                            [x: string]: string;
                        };
                    };
                    _disabled: {
                        opacity: number;
                        cursor: string;
                    };
                    bg: string;
                };
                groupTitle: {
                    mx: number;
                    my: number;
                    fontWeight: string;
                    fontSize: string;
                };
                icon: {
                    display: string;
                    alignItems: string;
                    justifyContent: string;
                    flexShrink: number;
                };
                command: {
                    opacity: number;
                };
                divider: {
                    border: number;
                    borderBottom: string;
                    borderColor: string;
                    my: string;
                    opacity: number;
                };
            } | undefined;
            sizes?: {
                [key: string]: import("@chakra-ui/styled-system").PartsStyleInterpolation<{
                    keys: ("button" | "icon" | "item" | "list" | "groupTitle" | "command" | "divider")[];
                }>;
            } | undefined;
            variants?: {
                [key: string]: import("@chakra-ui/styled-system").PartsStyleInterpolation<{
                    keys: ("button" | "icon" | "item" | "list" | "groupTitle" | "command" | "divider")[];
                }>;
            } | undefined;
            defaultProps?: {
                size?: string | number | undefined;
                variant?: string | number | undefined;
                colorScheme?: string | undefined;
            } | undefined;
            parts: ("button" | "icon" | "item" | "list" | "groupTitle" | "command" | "divider")[];
        };
        Modal: {
            baseStyle?: ((props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                overlay: {
                    bg: string;
                    zIndex: string;
                };
                dialogContainer: {
                    display: string;
                    zIndex: string;
                    justifyContent: string;
                    alignItems: string;
                    overflow: string;
                    overscrollBehaviorY: string;
                };
                dialog: {
                    [x: string]: string | {
                        [x: string]: string;
                    } | undefined;
                    borderRadius: string;
                    color: string;
                    my: string;
                    mx: string | undefined;
                    zIndex: string;
                    maxH: string | undefined;
                    _dark: {
                        [x: string]: string;
                    };
                    bg: string;
                    boxShadow: string;
                };
                header: {
                    px: string;
                    py: string;
                    fontSize: string;
                    fontWeight: string;
                };
                closeButton: {
                    position: string;
                    top: string;
                    insetEnd: string;
                };
                body: {
                    px: string;
                    py: string;
                    flex: string;
                    overflow: string | undefined;
                };
                footer: {
                    px: string;
                    py: string;
                };
            }) | undefined;
            sizes?: {
                xs: {
                    dialog: {
                        maxW: string;
                    };
                };
                sm: {
                    dialog: {
                        maxW: string;
                    };
                };
                md: {
                    dialog: {
                        maxW: string;
                    };
                };
                lg: {
                    dialog: {
                        maxW: string;
                    };
                };
                xl: {
                    dialog: {
                        maxW: string;
                    };
                };
                "2xl": {
                    dialog: {
                        maxW: string;
                    };
                };
                "3xl": {
                    dialog: {
                        maxW: string;
                    };
                };
                "4xl": {
                    dialog: {
                        maxW: string;
                    };
                };
                "5xl": {
                    dialog: {
                        maxW: string;
                    };
                };
                "6xl": {
                    dialog: {
                        maxW: string;
                    };
                };
                full: {
                    dialog: {
                        maxW: string;
                    };
                };
            } | undefined;
            variants?: {
                [key: string]: import("@chakra-ui/styled-system").PartsStyleInterpolation<{
                    keys: ("overlay" | "header" | "body" | "footer" | "dialogContainer" | "dialog" | "closeButton")[];
                }>;
            } | undefined;
            defaultProps?: {
                size?: "md" | "full" | "xs" | "sm" | "lg" | "xl" | "2xl" | "3xl" | "4xl" | "5xl" | "6xl" | undefined;
                variant?: string | number | undefined;
                colorScheme?: string | undefined;
            } | undefined;
            parts: ("overlay" | "header" | "body" | "footer" | "dialogContainer" | "dialog" | "closeButton")[];
        };
        NumberInput: {
            baseStyle?: ((props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                root: {
                    [x: string]: string;
                };
                field: {};
                stepperGroup: {
                    width: string;
                };
                stepper: {
                    [x: string]: string | {
                        [x: string]: string;
                        _dark?: undefined;
                        opacity?: undefined;
                        cursor?: undefined;
                    } | {
                        [x: string]: string | {
                            [x: string]: string;
                        };
                        _dark: {
                            [x: string]: string;
                        };
                        opacity?: undefined;
                        cursor?: undefined;
                    } | {
                        opacity: number;
                        cursor: string;
                        _dark?: undefined;
                    };
                    borderStart: string;
                    borderStartColor: string;
                    color: string;
                    bg: string;
                    _dark: {
                        [x: string]: string;
                    };
                    _active: {
                        [x: string]: string | {
                            [x: string]: string;
                        };
                        _dark: {
                            [x: string]: string;
                        };
                    };
                    _disabled: {
                        opacity: number;
                        cursor: string;
                    };
                };
            }) | undefined;
            sizes?: {
                xs: {
                    field: any;
                    stepper: {
                        fontSize: string;
                        _first: {
                            borderTopEndRadius: string | undefined;
                        };
                        _last: {
                            borderBottomEndRadius: string | undefined;
                            mt: string;
                            borderTopWidth: number;
                        };
                    };
                };
                sm: {
                    field: any;
                    stepper: {
                        fontSize: string;
                        _first: {
                            borderTopEndRadius: string | undefined;
                        };
                        _last: {
                            borderBottomEndRadius: string | undefined;
                            mt: string;
                            borderTopWidth: number;
                        };
                    };
                };
                md: {
                    field: any;
                    stepper: {
                        fontSize: string;
                        _first: {
                            borderTopEndRadius: string | undefined;
                        };
                        _last: {
                            borderBottomEndRadius: string | undefined;
                            mt: string;
                            borderTopWidth: number;
                        };
                    };
                };
                lg: {
                    field: any;
                    stepper: {
                        fontSize: string;
                        _first: {
                            borderTopEndRadius: string | undefined;
                        };
                        _last: {
                            borderBottomEndRadius: string | undefined;
                            mt: string;
                            borderTopWidth: number;
                        };
                    };
                };
            } | undefined;
            variants?: {
                outline: (props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                    field: {
                        border: string;
                        borderColor: string;
                        bg: string;
                        _hover: {
                            borderColor: string;
                        };
                        _readOnly: {
                            boxShadow: string;
                            userSelect: string;
                        };
                        _invalid: {
                            borderColor: any;
                            boxShadow: string;
                        };
                        _focusVisible: {
                            zIndex: number;
                            borderColor: any;
                            boxShadow: string;
                        };
                    };
                    addon: {
                        border: string;
                        borderColor: string;
                        bg: string;
                    };
                };
                filled: (props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                    field: {
                        border: string;
                        borderColor: string;
                        bg: string;
                        _hover: {
                            bg: string;
                        };
                        _readOnly: {
                            boxShadow: string;
                            userSelect: string;
                        };
                        _invalid: {
                            borderColor: any;
                        };
                        _focusVisible: {
                            bg: string;
                            borderColor: any;
                        };
                    };
                    addon: {
                        border: string;
                        borderColor: string;
                        bg: string;
                    };
                };
                flushed: (props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                    field: {
                        borderBottom: string;
                        borderColor: string;
                        borderRadius: string;
                        px: string;
                        bg: string;
                        _readOnly: {
                            boxShadow: string;
                            userSelect: string;
                        };
                        _invalid: {
                            borderColor: any;
                            boxShadow: string;
                        };
                        _focusVisible: {
                            borderColor: any;
                            boxShadow: string;
                        };
                    };
                    addon: {
                        borderBottom: string;
                        borderColor: string;
                        borderRadius: string;
                        px: string;
                        bg: string;
                    };
                };
                unstyled: {
                    field: {
                        bg: string;
                        px: string;
                        height: string;
                    };
                    addon: {
                        bg: string;
                        px: string;
                        height: string;
                    };
                };
            } | undefined;
            defaultProps?: {
                size?: "md" | "xs" | "sm" | "lg" | undefined;
                variant?: "outline" | "filled" | "unstyled" | "flushed" | undefined;
                colorScheme?: string | undefined;
            } | undefined;
            parts: ("root" | "field" | "stepperGroup" | "stepper")[];
        };
        PinInput: {
            baseStyle?: {
                textAlign: string;
                width?: string | undefined;
                height?: string | undefined;
                fontSize?: string | undefined;
                px?: string | undefined;
                borderRadius?: string | undefined;
                minWidth?: number | undefined;
                outline?: number | undefined;
                position?: string | undefined;
                appearance?: string | undefined;
                transitionProperty?: string | undefined;
                transitionDuration?: string | undefined;
                _disabled?: {
                    opacity: number;
                    cursor: string;
                } | undefined;
            } | undefined;
            sizes?: {
                lg: {
                    fontSize: string;
                    w: number;
                    h: number;
                    borderRadius: string;
                };
                md: {
                    fontSize: string;
                    w: number;
                    h: number;
                    borderRadius: string;
                };
                sm: {
                    fontSize: string;
                    w: number;
                    h: number;
                    borderRadius: string;
                };
                xs: {
                    fontSize: string;
                    w: number;
                    h: number;
                    borderRadius: string;
                };
            } | undefined;
            variants?: {
                outline: (props: import("@chakra-ui/styled-system").StyleFunctionProps) => {};
                flushed: (props: import("@chakra-ui/styled-system").StyleFunctionProps) => {};
                filled: (props: import("@chakra-ui/styled-system").StyleFunctionProps) => {};
                unstyled: {};
            } | undefined;
            defaultProps?: {
                size?: "md" | "xs" | "sm" | "lg" | undefined;
                variant?: "outline" | "filled" | "unstyled" | "flushed" | undefined;
                colorScheme?: string | undefined;
            } | undefined;
        };
        Popover: {
            baseStyle?: {
                popper: {
                    zIndex: string;
                };
                content: {
                    [x: string]: string | {
                        [x: string]: string;
                        outline?: undefined;
                        boxShadow?: undefined;
                    } | {
                        outline: number;
                        boxShadow: string;
                    };
                    bg: string;
                    _dark: {
                        [x: string]: string;
                    };
                    width: string;
                    border: string;
                    borderColor: string;
                    borderRadius: string;
                    boxShadow: string;
                    zIndex: string;
                    _focusVisible: {
                        outline: number;
                        boxShadow: string;
                    };
                };
                header: {
                    px: number;
                    py: number;
                    borderBottomWidth: string;
                };
                body: {
                    px: number;
                    py: number;
                };
                footer: {
                    px: number;
                    py: number;
                    borderTopWidth: string;
                };
                closeButton: {
                    position: string;
                    borderRadius: string;
                    top: number;
                    insetEnd: number;
                    padding: number;
                };
            } | undefined;
            sizes?: {
                [key: string]: import("@chakra-ui/styled-system").PartsStyleInterpolation<{
                    keys: ("content" | "header" | "body" | "footer" | "closeButton" | "popper" | "arrow")[];
                }>;
            } | undefined;
            variants?: {
                [key: string]: import("@chakra-ui/styled-system").PartsStyleInterpolation<{
                    keys: ("content" | "header" | "body" | "footer" | "closeButton" | "popper" | "arrow")[];
                }>;
            } | undefined;
            defaultProps?: {
                size?: string | number | undefined;
                variant?: string | number | undefined;
                colorScheme?: string | undefined;
            } | undefined;
            parts: ("content" | "header" | "body" | "footer" | "closeButton" | "popper" | "arrow")[];
        };
        Progress: {
            baseStyle?: ((props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                label: {
                    lineHeight: string;
                    fontSize: string;
                    fontWeight: string;
                    color: string;
                };
                filledTrack: any;
                track: {
                    bg: string;
                };
            }) | undefined;
            sizes?: {
                xs: {
                    track: {
                        h: string;
                    };
                };
                sm: {
                    track: {
                        h: string;
                    };
                };
                md: {
                    track: {
                        h: string;
                    };
                };
                lg: {
                    track: {
                        h: string;
                    };
                };
            } | undefined;
            variants?: {
                [key: string]: import("@chakra-ui/styled-system").PartsStyleInterpolation<{
                    keys: ("label" | "track" | "filledTrack")[];
                }>;
            } | undefined;
            defaultProps?: {
                size?: "md" | "xs" | "sm" | "lg" | undefined;
                variant?: string | number | undefined;
                colorScheme?: string | undefined;
            } | undefined;
            parts: ("label" | "track" | "filledTrack")[];
        };
        Radio: {
            baseStyle?: ((props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                label: {
                    userSelect: string;
                    _disabled: {
                        opacity: number;
                    };
                } | undefined;
                container: {
                    _disabled: {
                        cursor: string;
                    };
                } | undefined;
                control: {
                    borderRadius: string;
                    _checked: {
                        _before: {
                            content: string;
                            display: string;
                            pos: string;
                            w: string;
                            h: string;
                            borderRadius: string;
                            bg: string;
                        };
                        bg?: string | undefined;
                        borderColor?: string | undefined;
                        color?: string | undefined;
                        _hover?: {
                            bg: string;
                            borderColor: string;
                        } | undefined;
                        _disabled?: {
                            borderColor: string;
                            bg: string;
                            color: string;
                        } | undefined;
                    };
                    w?: string | undefined;
                    h?: string | undefined;
                    transitionProperty?: string | undefined;
                    transitionDuration?: string | undefined;
                    border?: string | undefined;
                    borderColor?: string | undefined;
                    color?: string | undefined;
                    _indeterminate?: {
                        bg: string;
                        borderColor: string;
                        color: string;
                    } | undefined;
                    _disabled?: {
                        bg: string;
                        borderColor: string;
                    } | undefined;
                    _focusVisible?: {
                        boxShadow: string;
                    } | undefined;
                    _invalid?: {
                        borderColor: string;
                    } | undefined;
                };
            }) | undefined;
            sizes?: {
                md: {
                    control: {
                        w: string;
                        h: string;
                    };
                    label: {
                        fontSize: string;
                    };
                };
                lg: {
                    control: {
                        w: string;
                        h: string;
                    };
                    label: {
                        fontSize: string;
                    };
                };
                sm: {
                    control: {
                        width: string;
                        height: string;
                    };
                    label: {
                        fontSize: string;
                    };
                };
            } | undefined;
            variants?: {
                [key: string]: import("@chakra-ui/styled-system").PartsStyleInterpolation<{
                    keys: ("container" | "label" | "control")[];
                }>;
            } | undefined;
            defaultProps?: {
                size?: "md" | "sm" | "lg" | undefined;
                variant?: string | number | undefined;
                colorScheme?: string | undefined;
            } | undefined;
            parts: ("container" | "label" | "control")[];
        };
        Select: {
            baseStyle?: {
                field: {
                    appearance: string;
                    paddingBottom: string;
                    lineHeight: string;
                    bg: string;
                    _dark: {
                        [x: string]: string;
                    };
                    "> option, > optgroup": {
                        bg: string;
                    };
                    width?: string | undefined;
                    height?: string | undefined;
                    fontSize?: string | undefined;
                    px?: string | undefined;
                    borderRadius?: string | undefined;
                    minWidth?: number | undefined;
                    outline?: number | undefined;
                    position?: string | undefined;
                    transitionProperty?: string | undefined;
                    transitionDuration?: string | undefined;
                    _disabled?: {
                        opacity: number;
                        cursor: string;
                    } | undefined;
                };
                icon: {
                    width: string;
                    height: string;
                    insetEnd: string;
                    position: string;
                    color: string;
                    fontSize: string;
                    _disabled: {
                        opacity: number;
                    };
                };
            } | undefined;
            sizes?: {
                lg: {
                    field: {
                        paddingInlineEnd: string;
                    };
                    group?: {
                        [x: string]: string;
                    } | undefined;
                };
                md: {
                    field: {
                        paddingInlineEnd: string;
                    };
                    group?: {
                        [x: string]: string;
                    } | undefined;
                };
                sm: {
                    field: {
                        paddingInlineEnd: string;
                    };
                    group?: {
                        [x: string]: string;
                    } | undefined;
                };
                xs: {
                    field: {
                        paddingInlineEnd: string;
                    };
                    icon: {
                        insetEnd: string;
                    };
                    group?: {
                        [x: string]: string;
                    } | undefined;
                };
            } | undefined;
            variants?: {
                outline: (props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                    field: {
                        border: string;
                        borderColor: string;
                        bg: string;
                        _hover: {
                            borderColor: string;
                        };
                        _readOnly: {
                            boxShadow: string;
                            userSelect: string;
                        };
                        _invalid: {
                            borderColor: any;
                            boxShadow: string;
                        };
                        _focusVisible: {
                            zIndex: number;
                            borderColor: any;
                            boxShadow: string;
                        };
                    };
                    addon: {
                        border: string;
                        borderColor: string;
                        bg: string;
                    };
                };
                filled: (props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                    field: {
                        border: string;
                        borderColor: string;
                        bg: string;
                        _hover: {
                            bg: string;
                        };
                        _readOnly: {
                            boxShadow: string;
                            userSelect: string;
                        };
                        _invalid: {
                            borderColor: any;
                        };
                        _focusVisible: {
                            bg: string;
                            borderColor: any;
                        };
                    };
                    addon: {
                        border: string;
                        borderColor: string;
                        bg: string;
                    };
                };
                flushed: (props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                    field: {
                        borderBottom: string;
                        borderColor: string;
                        borderRadius: string;
                        px: string;
                        bg: string;
                        _readOnly: {
                            boxShadow: string;
                            userSelect: string;
                        };
                        _invalid: {
                            borderColor: any;
                            boxShadow: string;
                        };
                        _focusVisible: {
                            borderColor: any;
                            boxShadow: string;
                        };
                    };
                    addon: {
                        borderBottom: string;
                        borderColor: string;
                        borderRadius: string;
                        px: string;
                        bg: string;
                    };
                };
                unstyled: {
                    field: {
                        bg: string;
                        px: string;
                        height: string;
                    };
                    addon: {
                        bg: string;
                        px: string;
                        height: string;
                    };
                };
            } | undefined;
            defaultProps?: {
                size?: "md" | "xs" | "sm" | "lg" | undefined;
                variant?: "outline" | "filled" | "unstyled" | "flushed" | undefined;
                colorScheme?: string | undefined;
            } | undefined;
            parts: ("icon" | "field")[];
        };
        Skeleton: {
            baseStyle?: {
                [x: string]: string | number | {
                    [x: string]: string;
                };
                _dark: {
                    [x: string]: string;
                };
                background: string;
                borderColor: string;
                opacity: number;
                borderRadius: string;
            } | undefined;
            sizes?: {
                [key: string]: import("@chakra-ui/styled-system").SystemStyleInterpolation;
            } | undefined;
            variants?: {
                [key: string]: import("@chakra-ui/styled-system").SystemStyleInterpolation;
            } | undefined;
            defaultProps?: {
                size?: string | number | undefined;
                variant?: string | number | undefined;
                colorScheme?: string | undefined;
            } | undefined;
        };
        SkipLink: {
            baseStyle?: {
                borderRadius: string;
                fontWeight: string;
                _focusVisible: {
                    [x: string]: string | {
                        [x: string]: string;
                    };
                    boxShadow: string;
                    padding: string;
                    position: string;
                    top: string;
                    insetStart: string;
                    _dark: {
                        [x: string]: string;
                    };
                    bg: string;
                };
            } | undefined;
            sizes?: {
                [key: string]: import("@chakra-ui/styled-system").SystemStyleInterpolation;
            } | undefined;
            variants?: {
                [key: string]: import("@chakra-ui/styled-system").SystemStyleInterpolation;
            } | undefined;
            defaultProps?: {
                size?: string | number | undefined;
                variant?: string | number | undefined;
                colorScheme?: string | undefined;
            } | undefined;
        };
        Slider: {
            baseStyle?: ((props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                container: {
                    display: string;
                    position: string;
                    cursor: string;
                    _disabled: {
                        opacity: number;
                        cursor: string;
                        pointerEvents: string;
                    };
                } | {
                    h: string;
                    px: string;
                    w?: undefined;
                    py?: undefined;
                    display: string;
                    position: string;
                    cursor: string;
                    _disabled: {
                        opacity: number;
                        cursor: string;
                        pointerEvents: string;
                    };
                } | {
                    w: string;
                    py: string;
                    h?: undefined;
                    px?: undefined;
                    display: string;
                    position: string;
                    cursor: string;
                    _disabled: {
                        opacity: number;
                        cursor: string;
                        pointerEvents: string;
                    };
                };
                track: {
                    overflow: string;
                    borderRadius: string;
                    _dark: {
                        [x: string]: string;
                    };
                    _disabled: {
                        [x: string]: string | {
                            [x: string]: string;
                        };
                        _dark: {
                            [x: string]: string;
                        };
                    };
                    bg: string;
                } | {
                    overflow: string;
                    borderRadius: string;
                    _dark: {
                        [x: string]: string;
                    };
                    _disabled: {
                        [x: string]: string | {
                            [x: string]: string;
                        };
                        _dark: {
                            [x: string]: string;
                        };
                    };
                    bg: string;
                    h: string;
                    w?: undefined;
                } | {
                    overflow: string;
                    borderRadius: string;
                    _dark: {
                        [x: string]: string;
                    };
                    _disabled: {
                        [x: string]: string | {
                            [x: string]: string;
                        };
                        _dark: {
                            [x: string]: string;
                        };
                    };
                    bg: string;
                    w: string;
                    h?: undefined;
                };
                thumb: {
                    w: string;
                    h: string;
                    display: string;
                    alignItems: string;
                    justifyContent: string;
                    position: string;
                    outline: number;
                    zIndex: number;
                    borderRadius: string;
                    bg: string;
                    boxShadow: string;
                    border: string;
                    borderColor: string;
                    transitionProperty: string;
                    transitionDuration: string;
                    _focusVisible: {
                        boxShadow: string;
                    };
                    _disabled: {
                        bg: string;
                    };
                } | {
                    w: string;
                    h: string;
                    display: string;
                    alignItems: string;
                    justifyContent: string;
                    position: string;
                    outline: number;
                    zIndex: number;
                    borderRadius: string;
                    bg: string;
                    boxShadow: string;
                    border: string;
                    borderColor: string;
                    transitionProperty: string;
                    transitionDuration: string;
                    _focusVisible: {
                        boxShadow: string;
                    };
                    _disabled: {
                        bg: string;
                    };
                    left: string;
                    transform: string;
                    _active: {
                        transform: string;
                    };
                    top?: undefined;
                } | {
                    w: string;
                    h: string;
                    display: string;
                    alignItems: string;
                    justifyContent: string;
                    position: string;
                    outline: number;
                    zIndex: number;
                    borderRadius: string;
                    bg: string;
                    boxShadow: string;
                    border: string;
                    borderColor: string;
                    transitionProperty: string;
                    transitionDuration: string;
                    _focusVisible: {
                        boxShadow: string;
                    };
                    _disabled: {
                        bg: string;
                    };
                    top: string;
                    transform: string;
                    _active: {
                        transform: string;
                    };
                    left?: undefined;
                };
                filledTrack: {
                    [x: string]: string | {
                        [x: string]: string;
                    };
                    width: string;
                    height: string;
                    _dark: {
                        [x: string]: string;
                    };
                    bg: string;
                };
            }) | undefined;
            sizes?: {
                lg: {
                    container: {
                        [x: string]: string;
                    };
                };
                md: {
                    container: {
                        [x: string]: string;
                    };
                };
                sm: {
                    container: {
                        [x: string]: string;
                    };
                };
            } | undefined;
            variants?: {
                [key: string]: import("@chakra-ui/styled-system").PartsStyleInterpolation<{
                    keys: ("container" | "track" | "filledTrack" | "thumb" | "mark")[];
                }>;
            } | undefined;
            defaultProps?: {
                size?: "md" | "sm" | "lg" | undefined;
                variant?: string | number | undefined;
                colorScheme?: string | undefined;
            } | undefined;
            parts: ("container" | "track" | "filledTrack" | "thumb" | "mark")[];
        };
        Spinner: {
            baseStyle?: {
                width: string[];
                height: string[];
            } | undefined;
            sizes?: {
                xs: {
                    [x: string]: string;
                };
                sm: {
                    [x: string]: string;
                };
                md: {
                    [x: string]: string;
                };
                lg: {
                    [x: string]: string;
                };
                xl: {
                    [x: string]: string;
                };
            } | undefined;
            variants?: {
                [key: string]: import("@chakra-ui/styled-system").SystemStyleInterpolation;
            } | undefined;
            defaultProps?: {
                size?: "md" | "xs" | "sm" | "lg" | "xl" | undefined;
                variant?: string | number | undefined;
                colorScheme?: string | undefined;
            } | undefined;
        };
        Stat: {
            baseStyle?: {
                container: {};
                label: {
                    fontWeight: string;
                };
                helpText: {
                    opacity: number;
                    marginBottom: string;
                };
                number: {
                    verticalAlign: string;
                    fontWeight: string;
                };
                icon: {
                    marginEnd: number;
                    w: string;
                    h: string;
                    verticalAlign: string;
                };
            } | undefined;
            sizes?: {
                md: {
                    label: {
                        fontSize: string;
                    };
                    helpText: {
                        fontSize: string;
                    };
                    number: {
                        fontSize: string;
                    };
                };
            } | undefined;
            variants?: {
                [key: string]: import("@chakra-ui/styled-system").PartsStyleInterpolation<{
                    keys: ("number" | "container" | "icon" | "label" | "helpText")[];
                }>;
            } | undefined;
            defaultProps?: {
                size?: "md" | undefined;
                variant?: string | number | undefined;
                colorScheme?: string | undefined;
            } | undefined;
            parts: ("number" | "container" | "icon" | "label" | "helpText")[];
        };
        Switch: {
            baseStyle?: ((props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                container: {
                    [x: string]: string | {
                        [x: string]: string;
                    };
                    _rtl: {
                        [x: string]: string;
                    };
                };
                track: {
                    [x: string]: string | string[] | {
                        [x: string]: string;
                        boxShadow?: undefined;
                        opacity?: undefined;
                        cursor?: undefined;
                        _dark?: undefined;
                    } | {
                        boxShadow: string;
                        opacity?: undefined;
                        cursor?: undefined;
                        _dark?: undefined;
                    } | {
                        opacity: number;
                        cursor: string;
                        boxShadow?: undefined;
                        _dark?: undefined;
                    } | {
                        [x: string]: string | {
                            [x: string]: string;
                        };
                        _dark: {
                            [x: string]: string;
                        };
                        boxShadow?: undefined;
                        opacity?: undefined;
                        cursor?: undefined;
                    };
                    borderRadius: string;
                    p: string;
                    width: string[];
                    height: string[];
                    transitionProperty: string;
                    transitionDuration: string;
                    _dark: {
                        [x: string]: string;
                    };
                    _focusVisible: {
                        boxShadow: string;
                    };
                    _disabled: {
                        opacity: number;
                        cursor: string;
                    };
                    _checked: {
                        [x: string]: string | {
                            [x: string]: string;
                        };
                        _dark: {
                            [x: string]: string;
                        };
                    };
                    bg: string;
                };
                thumb: {
                    bg: string;
                    transitionProperty: string;
                    transitionDuration: string;
                    borderRadius: string;
                    width: string[];
                    height: string[];
                    _checked: {
                        transform: string;
                    };
                };
            }) | undefined;
            sizes?: {
                sm: {
                    container: {
                        [x: string]: string;
                    };
                };
                md: {
                    container: {
                        [x: string]: string;
                    };
                };
                lg: {
                    container: {
                        [x: string]: string;
                    };
                };
            } | undefined;
            variants?: {
                [key: string]: import("@chakra-ui/styled-system").PartsStyleInterpolation<{
                    keys: ("container" | "label" | "track" | "thumb")[];
                }>;
            } | undefined;
            defaultProps?: {
                size?: "md" | "sm" | "lg" | undefined;
                variant?: string | number | undefined;
                colorScheme?: string | undefined;
            } | undefined;
            parts: ("container" | "label" | "track" | "thumb")[];
        };
        Table: {
            baseStyle?: {
                table: {
                    fontVariantNumeric: string;
                    borderCollapse: string;
                    width: string;
                };
                th: {
                    fontFamily: string;
                    fontWeight: string;
                    textTransform: string;
                    letterSpacing: string;
                    textAlign: string;
                };
                td: {
                    textAlign: string;
                };
                caption: {
                    mt: number;
                    fontFamily: string;
                    textAlign: string;
                    fontWeight: string;
                };
            } | undefined;
            sizes?: {
                sm: {
                    th: {
                        px: string;
                        py: string;
                        lineHeight: string;
                        fontSize: string;
                    };
                    td: {
                        px: string;
                        py: string;
                        fontSize: string;
                        lineHeight: string;
                    };
                    caption: {
                        px: string;
                        py: string;
                        fontSize: string;
                    };
                };
                md: {
                    th: {
                        px: string;
                        py: string;
                        lineHeight: string;
                        fontSize: string;
                    };
                    td: {
                        px: string;
                        py: string;
                        lineHeight: string;
                    };
                    caption: {
                        px: string;
                        py: string;
                        fontSize: string;
                    };
                };
                lg: {
                    th: {
                        px: string;
                        py: string;
                        lineHeight: string;
                        fontSize: string;
                    };
                    td: {
                        px: string;
                        py: string;
                        lineHeight: string;
                    };
                    caption: {
                        px: string;
                        py: string;
                        fontSize: string;
                    };
                };
            } | undefined;
            variants?: {
                simple: (props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                    th: {
                        "&[data-is-numeric=true]": {
                            textAlign: string;
                        };
                        color: string;
                        borderBottom: string;
                        borderColor: string;
                    };
                    td: {
                        "&[data-is-numeric=true]": {
                            textAlign: string;
                        };
                        borderBottom: string;
                        borderColor: string;
                    };
                    caption: {
                        color: string;
                    };
                    tfoot: {
                        tr: {
                            "&:last-of-type": {
                                th: {
                                    borderBottomWidth: number;
                                };
                            };
                        };
                    };
                };
                striped: (props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                    th: {
                        "&[data-is-numeric=true]": {
                            textAlign: string;
                        };
                        color: string;
                        borderBottom: string;
                        borderColor: string;
                    };
                    td: {
                        "&[data-is-numeric=true]": {
                            textAlign: string;
                        };
                        borderBottom: string;
                        borderColor: string;
                    };
                    caption: {
                        color: string;
                    };
                    tbody: {
                        tr: {
                            "&:nth-of-type(odd)": {
                                "th, td": {
                                    borderBottomWidth: string;
                                    borderColor: string;
                                };
                                td: {
                                    background: string;
                                };
                            };
                        };
                    };
                    tfoot: {
                        tr: {
                            "&:last-of-type": {
                                th: {
                                    borderBottomWidth: number;
                                };
                            };
                        };
                    };
                };
                unstyled: {};
            } | undefined;
            defaultProps?: {
                size?: "md" | "sm" | "lg" | undefined;
                variant?: "unstyled" | "simple" | "striped" | undefined;
                colorScheme?: string | undefined;
            } | undefined;
            parts: ("table" | "caption" | "thead" | "tbody" | "tr" | "th" | "td" | "tfoot")[];
        };
        Tabs: {
            baseStyle?: ((props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                root: {
                    display: string;
                };
                tab: {
                    flex: number | undefined;
                    transitionProperty: string;
                    transitionDuration: string;
                    _focusVisible: {
                        zIndex: number;
                        boxShadow: string;
                    };
                    _disabled: {
                        cursor: string;
                        opacity: number;
                    };
                };
                tablist: {
                    justifyContent: string;
                    flexDirection: string;
                };
                tabpanel: {
                    p: number;
                };
            }) | undefined;
            sizes?: {
                sm: {
                    tab: {
                        py: number;
                        px: number;
                        fontSize: string;
                    };
                };
                md: {
                    tab: {
                        fontSize: string;
                        py: number;
                        px: number;
                    };
                };
                lg: {
                    tab: {
                        fontSize: string;
                        py: number;
                        px: number;
                    };
                };
            } | undefined;
            variants?: {
                line: (props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                    tablist: {
                        [x: string]: string;
                        borderColor: string;
                    };
                    tab: {
                        [x: string]: string | {
                            [x: string]: string | {
                                [x: string]: string;
                            };
                            _dark: {
                                [x: string]: string;
                            };
                            borderColor: string;
                            _active?: undefined;
                        } | {
                            [x: string]: string | {
                                [x: string]: string;
                            };
                            _dark: {
                                [x: string]: string;
                            };
                            borderColor?: undefined;
                            _active?: undefined;
                        } | {
                            _active: {
                                bg: string;
                            };
                            _dark?: undefined;
                            borderColor?: undefined;
                        };
                        borderColor: string;
                        _selected: {
                            [x: string]: string | {
                                [x: string]: string;
                            };
                            _dark: {
                                [x: string]: string;
                            };
                            borderColor: string;
                        };
                        _active: {
                            [x: string]: string | {
                                [x: string]: string;
                            };
                            _dark: {
                                [x: string]: string;
                            };
                        };
                        _disabled: {
                            _active: {
                                bg: string;
                            };
                        };
                        color: string;
                        bg: string;
                    };
                };
                enclosed: (props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                    tab: {
                        [x: string]: string | {
                            [x: string]: string | {
                                [x: string]: string;
                            };
                            _dark: {
                                [x: string]: string;
                            };
                            borderColor: string;
                            borderBottomColor: string;
                        };
                        borderTopRadius: string;
                        border: string;
                        borderColor: string;
                        mb: string;
                        _selected: {
                            [x: string]: string | {
                                [x: string]: string;
                            };
                            _dark: {
                                [x: string]: string;
                            };
                            borderColor: string;
                            borderBottomColor: string;
                        };
                        color: string;
                    };
                    tablist: {
                        mb: string;
                        borderBottom: string;
                        borderColor: string;
                    };
                };
                "enclosed-colored": (props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                    tab: {
                        [x: string]: string | {
                            [x: string]: string;
                            marginEnd?: undefined;
                            _dark?: undefined;
                            borderColor?: undefined;
                            borderTopColor?: undefined;
                            borderBottomColor?: undefined;
                        } | {
                            marginEnd: string;
                            _dark?: undefined;
                            borderColor?: undefined;
                            borderTopColor?: undefined;
                            borderBottomColor?: undefined;
                        } | {
                            [x: string]: string | {
                                [x: string]: string;
                            };
                            _dark: {
                                [x: string]: string;
                            };
                            borderColor: string;
                            borderTopColor: string;
                            borderBottomColor: string;
                            marginEnd?: undefined;
                        };
                        border: string;
                        borderColor: string;
                        _dark: {
                            [x: string]: string;
                        };
                        mb: string;
                        _notLast: {
                            marginEnd: string;
                        };
                        _selected: {
                            [x: string]: string | {
                                [x: string]: string;
                            };
                            _dark: {
                                [x: string]: string;
                            };
                            borderColor: string;
                            borderTopColor: string;
                            borderBottomColor: string;
                        };
                        color: string;
                        bg: string;
                    };
                    tablist: {
                        mb: string;
                        borderBottom: string;
                        borderColor: string;
                    };
                };
                "soft-rounded": (props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                    tab: {
                        borderRadius: string;
                        fontWeight: string;
                        color: string;
                        _selected: {
                            color: any;
                            bg: any;
                        };
                    };
                };
                "solid-rounded": (props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                    tab: {
                        [x: string]: string | {
                            [x: string]: string;
                            _dark?: undefined;
                        } | {
                            [x: string]: string | {
                                [x: string]: string;
                            };
                            _dark: {
                                [x: string]: string;
                            };
                        };
                        borderRadius: string;
                        fontWeight: string;
                        _dark: {
                            [x: string]: string;
                        };
                        _selected: {
                            [x: string]: string | {
                                [x: string]: string;
                            };
                            _dark: {
                                [x: string]: string;
                            };
                        };
                        color: string;
                        bg: string;
                    };
                };
                unstyled: {};
            } | undefined;
            defaultProps?: {
                size?: "md" | "sm" | "lg" | undefined;
                variant?: "unstyled" | "line" | "enclosed" | "enclosed-colored" | "soft-rounded" | "solid-rounded" | undefined;
                colorScheme?: string | undefined;
            } | undefined;
            parts: ("root" | "tab" | "tabpanel" | "tabpanels" | "indicator" | "tablist")[];
        };
        Tag: {
            baseStyle?: {
                container: {
                    [x: string]: string | number | {
                        [x: string]: string;
                    };
                    fontWeight: string;
                    lineHeight: number;
                    outline: number;
                    color: string;
                    bg: string;
                    boxShadow: string;
                    borderRadius: string;
                    minH: string;
                    minW: string;
                    fontSize: string;
                    px: string;
                    _focusVisible: {
                        [x: string]: string;
                    };
                };
                label: {
                    lineHeight: number;
                    overflow: string;
                };
                closeButton: {
                    fontSize: string;
                    w: string;
                    h: string;
                    transitionProperty: string;
                    transitionDuration: string;
                    borderRadius: string;
                    marginStart: string;
                    marginEnd: string;
                    opacity: number;
                    _disabled: {
                        opacity: number;
                    };
                    _focusVisible: {
                        boxShadow: string;
                        bg: string;
                    };
                    _hover: {
                        opacity: number;
                    };
                    _active: {
                        opacity: number;
                    };
                };
            } | undefined;
            sizes?: {
                sm: {
                    container: {
                        [x: string]: string;
                    };
                    closeButton: {
                        marginEnd: string;
                        marginStart: string;
                    };
                };
                md: {
                    container: {
                        [x: string]: string;
                    };
                };
                lg: {
                    container: {
                        [x: string]: string;
                    };
                };
            } | undefined;
            variants?: {
                subtle: (props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                    container: {
                        [x: string]: string | {
                            [x: string]: string;
                        };
                        _dark: {
                            [x: string]: string;
                        };
                    } | undefined;
                };
                solid: (props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                    container: {
                        [x: string]: string | {
                            [x: string]: string;
                        };
                        _dark: {
                            [x: string]: string;
                        };
                    } | undefined;
                };
                outline: (props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                    container: {
                        [x: string]: string | {
                            [x: string]: string;
                        };
                        _dark: {
                            [x: string]: string;
                        };
                    } | undefined;
                };
            } | undefined;
            defaultProps?: {
                size?: "md" | "sm" | "lg" | undefined;
                variant?: "outline" | "solid" | "subtle" | undefined;
                colorScheme?: string | undefined;
            } | undefined;
            parts: ("container" | "label" | "closeButton")[];
        };
        Textarea: {
            baseStyle?: {
                paddingY: string;
                minHeight: string;
                lineHeight: string;
                verticalAlign: string;
                width?: string | undefined;
                height?: string | undefined;
                fontSize?: string | undefined;
                px?: string | undefined;
                borderRadius?: string | undefined;
                minWidth?: number | undefined;
                outline?: number | undefined;
                position?: string | undefined;
                appearance?: string | undefined;
                transitionProperty?: string | undefined;
                transitionDuration?: string | undefined;
                _disabled?: {
                    opacity: number;
                    cursor: string;
                } | undefined;
            } | undefined;
            sizes?: {
                xs: {
                    [x: string]: string;
                };
                sm: {
                    [x: string]: string;
                };
                md: {
                    [x: string]: string;
                };
                lg: {
                    [x: string]: string;
                };
            } | undefined;
            variants?: {
                outline: (props: import("@chakra-ui/styled-system").StyleFunctionProps) => {};
                flushed: (props: import("@chakra-ui/styled-system").StyleFunctionProps) => {};
                filled: (props: import("@chakra-ui/styled-system").StyleFunctionProps) => {};
                unstyled: {};
            } | undefined;
            defaultProps?: {
                size?: "md" | "xs" | "sm" | "lg" | undefined;
                variant?: "outline" | "filled" | "unstyled" | "flushed" | undefined;
                colorScheme?: string | undefined;
            } | undefined;
        };
        Tooltip: {
            baseStyle?: {
                [x: string]: string | {
                    [x: string]: string;
                };
                bg: string;
                color: string;
                _dark: {
                    [x: string]: string;
                };
                px: string;
                py: string;
                borderRadius: string;
                fontWeight: string;
                fontSize: string;
                boxShadow: string;
                maxW: string;
                zIndex: string;
            } | undefined;
            sizes?: {
                [key: string]: import("@chakra-ui/styled-system").SystemStyleInterpolation;
            } | undefined;
            variants?: {
                [key: string]: import("@chakra-ui/styled-system").SystemStyleInterpolation;
            } | undefined;
            defaultProps?: {
                size?: string | number | undefined;
                variant?: string | number | undefined;
                colorScheme?: string | undefined;
            } | undefined;
        };
        Card: {
            baseStyle?: {
                container: {
                    [x: string]: string;
                    backgroundColor: string;
                    boxShadow: string;
                    borderRadius: string;
                    color: string;
                    borderWidth: string;
                    borderColor: string;
                };
                body: {
                    padding: string;
                    flex: string;
                };
                header: {
                    padding: string;
                };
                footer: {
                    padding: string;
                };
            } | undefined;
            sizes?: {
                sm: {
                    container: {
                        [x: string]: string;
                    };
                };
                md: {
                    container: {
                        [x: string]: string;
                    };
                };
                lg: {
                    container: {
                        [x: string]: string;
                    };
                };
            } | undefined;
            variants?: {
                elevated: {
                    container: {
                        [x: string]: string | {
                            [x: string]: string;
                        };
                        _dark: {
                            [x: string]: string;
                        };
                    };
                };
                outline: {
                    container: {
                        [x: string]: string;
                    };
                };
                filled: {
                    container: {
                        [x: string]: string;
                    };
                };
                unstyled: {
                    body: {
                        [x: string]: number;
                    };
                    header: {
                        [x: string]: number;
                    };
                    footer: {
                        [x: string]: number;
                    };
                };
            } | undefined;
            defaultProps?: {
                size?: "md" | "sm" | "lg" | undefined;
                variant?: "outline" | "filled" | "unstyled" | "elevated" | undefined;
                colorScheme?: string | undefined;
            } | undefined;
            parts: ("container" | "header" | "body" | "footer")[];
        };
        Stepper: {
            baseStyle?: (({ colorScheme: c }: import("@chakra-ui/styled-system").StyleFunctionProps) => {
                stepper: {
                    [x: string]: string | {
                        flexDirection: string;
                        alignItems: string;
                    } | {
                        [x: string]: string;
                        flexDirection?: undefined;
                        alignItems?: undefined;
                    };
                    display: string;
                    justifyContent: string;
                    gap: string;
                    "&[data-orientation=vertical]": {
                        flexDirection: string;
                        alignItems: string;
                    };
                    "&[data-orientation=horizontal]": {
                        flexDirection: string;
                        alignItems: string;
                    };
                    _dark: {
                        [x: string]: string;
                    };
                };
                title: {
                    fontSize: string;
                    fontWeight: string;
                };
                description: {
                    fontSize: string;
                    color: string;
                };
                number: {
                    fontSize: string;
                };
                step: {
                    flexShrink: number;
                    position: string;
                    display: string;
                    gap: string;
                    "&[data-orientation=horizontal]": {
                        alignItems: string;
                    };
                    flex: string;
                    "&:last-of-type:not([data-stretch])": {
                        flex: string;
                    };
                };
                icon: {
                    flexShrink: number;
                    width: string;
                    height: string;
                };
                indicator: {
                    flexShrink: number;
                    borderRadius: string;
                    width: string;
                    height: string;
                    display: string;
                    justifyContent: string;
                    alignItems: string;
                    "&[data-status=active]": {
                        borderWidth: string;
                        borderColor: string;
                    };
                    "&[data-status=complete]": {
                        bg: string;
                        color: string;
                    };
                    "&[data-status=incomplete]": {
                        borderWidth: string;
                    };
                };
                separator: {
                    bg: string;
                    flex: string;
                    "&[data-status=complete]": {
                        bg: string;
                    };
                    "&[data-orientation=horizontal]": {
                        width: string;
                        height: string;
                        marginStart: string;
                    };
                    "&[data-orientation=vertical]": {
                        width: string;
                        position: string;
                        height: string;
                        maxHeight: string;
                        top: string;
                        insetStart: string;
                    };
                };
            }) | undefined;
            sizes?: {
                xs: {
                    stepper: {
                        [x: string]: string;
                    };
                };
                sm: {
                    stepper: {
                        [x: string]: string;
                    };
                };
                md: {
                    stepper: {
                        [x: string]: string;
                    };
                };
                lg: {
                    stepper: {
                        [x: string]: string;
                    };
                };
            } | undefined;
            variants?: {
                [key: string]: import("@chakra-ui/styled-system").PartsStyleInterpolation<{
                    keys: ("number" | "icon" | "separator" | "title" | "description" | "stepper" | "step" | "indicator")[];
                }>;
            } | undefined;
            defaultProps?: {
                size?: "md" | "xs" | "sm" | "lg" | undefined;
                variant?: string | number | undefined;
                colorScheme?: string | undefined;
            } | undefined;
            parts: ("number" | "icon" | "separator" | "title" | "description" | "stepper" | "step" | "indicator")[];
        };
    };
    styles: import("@chakra-ui/theme-tools").Styles;
    config: ThemeConfig;
    sizes: {
        container: {
            sm: string;
            md: string;
            lg: string;
            xl: string;
        };
        max: string;
        min: string;
        full: string;
        "3xs": string;
        "2xs": string;
        xs: string;
        sm: string;
        md: string;
        lg: string;
        xl: string;
        "2xl": string;
        "3xl": string;
        "4xl": string;
        "5xl": string;
        "6xl": string;
        "7xl": string;
        "8xl": string;
        prose: string;
        px: string;
        0.5: string;
        1: string;
        1.5: string;
        2: string;
        2.5: string;
        3: string;
        3.5: string;
        4: string;
        5: string;
        6: string;
        7: string;
        8: string;
        9: string;
        10: string;
        12: string;
        14: string;
        16: string;
        20: string;
        24: string;
        28: string;
        32: string;
        36: string;
        40: string;
        44: string;
        48: string;
        52: string;
        56: string;
        60: string;
        64: string;
        72: string;
        80: string;
        96: string;
    };
    shadows: {
        xs: string;
        sm: string;
        base: string;
        md: string;
        lg: string;
        xl: string;
        "2xl": string;
        outline: string;
        inner: string;
        none: string;
        "dark-lg": string;
    };
    space: {
        px: string;
        0.5: string;
        1: string;
        1.5: string;
        2: string;
        2.5: string;
        3: string;
        3.5: string;
        4: string;
        5: string;
        6: string;
        7: string;
        8: string;
        9: string;
        10: string;
        12: string;
        14: string;
        16: string;
        20: string;
        24: string;
        28: string;
        32: string;
        36: string;
        40: string;
        44: string;
        48: string;
        52: string;
        56: string;
        60: string;
        64: string;
        72: string;
        80: string;
        96: string;
    };
    borders: {
        none: number;
        "1px": string;
        "2px": string;
        "4px": string;
        "8px": string;
    };
    transition: {
        property: {
            common: string;
            colors: string;
            dimensions: string;
            position: string;
            background: string;
        };
        easing: {
            "ease-in": string;
            "ease-out": string;
            "ease-in-out": string;
        };
        duration: {
            "ultra-fast": string;
            faster: string;
            fast: string;
            normal: string;
            slow: string;
            slower: string;
            "ultra-slow": string;
        };
    };
    letterSpacings: {
        tighter: string;
        tight: string;
        normal: string;
        wide: string;
        wider: string;
        widest: string;
    };
    lineHeights: {
        normal: string;
        none: number;
        shorter: number;
        short: number;
        base: number;
        tall: number;
        taller: string;
        "3": string;
        "4": string;
        "5": string;
        "6": string;
        "7": string;
        "8": string;
        "9": string;
        "10": string;
    };
    fontWeights: {
        hairline: number;
        thin: number;
        light: number;
        normal: number;
        medium: number;
        semibold: number;
        bold: number;
        extrabold: number;
        black: number;
    };
    fonts: {
        heading: string;
        body: string;
        mono: string;
    };
    fontSizes: {
        "3xs": string;
        "2xs": string;
        xs: string;
        sm: string;
        md: string;
        lg: string;
        xl: string;
        "2xl": string;
        "3xl": string;
        "4xl": string;
        "5xl": string;
        "6xl": string;
        "7xl": string;
        "8xl": string;
        "9xl": string;
    };
    breakpoints: {
        base: string;
        sm: string;
        md: string;
        lg: string;
        xl: string;
        "2xl": string;
    };
    zIndices: {
        hide: number;
        auto: string;
        base: number;
        docked: number;
        dropdown: number;
        sticky: number;
        banner: number;
        overlay: number;
        modal: number;
        popover: number;
        skipLink: number;
        toast: number;
        tooltip: number;
    };
    radii: {
        none: string;
        sm: string;
        base: string;
        md: string;
        lg: string;
        xl: string;
        "2xl": string;
        "3xl": string;
        full: string;
    };
    blur: {
        none: number;
        sm: string;
        base: string;
        md: string;
        lg: string;
        xl: string;
        "2xl": string;
        "3xl": string;
    };
    colors: {
        transparent: string;
        current: string;
        black: string;
        white: string;
        whiteAlpha: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
        };
        blackAlpha: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
        };
        gray: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
        };
        red: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
        };
        orange: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
        };
        yellow: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
        };
        green: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
        };
        teal: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
        };
        blue: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
        };
        cyan: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
        };
        purple: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
        };
        pink: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
        };
    };
    semanticTokens: {
        colors: {
            "chakra-body-text": {
                _light: string;
                _dark: string;
            };
            "chakra-body-bg": {
                _light: string;
                _dark: string;
            };
            "chakra-border-color": {
                _light: string;
                _dark: string;
            };
            "chakra-inverse-text": {
                _light: string;
                _dark: string;
            };
            "chakra-subtle-bg": {
                _light: string;
                _dark: string;
            };
            "chakra-subtle-text": {
                _light: string;
                _dark: string;
            };
            "chakra-placeholder-color": {
                _light: string;
                _dark: string;
            };
        };
    };
    direction: "ltr";
};
export type Theme = typeof theme;
export * from "./theme.types";
export * from "./utils/is-chakra-theme";
export declare const baseTheme: {
    styles: import("@chakra-ui/theme-tools").Styles;
    config: ThemeConfig;
    sizes: {
        container: {
            sm: string;
            md: string;
            lg: string;
            xl: string;
        };
        max: string;
        min: string;
        full: string;
        "3xs": string;
        "2xs": string;
        xs: string;
        sm: string;
        md: string;
        lg: string;
        xl: string;
        "2xl": string;
        "3xl": string;
        "4xl": string;
        "5xl": string;
        "6xl": string;
        "7xl": string;
        "8xl": string;
        prose: string;
        px: string;
        0.5: string;
        1: string;
        1.5: string;
        2: string;
        2.5: string;
        3: string;
        3.5: string;
        4: string;
        5: string;
        6: string;
        7: string;
        8: string;
        9: string;
        10: string;
        12: string;
        14: string;
        16: string;
        20: string;
        24: string;
        28: string;
        32: string;
        36: string;
        40: string;
        44: string;
        48: string;
        52: string;
        56: string;
        60: string;
        64: string;
        72: string;
        80: string;
        96: string;
    };
    shadows: {
        xs: string;
        sm: string;
        base: string;
        md: string;
        lg: string;
        xl: string;
        "2xl": string;
        outline: string;
        inner: string;
        none: string;
        "dark-lg": string;
    };
    space: {
        px: string;
        0.5: string;
        1: string;
        1.5: string;
        2: string;
        2.5: string;
        3: string;
        3.5: string;
        4: string;
        5: string;
        6: string;
        7: string;
        8: string;
        9: string;
        10: string;
        12: string;
        14: string;
        16: string;
        20: string;
        24: string;
        28: string;
        32: string;
        36: string;
        40: string;
        44: string;
        48: string;
        52: string;
        56: string;
        60: string;
        64: string;
        72: string;
        80: string;
        96: string;
    };
    borders: {
        none: number;
        "1px": string;
        "2px": string;
        "4px": string;
        "8px": string;
    };
    transition: {
        property: {
            common: string;
            colors: string;
            dimensions: string;
            position: string;
            background: string;
        };
        easing: {
            "ease-in": string;
            "ease-out": string;
            "ease-in-out": string;
        };
        duration: {
            "ultra-fast": string;
            faster: string;
            fast: string;
            normal: string;
            slow: string;
            slower: string;
            "ultra-slow": string;
        };
    };
    letterSpacings: {
        tighter: string;
        tight: string;
        normal: string;
        wide: string;
        wider: string;
        widest: string;
    };
    lineHeights: {
        normal: string;
        none: number;
        shorter: number;
        short: number;
        base: number;
        tall: number;
        taller: string;
        "3": string;
        "4": string;
        "5": string;
        "6": string;
        "7": string;
        "8": string;
        "9": string;
        "10": string;
    };
    fontWeights: {
        hairline: number;
        thin: number;
        light: number;
        normal: number;
        medium: number;
        semibold: number;
        bold: number;
        extrabold: number;
        black: number;
    };
    fonts: {
        heading: string;
        body: string;
        mono: string;
    };
    fontSizes: {
        "3xs": string;
        "2xs": string;
        xs: string;
        sm: string;
        md: string;
        lg: string;
        xl: string;
        "2xl": string;
        "3xl": string;
        "4xl": string;
        "5xl": string;
        "6xl": string;
        "7xl": string;
        "8xl": string;
        "9xl": string;
    };
    breakpoints: {
        base: string;
        sm: string;
        md: string;
        lg: string;
        xl: string;
        "2xl": string;
    };
    zIndices: {
        hide: number;
        auto: string;
        base: number;
        docked: number;
        dropdown: number;
        sticky: number;
        banner: number;
        overlay: number;
        modal: number;
        popover: number;
        skipLink: number;
        toast: number;
        tooltip: number;
    };
    radii: {
        none: string;
        sm: string;
        base: string;
        md: string;
        lg: string;
        xl: string;
        "2xl": string;
        "3xl": string;
        full: string;
    };
    blur: {
        none: number;
        sm: string;
        base: string;
        md: string;
        lg: string;
        xl: string;
        "2xl": string;
        "3xl": string;
    };
    colors: {
        transparent: string;
        current: string;
        black: string;
        white: string;
        whiteAlpha: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
        };
        blackAlpha: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
        };
        gray: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
        };
        red: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
        };
        orange: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
        };
        yellow: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
        };
        green: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
        };
        teal: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
        };
        blue: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
        };
        cyan: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
        };
        purple: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
        };
        pink: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
        };
    };
    semanticTokens: {
        colors: {
            "chakra-body-text": {
                _light: string;
                _dark: string;
            };
            "chakra-body-bg": {
                _light: string;
                _dark: string;
            };
            "chakra-border-color": {
                _light: string;
                _dark: string;
            };
            "chakra-inverse-text": {
                _light: string;
                _dark: string;
            };
            "chakra-subtle-bg": {
                _light: string;
                _dark: string;
            };
            "chakra-subtle-text": {
                _light: string;
                _dark: string;
            };
            "chakra-placeholder-color": {
                _light: string;
                _dark: string;
            };
        };
    };
    direction: "ltr";
    components: {};
};
