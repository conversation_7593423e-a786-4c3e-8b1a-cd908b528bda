{"version": 3, "file": "index.module.es.js", "sources": ["../src/guard.ts", "../src/ColorError.ts", "../src/parseToRgba.ts", "../src/parseToHsla.ts", "../src/hsla.ts", "../src/adjustHue.ts", "../src/darken.ts", "../src/desaturate.ts", "../src/getLuminance.ts", "../src/getContrast.ts", "../src/rgba.ts", "../src/mix.ts", "../src/getScale.ts", "../src/hasBadContrast.ts", "../src/lighten.ts", "../src/transparentize.ts", "../src/opacify.ts", "../src/readableColorIsBlack.ts", "../src/readableColor.ts", "../src/saturate.ts", "../src/toHex.ts", "../src/toRgba.ts", "../src/toHsla.ts"], "sourcesContent": ["/**\n * A simple guard function:\n *\n * ```js\n * Math.min(Math.max(low, value), high)\n * ```\n */\nfunction guard(low: number, high: number, value: number): number {\n  return Math.min(Math.max(low, value), high);\n}\n\nexport default guard;\n", "class ColorError extends Error {\n  constructor(color: string) {\n    super(`Failed to parse color: \"${color}\"`);\n  }\n}\n\nexport default ColorError;\n", "import guard from './guard';\nimport ColorError from './ColorError';\n\n/**\n * Parses a color into red, gree, blue, alpha parts\n *\n * @param color the input color. Can be a RGB, RBGA, HSL, HSLA, or named color\n */\nfunction parseToRgba(color: string): [number, number, number, number] {\n  if (typeof color !== 'string') throw new ColorError(color);\n  if (color.trim().toLowerCase() === 'transparent') return [0, 0, 0, 0];\n\n  let normalizedColor = color.trim();\n  normalizedColor = namedColorRegex.test(color) ? nameToHex(color) : color;\n\n  const reducedHexMatch = reducedHexRegex.exec(normalizedColor);\n  if (reducedHexMatch) {\n    const arr = Array.from(reducedHexMatch).slice(1);\n    return [\n      ...arr.slice(0, 3).map((x) => parseInt(r(x, 2), 16)),\n      parseInt(r(arr[3] || 'f', 2), 16) / 255,\n    ] as [number, number, number, number];\n  }\n\n  const hexMatch = hexRegex.exec(normalizedColor);\n  if (hexMatch) {\n    const arr = Array.from(hexMatch).slice(1);\n    return [\n      ...arr.slice(0, 3).map((x) => parseInt(x, 16)),\n      parseInt(arr[3] || 'ff', 16) / 255,\n    ] as [number, number, number, number];\n  }\n\n  const rgbaMatch = rgbaRegex.exec(normalizedColor);\n  if (rgbaMatch) {\n    const arr = Array.from(rgbaMatch).slice(1);\n    return [\n      ...arr.slice(0, 3).map((x) => parseInt(x, 10)),\n      parseFloat(arr[3] || '1'),\n    ] as [number, number, number, number];\n  }\n\n  const hslaMatch = hslaRegex.exec(normalizedColor);\n  if (hslaMatch) {\n    const [h, s, l, a] = Array.from(hslaMatch).slice(1).map(parseFloat);\n    if (guard(0, 100, s) !== s) throw new ColorError(color);\n    if (guard(0, 100, l) !== l) throw new ColorError(color);\n    return [...hslToRgb(h, s, l), Number.isNaN(a) ? 1 : a] as [\n      number,\n      number,\n      number,\n      number\n    ];\n  }\n\n  throw new ColorError(color);\n}\n\nfunction hash(str: string) {\n  let hash = 5381;\n  let i = str.length;\n\n  while (i) {\n    hash = (hash * 33) ^ str.charCodeAt(--i);\n  }\n\n  /* JavaScript does bitwise operations (like XOR, above) on 32-bit signed\n   * integers. Since we want the results to be always positive, convert the\n   * signed int to an unsigned by doing an unsigned bitshift. */\n  return (hash >>> 0) % 2341;\n}\n\nconst colorToInt = (x: string) => parseInt(x.replace(/_/g, ''), 36);\n\nconst compressedColorMap =\n  '1q29ehhb 1n09sgk7 1kl1ekf_ _yl4zsno 16z9eiv3 1p29lhp8 _bd9zg04 17u0____ _iw9zhe5 _to73___ _r45e31e _7l6g016 _jh8ouiv _zn3qba8 1jy4zshs 11u87k0u 1ro9yvyo 1aj3xael 1gz9zjz0 _3w8l4xo 1bf1ekf_ _ke3v___ _4rrkb__ 13j776yz _646mbhl _nrjr4__ _le6mbhl 1n37ehkb _m75f91n _qj3bzfz 1939yygw 11i5z6x8 _1k5f8xs 1509441m 15t5lwgf _ae2th1n _tg1ugcv 1lp1ugcv 16e14up_ _h55rw7n _ny9yavn _7a11xb_ 1ih442g9 _pv442g9 1mv16xof 14e6y7tu 1oo9zkds 17d1cisi _4v9y70f _y98m8kc 1019pq0v 12o9zda8 _348j4f4 1et50i2o _8epa8__ _ts6senj 1o350i2o 1mi9eiuo 1259yrp0 1ln80gnw _632xcoy 1cn9zldc _f29edu4 1n490c8q _9f9ziet 1b94vk74 _m49zkct 1kz6s73a 1eu9dtog _q58s1rz 1dy9sjiq __u89jo3 _aj5nkwg _ld89jo3 13h9z6wx _qa9z2ii _l119xgq _bs5arju 1hj4nwk9 1qt4nwk9 1ge6wau6 14j9zlcw 11p1edc_ _ms1zcxe _439shk6 _jt9y70f _754zsow 1la40eju _oq5p___ _x279qkz 1fa5r3rv _yd2d9ip _424tcku _8y1di2_ _zi2uabw _yy7rn9h 12yz980_ __39ljp6 1b59zg0x _n39zfzp 1fy9zest _b33k___ _hp9wq92 1il50hz4 _io472ub _lj9z3eo 19z9ykg0 _8t8iu3a 12b9bl4a 1ak5yw0o _896v4ku _tb8k8lv _s59zi6t _c09ze0p 1lg80oqn 1id9z8wb _238nba5 1kq6wgdi _154zssg _tn3zk49 _da9y6tc 1sg7cv4f _r12jvtt 1gq5fmkz 1cs9rvci _lp9jn1c _xw1tdnb 13f9zje6 16f6973h _vo7ir40 _bt5arjf _rc45e4t _hr4e100 10v4e100 _hc9zke2 _w91egv_ _sj2r1kk 13c87yx8 _vqpds__ _ni8ggk8 _tj9yqfb 1ia2j4r4 _7x9b10u 1fc9ld4j 1eq9zldr _5j9lhpx _ez9zl6o _md61fzm'\n    .split(' ')\n    .reduce((acc, next) => {\n      const key = colorToInt(next.substring(0, 3));\n      const hex = colorToInt(next.substring(3)).toString(16);\n\n      // NOTE: padStart could be used here but it breaks Node 6 compat\n      // https://github.com/ricokahler/color2k/issues/351\n      let prefix = '';\n      for (let i = 0; i < 6 - hex.length; i++) {\n        prefix += '0';\n      }\n\n      acc[key] = `${prefix}${hex}`;\n      return acc;\n    }, {} as { [key: string]: string });\n\n/**\n * Checks if a string is a CSS named color and returns its equivalent hex value, otherwise returns the original color.\n */\nfunction nameToHex(color: string): string {\n  const normalizedColorName = color.toLowerCase().trim();\n  const result = compressedColorMap[hash(normalizedColorName)];\n  if (!result) throw new ColorError(color);\n  return `#${result}`;\n}\n\nconst r = (str: string, amount: number) =>\n  Array.from(Array(amount))\n    .map(() => str)\n    .join('');\n\nconst reducedHexRegex = new RegExp(`^#${r('([a-f0-9])', 3)}([a-f0-9])?$`, 'i');\nconst hexRegex = new RegExp(`^#${r('([a-f0-9]{2})', 3)}([a-f0-9]{2})?$`, 'i');\nconst rgbaRegex = new RegExp(\n  `^rgba?\\\\(\\\\s*(\\\\d+)\\\\s*${r(\n    ',\\\\s*(\\\\d+)\\\\s*',\n    2\n  )}(?:,\\\\s*([\\\\d.]+))?\\\\s*\\\\)$`,\n  'i'\n);\nconst hslaRegex =\n  /^hsla?\\(\\s*([\\d.]+)\\s*,\\s*([\\d.]+)%\\s*,\\s*([\\d.]+)%(?:\\s*,\\s*([\\d.]+))?\\s*\\)$/i;\nconst namedColorRegex = /^[a-z]+$/i;\n\nconst roundColor = (color: number): number => {\n  return Math.round(color * 255);\n};\n\nconst hslToRgb = (\n  hue: number,\n  saturation: number,\n  lightness: number\n): [number, number, number] => {\n  let l = lightness / 100;\n  if (saturation === 0) {\n    // achromatic\n    return [l, l, l].map(roundColor) as [number, number, number];\n  }\n\n  // formulae from https://en.wikipedia.org/wiki/HSL_and_HSV\n  const huePrime = (((hue % 360) + 360) % 360) / 60;\n  const chroma = (1 - Math.abs(2 * l - 1)) * (saturation / 100);\n  const secondComponent = chroma * (1 - Math.abs((huePrime % 2) - 1));\n\n  let red = 0;\n  let green = 0;\n  let blue = 0;\n\n  if (huePrime >= 0 && huePrime < 1) {\n    red = chroma;\n    green = secondComponent;\n  } else if (huePrime >= 1 && huePrime < 2) {\n    red = secondComponent;\n    green = chroma;\n  } else if (huePrime >= 2 && huePrime < 3) {\n    green = chroma;\n    blue = secondComponent;\n  } else if (huePrime >= 3 && huePrime < 4) {\n    green = secondComponent;\n    blue = chroma;\n  } else if (huePrime >= 4 && huePrime < 5) {\n    red = secondComponent;\n    blue = chroma;\n  } else if (huePrime >= 5 && huePrime < 6) {\n    red = chroma;\n    blue = secondComponent;\n  }\n\n  const lightnessModification = l - chroma / 2;\n  const finalRed = red + lightnessModification;\n  const finalGreen = green + lightnessModification;\n  const finalBlue = blue + lightnessModification;\n\n  return [finalRed, finalGreen, finalBlue].map(roundColor) as [\n    number,\n    number,\n    number\n  ];\n};\n\nexport default parseToRgba;\n", "// taken from:\n// https://github.com/styled-components/polished/blob/a23a6a2bb26802b3d922d9c3b67bac3f3a54a310/src/internalHelpers/_rgbToHsl.js\nimport parseToRgba from './parseToRgba';\n\n/**\n * Parses a color in hue, saturation, lightness, and the alpha channel.\n *\n * Hue is a number between 0 and 360, saturation, lightness, and alpha are\n * decimal percentages between 0 and 1\n */\nfunction parseToHsla(color: string): [number, number, number, number] {\n  const [red, green, blue, alpha] = parseToRgba(color).map((value, index) =>\n    // 3rd index is alpha channel which is already normalized\n    index === 3 ? value : value / 255\n  );\n\n  const max = Math.max(red, green, blue);\n  const min = Math.min(red, green, blue);\n  const lightness = (max + min) / 2;\n\n  // achromatic\n  if (max === min) return [0, 0, lightness, alpha];\n\n  const delta = max - min;\n  const saturation =\n    lightness > 0.5 ? delta / (2 - max - min) : delta / (max + min);\n\n  const hue =\n    60 *\n    (red === max\n      ? (green - blue) / delta + (green < blue ? 6 : 0)\n      : green === max\n      ? (blue - red) / delta + 2\n      : (red - green) / delta + 4);\n\n  return [hue, saturation, lightness, alpha];\n}\n\nexport default parseToHsla;\n", "import guard from './guard';\n\n/**\n * Takes in hsla parts and constructs an hsla string\n *\n * @param hue The color circle (from 0 to 360) - 0 (or 360) is red, 120 is green, 240 is blue\n * @param saturation Percentage of saturation, given as a decimal between 0 and 1\n * @param lightness Percentage of lightness, given as a decimal between 0 and 1\n * @param alpha Percentage of opacity, given as a decimal between 0 and 1\n */\nfunction hsla(\n  hue: number,\n  saturation: number,\n  lightness: number,\n  alpha: number\n): string {\n  return `hsla(${(hue % 360).toFixed()}, ${guard(\n    0,\n    100,\n    saturation * 100\n  ).toFixed()}%, ${guard(0, 100, lightness * 100).toFixed()}%, ${parseFloat(\n    guard(0, 1, alpha).toFixed(3)\n  )})`;\n}\n\nexport default hsla;\n", "import parseToHsla from './parseToHsla';\nimport hsla from './hsla';\n\n/**\n * Adjusts the current hue of the color by the given degrees. Wraps around when\n * over 360.\n *\n * @param color input color\n * @param degrees degrees to adjust the input color, accepts degree integers\n * (0 - 360) and wraps around on overflow\n */\nfunction adjustHue(color: string, degrees: number): string {\n  const [h, s, l, a] = parseToHsla(color);\n  return hsla(h + degrees, s, l, a);\n}\n\nexport default adjustHue;\n", "import parseToHsla from './parseToHsla';\nimport hsla from './hsla';\n\n/**\n * Darkens using lightness. This is equivalent to subtracting the lightness\n * from the L in HSL.\n *\n * @param amount The amount to darken, given as a decimal between 0 and 1\n */\nfunction darken(color: string, amount: number): string {\n  const [hue, saturation, lightness, alpha] = parseToHsla(color);\n  return hsla(hue, saturation, lightness - amount, alpha);\n}\n\nexport default darken;\n", "import parseToHsla from './parseToHsla';\nimport hsla from './hsla';\n\n/**\n * Desaturates the input color by the given amount via subtracting from the `s`\n * in `hsla`.\n *\n * @param amount The amount to desaturate, given as a decimal between 0 and 1\n */\nfunction desaturate(color: string, amount: number): string {\n  const [h, s, l, a] = parseToHsla(color);\n  return hsla(h, s - amount, l, a);\n}\n\nexport default desaturate;\n", "import parseToRgba from './parseToRgba';\n// taken from:\n// https://github.com/styled-components/polished/blob/0764c982551b487469043acb56281b0358b3107b/src/color/getLuminance.js\n\n/**\n * Returns a number (float) representing the luminance of a color.\n */\nfunction getLuminance(color: string): number {\n  if (color === 'transparent') return 0;\n\n  function f(x: number) {\n    const channel = x / 255;\n    return channel <= 0.04045\n      ? channel / 12.92\n      : Math.pow(((channel + 0.055) / 1.055), 2.4);\n  }\n\n  const [r, g, b] = parseToRgba(color);\n  return 0.2126 * f(r) + 0.7152 * f(g) + 0.0722 * f(b);\n}\n\nexport default getLuminance;\n", "// taken from:\n// https://github.com/styled-components/polished/blob/0764c982551b487469043acb56281b0358b3107b/src/color/getContrast.js\nimport getLuminance from './getLuminance';\n\n/**\n * Returns the contrast ratio between two colors based on\n * [W3's recommended equation for calculating contrast](http://www.w3.org/TR/WCAG20/#contrast-ratiodef).\n */\nfunction getContrast(color1: string, color2: string): number {\n  const luminance1 = getLuminance(color1);\n  const luminance2 = getLuminance(color2);\n\n  return luminance1 > luminance2\n    ? (luminance1 + 0.05) / (luminance2 + 0.05)\n    : (luminance2 + 0.05) / (luminance1 + 0.05);\n}\n\nexport default getContrast;\n", "import guard from './guard';\n\n/**\n * Takes in rgba parts and returns an rgba string\n *\n * @param red The amount of red in the red channel, given in a number between 0 and 255 inclusive\n * @param green The amount of green in the red channel, given in a number between 0 and 255 inclusive\n * @param blue The amount of blue in the red channel, given in a number between 0 and 255 inclusive\n * @param alpha Percentage of opacity, given as a decimal between 0 and 1\n */\nfunction rgba(red: number, green: number, blue: number, alpha: number): string {\n  return `rgba(${guard(0, 255, red).toFixed()}, ${guard(\n    0,\n    255,\n    green\n  ).toFixed()}, ${guard(0, 255, blue).toFixed()}, ${parseFloat(\n    guard(0, 1, alpha).toFixed(3)\n  )})`;\n}\n\nexport default rgba;\n", "import parseToRgba from './parseToRgba';\nimport rgba from './rgba';\n\n/**\n * Mixes two colors together. Taken from sass's implementation.\n */\nfunction mix(color1: string, color2: string, weight: number): string {\n  const normalize = (n: number, index: number) =>\n    // 3rd index is alpha channel which is already normalized\n    index === 3 ? n : n / 255;\n\n  const [r1, g1, b1, a1] = parseToRgba(color1).map(normalize);\n  const [r2, g2, b2, a2] = parseToRgba(color2).map(normalize);\n\n  // The formula is copied from the original Sass implementation:\n  // http://sass-lang.com/documentation/Sass/Script/Functions.html#mix-instance_method\n  const alphaDelta = a2 - a1;\n  const normalizedWeight = weight * 2 - 1;\n  const combinedWeight =\n    normalizedWeight * alphaDelta === -1\n      ? normalizedWeight\n      : normalizedWeight + alphaDelta / (1 + normalizedWeight * alphaDelta);\n  const weight2 = (combinedWeight + 1) / 2;\n  const weight1 = 1 - weight2;\n\n  const r = (r1 * weight1 + r2 * weight2) * 255;\n  const g = (g1 * weight1 + g2 * weight2) * 255;\n  const b = (b1 * weight1 + b2 * weight2) * 255;\n  const a = a2 * weight + a1 * (1 - weight);\n\n  return rgba(r, g, b, a);\n}\n\nexport default mix;\n", "import mix from './mix';\nimport guard from './guard';\n\n/**\n * Given a series colors, this function will return a `scale(x)` function that\n * accepts a percentage as a decimal between 0 and 1 and returns the color at\n * that percentage in the scale.\n *\n * ```js\n * const scale = getScale('red', 'yellow', 'green');\n * console.log(scale(0)); // rgba(255, 0, 0, 1)\n * console.log(scale(0.5)); // rgba(255, 255, 0, 1)\n * console.log(scale(1)); // rgba(0, 128, 0, 1)\n * ```\n *\n * If you'd like to limit the domain and range like chroma-js, we recommend\n * wrapping scale again.\n *\n * ```js\n * const _scale = getScale('red', 'yellow', 'green');\n * const scale = x => _scale(x / 100);\n *\n * console.log(scale(0)); // rgba(255, 0, 0, 1)\n * console.log(scale(50)); // rgba(255, 255, 0, 1)\n * console.log(scale(100)); // rgba(0, 128, 0, 1)\n * ```\n */\nfunction getScale(...colors: string[]): (n: number) => string {\n  return (n) => {\n    const lastIndex = colors.length - 1;\n    const lowIndex = guard(0, lastIndex, Math.floor(n * lastIndex));\n    const highIndex = guard(0, lastIndex, Math.ceil(n * lastIndex));\n\n    const color1 = colors[lowIndex];\n    const color2 = colors[highIndex];\n\n    const unit = 1 / lastIndex;\n    const weight = (n - unit * lowIndex) / unit;\n\n    return mix(color1, color2, weight);\n  };\n}\n\nexport default getScale;\n", "import getContrast from './getContrast';\n\nconst guidelines = {\n  decorative: 1.5,\n  readable: 3,\n  aa: 4.5,\n  aaa: 7,\n};\n\n/**\n * Returns whether or not a color has bad contrast against a background\n * according to a given standard.\n */\nfunction hasBadContrast(\n  color: string,\n  standard: 'decorative' | 'readable' | 'aa' | 'aaa' = 'aa',\n  background: string = '#fff'\n): boolean {\n  return getContrast(color, background) < guidelines[standard];\n}\n\nexport default hasBadContrast;\n", "import darken from './darken';\n/**\n * Lightens a color by a given amount. This is equivalent to\n * `darken(color, -amount)`\n *\n * @param amount The amount to darken, given as a decimal between 0 and 1\n */\nfunction lighten(color: string, amount: number): string {\n  return darken(color, -amount);\n}\n\nexport default lighten;\n", "import parseToRgba from './parseToRgba';\nimport rgba from './rgba';\n\n/**\n * Takes in a color and makes it more transparent by convert to `rgba` and\n * decreasing the amount in the alpha channel.\n *\n * @param amount The amount to increase the transparency by, given as a decimal between 0 and 1\n */\nfunction transparentize(color: string, amount: number): string {\n  const [r, g, b, a] = parseToRgba(color);\n  return rgba(r, g, b, a - amount);\n}\n\nexport default transparentize;\n", "import transparentize from './transparentize';\n\n/**\n * Takes a color and un-transparentizes it. Equivalent to\n * `transparentize(color, -amount)`\n *\n * @param amount The amount to increase the opacity by, given as a decimal between 0 and 1\n */\nfunction opacify(color: string, amount: number): string {\n  return transparentize(color, -amount);\n}\n\nexport default opacify;\n", "import getLuminance from './getLuminance';\n\n/**\n * An alternative function to `readableColor`. Returns whether or not the \n * readable color (i.e. the color to be place on top the input color) should be\n * black.\n */\nfunction readableColorIsBlack(color: string): boolean {\n  return getLuminance(color) > 0.179;\n}\n\nexport default readableColorIsBlack;\n", "import readableColorIsBlack from './readableColorIsBlack';\n\n/**\n * Returns black or white for best contrast depending on the luminosity of the\n * given color.\n */\nfunction readableColor(color: string): string {\n  return readableColorIsBlack(color) ? '#000' : '#fff';\n}\n\nexport default readableColor;\n", "import desaturate from './desaturate';\n\n/**\n * Saturates a color by converting it to `hsl` and increasing the saturation\n * amount. Equivalent to `desaturate(color, -amount)`\n * \n * @param color Input color\n * @param amount The amount to darken, given as a decimal between 0 and 1\n */\nfunction saturate(color: string, amount: number): string {\n  return desaturate(color, -amount);\n}\n\nexport default saturate;\n", "import parseToRgba from './parseToRgba';\nimport guard from './guard';\n\n/**\n * Takes in any color and returns it as a hex code.\n */\nfunction toHex(color: string): string {\n  const [r, g, b, a] = parseToRgba(color);\n\n  let hex = (x: number) => {\n    const h = guard(0, 255, x).toString(16);\n    // NOTE: padStart could be used here but it breaks Node 6 compat\n    // https://github.com/ricokahler/color2k/issues/351\n    return h.length === 1 ? `0${h}` : h;\n  };\n\n  return `#${hex(r)}${hex(g)}${hex(b)}${a < 1 ? hex(Math.round(a * 255)) : ''}`;\n}\n\nexport default toHex;\n", "import parseToRgba from './parseToRgba';\nimport rgba from './rgba';\n\n/**\n * Takes in any color and returns it as an rgba string.\n */\nfunction toRgba(color: string): string {\n  return rgba(...parseToRgba(color));\n}\n\nexport default toRgba;\n", "import parseToHsla from './parseToHsla';\nimport hsla from './hsla';\n\n/**\n * Takes in any color and returns it as an hsla string.\n */\nfunction toHsla(color: string): string {\n  return hsla(...parseToHsla(color));\n}\n\nexport default toHsla;\n"], "names": ["guard", "low", "high", "value", "Math", "min", "max", "ColorError", "Error", "constructor", "color", "parseToRgba", "trim", "toLowerCase", "normalizedColor", "namedColorRegex", "test", "nameToHex", "reducedHexMatch", "reducedHexRegex", "exec", "arr", "Array", "from", "slice", "map", "x", "parseInt", "r", "hexMatch", "hexRegex", "rgbaMatch", "rgbaRegex", "parseFloat", "hslaMatch", "hslaRegex", "h", "s", "l", "a", "hslToRgb", "Number", "isNaN", "hash", "str", "i", "length", "charCodeAt", "colorToInt", "replace", "compressedColorMap", "split", "reduce", "acc", "next", "key", "substring", "hex", "toString", "prefix", "normalizedColorName", "result", "amount", "join", "RegExp", "roundColor", "round", "hue", "saturation", "lightness", "huePrime", "chroma", "abs", "secondComponent", "red", "green", "blue", "lightnessModification", "finalRed", "finalGreen", "finalBlue", "parseToHsla", "alpha", "index", "delta", "hsla", "toFixed", "adjustHue", "degrees", "darken", "desaturate", "getLuminance", "f", "channel", "pow", "g", "b", "getContrast", "color1", "color2", "luminance1", "luminance2", "rgba", "mix", "weight", "normalize", "n", "r1", "g1", "b1", "a1", "r2", "g2", "b2", "a2", "alphaDelta", "normalizedWeight", "combinedWeight", "weight2", "weight1", "getScale", "colors", "lastIndex", "lowIndex", "floor", "highIndex", "ceil", "unit", "guidelines", "decorative", "readable", "aa", "aaa", "hasBadContrast", "standard", "background", "lighten", "transparentize", "opacify", "readableColorIsBlack", "readableColor", "saturate", "toHex", "toRgba", "toHsla"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,KAAKA,CAACC,GAAW,EAAEC,IAAY,EAAEC,KAAa,EAAU;AAC/D,EAAA,OAAOC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACL,GAAG,EAAEE,KAAK,CAAC,EAAED,IAAI,CAAC,CAAA;AAC7C;;ACTA,MAAMK,UAAU,SAASC,KAAK,CAAC;EAC7BC,WAAWA,CAACC,KAAa,EAAE;AACzB,IAAA,KAAK,CAAE,CAAA,wBAAA,EAA0BA,KAAM,CAAA,CAAA,CAAE,CAAC,CAAA;AAC5C,GAAA;AACF,CAAA;AAEA,mBAAeH,UAAU;;ACHzB;AACA;AACA;AACA;AACA;AACA,SAASI,WAAWA,CAACD,KAAa,EAAoC;EACpE,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE,MAAM,IAAIH,YAAU,CAACG,KAAK,CAAC,CAAA;EAC1D,IAAIA,KAAK,CAACE,IAAI,EAAE,CAACC,WAAW,EAAE,KAAK,aAAa,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;AAErE,EAAA,IAAIC,eAAe,GAAGJ,KAAK,CAACE,IAAI,EAAE,CAAA;AAClCE,EAAAA,eAAe,GAAGC,eAAe,CAACC,IAAI,CAACN,KAAK,CAAC,GAAGO,SAAS,CAACP,KAAK,CAAC,GAAGA,KAAK,CAAA;AAExE,EAAA,MAAMQ,eAAe,GAAGC,eAAe,CAACC,IAAI,CAACN,eAAe,CAAC,CAAA;AAC7D,EAAA,IAAII,eAAe,EAAE;AACnB,IAAA,MAAMG,GAAG,GAAGC,KAAK,CAACC,IAAI,CAACL,eAAe,CAAC,CAACM,KAAK,CAAC,CAAC,CAAC,CAAA;IAChD,OAAO,CACL,GAAGH,GAAG,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAEC,CAAC,IAAKC,QAAQ,CAACC,CAAC,CAACF,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EACpDC,QAAQ,CAACC,CAAC,CAACP,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CACxC,CAAA;AACH,GAAA;AAEA,EAAA,MAAMQ,QAAQ,GAAGC,QAAQ,CAACV,IAAI,CAACN,eAAe,CAAC,CAAA;AAC/C,EAAA,IAAIe,QAAQ,EAAE;AACZ,IAAA,MAAMR,GAAG,GAAGC,KAAK,CAACC,IAAI,CAACM,QAAQ,CAAC,CAACL,KAAK,CAAC,CAAC,CAAC,CAAA;AACzC,IAAA,OAAO,CACL,GAAGH,GAAG,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAEC,CAAC,IAAKC,QAAQ,CAACD,CAAC,EAAE,EAAE,CAAC,CAAC,EAC9CC,QAAQ,CAACN,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,CACnC,CAAA;AACH,GAAA;AAEA,EAAA,MAAMU,SAAS,GAAGC,SAAS,CAACZ,IAAI,CAACN,eAAe,CAAC,CAAA;AACjD,EAAA,IAAIiB,SAAS,EAAE;AACb,IAAA,MAAMV,GAAG,GAAGC,KAAK,CAACC,IAAI,CAACQ,SAAS,CAAC,CAACP,KAAK,CAAC,CAAC,CAAC,CAAA;AAC1C,IAAA,OAAO,CACL,GAAGH,GAAG,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAEC,CAAC,IAAKC,QAAQ,CAACD,CAAC,EAAE,EAAE,CAAC,CAAC,EAC9CO,UAAU,CAACZ,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAC1B,CAAA;AACH,GAAA;AAEA,EAAA,MAAMa,SAAS,GAAGC,SAAS,CAACf,IAAI,CAACN,eAAe,CAAC,CAAA;AACjD,EAAA,IAAIoB,SAAS,EAAE;IACb,MAAM,CAACE,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAGjB,KAAK,CAACC,IAAI,CAACW,SAAS,CAAC,CAACV,KAAK,CAAC,CAAC,CAAC,CAACC,GAAG,CAACQ,UAAU,CAAC,CAAA;AACnE,IAAA,IAAIjC,KAAK,CAAC,CAAC,EAAE,GAAG,EAAEqC,CAAC,CAAC,KAAKA,CAAC,EAAE,MAAM,IAAI9B,YAAU,CAACG,KAAK,CAAC,CAAA;AACvD,IAAA,IAAIV,KAAK,CAAC,CAAC,EAAE,GAAG,EAAEsC,CAAC,CAAC,KAAKA,CAAC,EAAE,MAAM,IAAI/B,YAAU,CAACG,KAAK,CAAC,CAAA;IACvD,OAAO,CAAC,GAAG8B,QAAQ,CAACJ,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,EAAEG,MAAM,CAACC,KAAK,CAACH,CAAC,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC,CAAA;AAMxD,GAAA;AAEA,EAAA,MAAM,IAAIhC,YAAU,CAACG,KAAK,CAAC,CAAA;AAC7B,CAAA;AAEA,SAASiC,IAAIA,CAACC,GAAW,EAAE;EACzB,IAAID,IAAI,GAAG,IAAI,CAAA;AACf,EAAA,IAAIE,CAAC,GAAGD,GAAG,CAACE,MAAM,CAAA;AAElB,EAAA,OAAOD,CAAC,EAAE;IACRF,IAAI,GAAIA,IAAI,GAAG,EAAE,GAAIC,GAAG,CAACG,UAAU,CAAC,EAAEF,CAAC,CAAC,CAAA;AAC1C,GAAA;;AAEA;AACF;AACA;AACE,EAAA,OAAO,CAACF,IAAI,KAAK,CAAC,IAAI,IAAI,CAAA;AAC5B,CAAA;AAEA,MAAMK,UAAU,GAAItB,CAAS,IAAKC,QAAQ,CAACD,CAAC,CAACuB,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAA;AAEnE,MAAMC,kBAAkB,GACtB,qzCAAqzC,CAClzCC,KAAK,CAAC,GAAG,CAAC,CACVC,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAK;AACrB,EAAA,MAAMC,GAAG,GAAGP,UAAU,CAACM,IAAI,CAACE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;AAC5C,EAAA,MAAMC,GAAG,GAAGT,UAAU,CAACM,IAAI,CAACE,SAAS,CAAC,CAAC,CAAC,CAAC,CAACE,QAAQ,CAAC,EAAE,CAAC,CAAA;;AAEtD;AACA;EACA,IAAIC,MAAM,GAAG,EAAE,CAAA;AACf,EAAA,KAAK,IAAId,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,GAAGY,GAAG,CAACX,MAAM,EAAED,CAAC,EAAE,EAAE;AACvCc,IAAAA,MAAM,IAAI,GAAG,CAAA;AACf,GAAA;EAEAN,GAAG,CAACE,GAAG,CAAC,GAAI,GAAEI,MAAO,CAAA,EAAEF,GAAI,CAAC,CAAA,CAAA;AAC5B,EAAA,OAAOJ,GAAG,CAAA;AACZ,CAAC,EAAE,EAA+B,CAAC,CAAA;;AAEvC;AACA;AACA;AACA,SAASpC,SAASA,CAACP,KAAa,EAAU;EACxC,MAAMkD,mBAAmB,GAAGlD,KAAK,CAACG,WAAW,EAAE,CAACD,IAAI,EAAE,CAAA;EACtD,MAAMiD,MAAM,GAAGX,kBAAkB,CAACP,IAAI,CAACiB,mBAAmB,CAAC,CAAC,CAAA;EAC5D,IAAI,CAACC,MAAM,EAAE,MAAM,IAAItD,YAAU,CAACG,KAAK,CAAC,CAAA;EACxC,OAAQ,CAAA,CAAA,EAAGmD,MAAO,CAAC,CAAA,CAAA;AACrB,CAAA;AAEA,MAAMjC,CAAC,GAAGA,CAACgB,GAAW,EAAEkB,MAAc,KACpCxC,KAAK,CAACC,IAAI,CAACD,KAAK,CAACwC,MAAM,CAAC,CAAC,CACtBrC,GAAG,CAAC,MAAMmB,GAAG,CAAC,CACdmB,IAAI,CAAC,EAAE,CAAC,CAAA;AAEb,MAAM5C,eAAe,GAAG,IAAI6C,MAAM,CAAE,CAAIpC,EAAAA,EAAAA,CAAC,CAAC,YAAY,EAAE,CAAC,CAAE,CAAa,YAAA,CAAA,EAAE,GAAG,CAAC,CAAA;AAC9E,MAAME,QAAQ,GAAG,IAAIkC,MAAM,CAAE,CAAIpC,EAAAA,EAAAA,CAAC,CAAC,eAAe,EAAE,CAAC,CAAE,CAAgB,eAAA,CAAA,EAAE,GAAG,CAAC,CAAA;AAC7E,MAAMI,SAAS,GAAG,IAAIgC,MAAM,CACzB,CAAyBpC,uBAAAA,EAAAA,CAAC,CACzB,iBAAiB,EACjB,CACF,CAAE,CAA4B,2BAAA,CAAA,EAC9B,GACF,CAAC,CAAA;AACD,MAAMO,SAAS,GACb,gFAAgF,CAAA;AAClF,MAAMpB,eAAe,GAAG,WAAW,CAAA;AAEnC,MAAMkD,UAAU,GAAIvD,KAAa,IAAa;AAC5C,EAAA,OAAON,IAAI,CAAC8D,KAAK,CAACxD,KAAK,GAAG,GAAG,CAAC,CAAA;AAChC,CAAC,CAAA;AAED,MAAM8B,QAAQ,GAAGA,CACf2B,GAAW,EACXC,UAAkB,EAClBC,SAAiB,KACY;AAC7B,EAAA,IAAI/B,CAAC,GAAG+B,SAAS,GAAG,GAAG,CAAA;EACvB,IAAID,UAAU,KAAK,CAAC,EAAE;AACpB;IACA,OAAO,CAAC9B,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,CAACb,GAAG,CAACwC,UAAU,CAAC,CAAA;AAClC,GAAA;;AAEA;EACA,MAAMK,QAAQ,GAAI,CAAEH,GAAG,GAAG,GAAG,GAAI,GAAG,IAAI,GAAG,GAAI,EAAE,CAAA;AACjD,EAAA,MAAMI,MAAM,GAAG,CAAC,CAAC,GAAGnE,IAAI,CAACoE,GAAG,CAAC,CAAC,GAAGlC,CAAC,GAAG,CAAC,CAAC,KAAK8B,UAAU,GAAG,GAAG,CAAC,CAAA;AAC7D,EAAA,MAAMK,eAAe,GAAGF,MAAM,IAAI,CAAC,GAAGnE,IAAI,CAACoE,GAAG,CAAEF,QAAQ,GAAG,CAAC,GAAI,CAAC,CAAC,CAAC,CAAA;EAEnE,IAAII,GAAG,GAAG,CAAC,CAAA;EACX,IAAIC,KAAK,GAAG,CAAC,CAAA;EACb,IAAIC,IAAI,GAAG,CAAC,CAAA;AAEZ,EAAA,IAAIN,QAAQ,IAAI,CAAC,IAAIA,QAAQ,GAAG,CAAC,EAAE;AACjCI,IAAAA,GAAG,GAAGH,MAAM,CAAA;AACZI,IAAAA,KAAK,GAAGF,eAAe,CAAA;GACxB,MAAM,IAAIH,QAAQ,IAAI,CAAC,IAAIA,QAAQ,GAAG,CAAC,EAAE;AACxCI,IAAAA,GAAG,GAAGD,eAAe,CAAA;AACrBE,IAAAA,KAAK,GAAGJ,MAAM,CAAA;GACf,MAAM,IAAID,QAAQ,IAAI,CAAC,IAAIA,QAAQ,GAAG,CAAC,EAAE;AACxCK,IAAAA,KAAK,GAAGJ,MAAM,CAAA;AACdK,IAAAA,IAAI,GAAGH,eAAe,CAAA;GACvB,MAAM,IAAIH,QAAQ,IAAI,CAAC,IAAIA,QAAQ,GAAG,CAAC,EAAE;AACxCK,IAAAA,KAAK,GAAGF,eAAe,CAAA;AACvBG,IAAAA,IAAI,GAAGL,MAAM,CAAA;GACd,MAAM,IAAID,QAAQ,IAAI,CAAC,IAAIA,QAAQ,GAAG,CAAC,EAAE;AACxCI,IAAAA,GAAG,GAAGD,eAAe,CAAA;AACrBG,IAAAA,IAAI,GAAGL,MAAM,CAAA;GACd,MAAM,IAAID,QAAQ,IAAI,CAAC,IAAIA,QAAQ,GAAG,CAAC,EAAE;AACxCI,IAAAA,GAAG,GAAGH,MAAM,CAAA;AACZK,IAAAA,IAAI,GAAGH,eAAe,CAAA;AACxB,GAAA;AAEA,EAAA,MAAMI,qBAAqB,GAAGvC,CAAC,GAAGiC,MAAM,GAAG,CAAC,CAAA;AAC5C,EAAA,MAAMO,QAAQ,GAAGJ,GAAG,GAAGG,qBAAqB,CAAA;AAC5C,EAAA,MAAME,UAAU,GAAGJ,KAAK,GAAGE,qBAAqB,CAAA;AAChD,EAAA,MAAMG,SAAS,GAAGJ,IAAI,GAAGC,qBAAqB,CAAA;EAE9C,OAAO,CAACC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,CAAC,CAACvD,GAAG,CAACwC,UAAU,CAAC,CAAA;AAK1D,CAAC;;AC9KD;AACA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA,SAASgB,WAAWA,CAACvE,KAAa,EAAoC;EACpE,MAAM,CAACgE,GAAG,EAAEC,KAAK,EAAEC,IAAI,EAAEM,KAAK,CAAC,GAAGvE,WAAW,CAACD,KAAK,CAAC,CAACe,GAAG,CAAC,CAACtB,KAAK,EAAEgF,KAAK;AACpE;EACAA,KAAK,KAAK,CAAC,GAAGhF,KAAK,GAAGA,KAAK,GAAG,GAChC,CAAC,CAAA;EAED,MAAMG,GAAG,GAAGF,IAAI,CAACE,GAAG,CAACoE,GAAG,EAAEC,KAAK,EAAEC,IAAI,CAAC,CAAA;EACtC,MAAMvE,GAAG,GAAGD,IAAI,CAACC,GAAG,CAACqE,GAAG,EAAEC,KAAK,EAAEC,IAAI,CAAC,CAAA;AACtC,EAAA,MAAMP,SAAS,GAAG,CAAC/D,GAAG,GAAGD,GAAG,IAAI,CAAC,CAAA;;AAEjC;AACA,EAAA,IAAIC,GAAG,KAAKD,GAAG,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAEgE,SAAS,EAAEa,KAAK,CAAC,CAAA;AAEhD,EAAA,MAAME,KAAK,GAAG9E,GAAG,GAAGD,GAAG,CAAA;EACvB,MAAM+D,UAAU,GACdC,SAAS,GAAG,GAAG,GAAGe,KAAK,IAAI,CAAC,GAAG9E,GAAG,GAAGD,GAAG,CAAC,GAAG+E,KAAK,IAAI9E,GAAG,GAAGD,GAAG,CAAC,CAAA;EAEjE,MAAM8D,GAAG,GACP,EAAE,IACDO,GAAG,KAAKpE,GAAG,GACR,CAACqE,KAAK,GAAGC,IAAI,IAAIQ,KAAK,IAAIT,KAAK,GAAGC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,GAC/CD,KAAK,KAAKrE,GAAG,GACb,CAACsE,IAAI,GAAGF,GAAG,IAAIU,KAAK,GAAG,CAAC,GACxB,CAACV,GAAG,GAAGC,KAAK,IAAIS,KAAK,GAAG,CAAC,CAAC,CAAA;EAEhC,OAAO,CAACjB,GAAG,EAAEC,UAAU,EAAEC,SAAS,EAAEa,KAAK,CAAC,CAAA;AAC5C;;AClCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,IAAIA,CACXlB,GAAW,EACXC,UAAkB,EAClBC,SAAiB,EACjBa,KAAa,EACL;EACR,OAAQ,CAAA,KAAA,EAAO,CAACf,GAAG,GAAG,GAAG,EAAEmB,OAAO,EAAG,CAAItF,EAAAA,EAAAA,KAAK,CAC5C,CAAC,EACD,GAAG,EACHoE,UAAU,GAAG,GACf,CAAC,CAACkB,OAAO,EAAG,CAAKtF,GAAAA,EAAAA,KAAK,CAAC,CAAC,EAAE,GAAG,EAAEqE,SAAS,GAAG,GAAG,CAAC,CAACiB,OAAO,EAAG,CAAKrD,GAAAA,EAAAA,UAAU,CACvEjC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAEkF,KAAK,CAAC,CAACI,OAAO,CAAC,CAAC,CAC9B,CAAE,CAAE,CAAA,CAAA,CAAA;AACN;;ACpBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAAC7E,KAAa,EAAE8E,OAAe,EAAU;AACzD,EAAA,MAAM,CAACpD,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAG0C,WAAW,CAACvE,KAAK,CAAC,CAAA;EACvC,OAAO2E,IAAI,CAACjD,CAAC,GAAGoD,OAAO,EAAEnD,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,CAAA;AACnC;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkD,MAAMA,CAAC/E,KAAa,EAAEoD,MAAc,EAAU;AACrD,EAAA,MAAM,CAACK,GAAG,EAAEC,UAAU,EAAEC,SAAS,EAAEa,KAAK,CAAC,GAAGD,WAAW,CAACvE,KAAK,CAAC,CAAA;EAC9D,OAAO2E,IAAI,CAAClB,GAAG,EAAEC,UAAU,EAAEC,SAAS,GAAGP,MAAM,EAAEoB,KAAK,CAAC,CAAA;AACzD;;ACTA;AACA;AACA;AACA;AACA;AACA;AACA,SAASQ,UAAUA,CAAChF,KAAa,EAAEoD,MAAc,EAAU;AACzD,EAAA,MAAM,CAAC1B,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAG0C,WAAW,CAACvE,KAAK,CAAC,CAAA;EACvC,OAAO2E,IAAI,CAACjD,CAAC,EAAEC,CAAC,GAAGyB,MAAM,EAAExB,CAAC,EAAEC,CAAC,CAAC,CAAA;AAClC;;ACXA;AACA;;AAEA;AACA;AACA;AACA,SAASoD,YAAYA,CAACjF,KAAa,EAAU;AAC3C,EAAA,IAAIA,KAAK,KAAK,aAAa,EAAE,OAAO,CAAC,CAAA;EAErC,SAASkF,CAACA,CAAClE,CAAS,EAAE;AACpB,IAAA,MAAMmE,OAAO,GAAGnE,CAAC,GAAG,GAAG,CAAA;IACvB,OAAOmE,OAAO,IAAI,OAAO,GACrBA,OAAO,GAAG,KAAK,GACfzF,IAAI,CAAC0F,GAAG,CAAE,CAACD,OAAO,GAAG,KAAK,IAAI,KAAK,EAAG,GAAG,CAAC,CAAA;AAChD,GAAA;EAEA,MAAM,CAACjE,CAAC,EAAEmE,CAAC,EAAEC,CAAC,CAAC,GAAGrF,WAAW,CAACD,KAAK,CAAC,CAAA;AACpC,EAAA,OAAO,MAAM,GAAGkF,CAAC,CAAChE,CAAC,CAAC,GAAG,MAAM,GAAGgE,CAAC,CAACG,CAAC,CAAC,GAAG,MAAM,GAAGH,CAAC,CAACI,CAAC,CAAC,CAAA;AACtD;;ACnBA;AACA;;AAGA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,MAAc,EAAEC,MAAc,EAAU;AAC3D,EAAA,MAAMC,UAAU,GAAGT,YAAY,CAACO,MAAM,CAAC,CAAA;AACvC,EAAA,MAAMG,UAAU,GAAGV,YAAY,CAACQ,MAAM,CAAC,CAAA;EAEvC,OAAOC,UAAU,GAAGC,UAAU,GAC1B,CAACD,UAAU,GAAG,IAAI,KAAKC,UAAU,GAAG,IAAI,CAAC,GACzC,CAACA,UAAU,GAAG,IAAI,KAAKD,UAAU,GAAG,IAAI,CAAC,CAAA;AAC/C;;ACbA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,IAAIA,CAAC5B,GAAW,EAAEC,KAAa,EAAEC,IAAY,EAAEM,KAAa,EAAU;EAC7E,OAAQ,CAAA,KAAA,EAAOlF,KAAK,CAAC,CAAC,EAAE,GAAG,EAAE0E,GAAG,CAAC,CAACY,OAAO,EAAG,KAAItF,KAAK,CACnD,CAAC,EACD,GAAG,EACH2E,KACF,CAAC,CAACW,OAAO,EAAG,CAAItF,EAAAA,EAAAA,KAAK,CAAC,CAAC,EAAE,GAAG,EAAE4E,IAAI,CAAC,CAACU,OAAO,EAAG,CAAIrD,EAAAA,EAAAA,UAAU,CAC1DjC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAEkF,KAAK,CAAC,CAACI,OAAO,CAAC,CAAC,CAC9B,CAAE,CAAE,CAAA,CAAA,CAAA;AACN;;ACfA;AACA;AACA;AACA,SAASiB,GAAGA,CAACL,MAAc,EAAEC,MAAc,EAAEK,MAAc,EAAU;AACnE,EAAA,MAAMC,SAAS,GAAGA,CAACC,CAAS,EAAEvB,KAAa;AACzC;AACAA,EAAAA,KAAK,KAAK,CAAC,GAAGuB,CAAC,GAAGA,CAAC,GAAG,GAAG,CAAA;AAE3B,EAAA,MAAM,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,GAAGnG,WAAW,CAACuF,MAAM,CAAC,CAACzE,GAAG,CAACgF,SAAS,CAAC,CAAA;AAC3D,EAAA,MAAM,CAACM,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,GAAGvG,WAAW,CAACwF,MAAM,CAAC,CAAC1E,GAAG,CAACgF,SAAS,CAAC,CAAA;;AAE3D;AACA;AACA,EAAA,MAAMU,UAAU,GAAGD,EAAE,GAAGJ,EAAE,CAAA;AAC1B,EAAA,MAAMM,gBAAgB,GAAGZ,MAAM,GAAG,CAAC,GAAG,CAAC,CAAA;EACvC,MAAMa,cAAc,GAClBD,gBAAgB,GAAGD,UAAU,KAAK,CAAC,CAAC,GAChCC,gBAAgB,GAChBA,gBAAgB,GAAGD,UAAU,IAAI,CAAC,GAAGC,gBAAgB,GAAGD,UAAU,CAAC,CAAA;AACzE,EAAA,MAAMG,OAAO,GAAG,CAACD,cAAc,GAAG,CAAC,IAAI,CAAC,CAAA;AACxC,EAAA,MAAME,OAAO,GAAG,CAAC,GAAGD,OAAO,CAAA;EAE3B,MAAM1F,CAAC,GAAG,CAAC+E,EAAE,GAAGY,OAAO,GAAGR,EAAE,GAAGO,OAAO,IAAI,GAAG,CAAA;EAC7C,MAAMvB,CAAC,GAAG,CAACa,EAAE,GAAGW,OAAO,GAAGP,EAAE,GAAGM,OAAO,IAAI,GAAG,CAAA;EAC7C,MAAMtB,CAAC,GAAG,CAACa,EAAE,GAAGU,OAAO,GAAGN,EAAE,GAAGK,OAAO,IAAI,GAAG,CAAA;EAC7C,MAAM/E,CAAC,GAAG2E,EAAE,GAAGV,MAAM,GAAGM,EAAE,IAAI,CAAC,GAAGN,MAAM,CAAC,CAAA;EAEzC,OAAOF,IAAI,CAAC1E,CAAC,EAAEmE,CAAC,EAAEC,CAAC,EAAEzD,CAAC,CAAC,CAAA;AACzB;;AC5BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASiF,QAAQA,CAAC,GAAGC,MAAgB,EAAyB;AAC5D,EAAA,OAAQf,CAAC,IAAK;AACZ,IAAA,MAAMgB,SAAS,GAAGD,MAAM,CAAC3E,MAAM,GAAG,CAAC,CAAA;AACnC,IAAA,MAAM6E,QAAQ,GAAG3H,KAAK,CAAC,CAAC,EAAE0H,SAAS,EAAEtH,IAAI,CAACwH,KAAK,CAAClB,CAAC,GAAGgB,SAAS,CAAC,CAAC,CAAA;AAC/D,IAAA,MAAMG,SAAS,GAAG7H,KAAK,CAAC,CAAC,EAAE0H,SAAS,EAAEtH,IAAI,CAAC0H,IAAI,CAACpB,CAAC,GAAGgB,SAAS,CAAC,CAAC,CAAA;AAE/D,IAAA,MAAMxB,MAAM,GAAGuB,MAAM,CAACE,QAAQ,CAAC,CAAA;AAC/B,IAAA,MAAMxB,MAAM,GAAGsB,MAAM,CAACI,SAAS,CAAC,CAAA;AAEhC,IAAA,MAAME,IAAI,GAAG,CAAC,GAAGL,SAAS,CAAA;IAC1B,MAAMlB,MAAM,GAAG,CAACE,CAAC,GAAGqB,IAAI,GAAGJ,QAAQ,IAAII,IAAI,CAAA;AAE3C,IAAA,OAAOxB,GAAG,CAACL,MAAM,EAAEC,MAAM,EAAEK,MAAM,CAAC,CAAA;GACnC,CAAA;AACH;;ACvCA,MAAMwB,UAAU,GAAG;AACjBC,EAAAA,UAAU,EAAE,GAAG;AACfC,EAAAA,QAAQ,EAAE,CAAC;AACXC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,GAAG,EAAE,CAAA;AACP,CAAC,CAAA;;AAED;AACA;AACA;AACA;AACA,SAASC,cAAcA,CACrB3H,KAAa,EACb4H,QAAkD,GAAG,IAAI,EACzDC,UAAkB,GAAG,MAAM,EAClB;EACT,OAAOtC,WAAW,CAACvF,KAAK,EAAE6H,UAAU,CAAC,GAAGP,UAAU,CAACM,QAAQ,CAAC,CAAA;AAC9D;;AClBA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,OAAOA,CAAC9H,KAAa,EAAEoD,MAAc,EAAU;AACtD,EAAA,OAAO2B,MAAM,CAAC/E,KAAK,EAAE,CAACoD,MAAM,CAAC,CAAA;AAC/B;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS2E,cAAcA,CAAC/H,KAAa,EAAEoD,MAAc,EAAU;AAC7D,EAAA,MAAM,CAAClC,CAAC,EAAEmE,CAAC,EAAEC,CAAC,EAAEzD,CAAC,CAAC,GAAG5B,WAAW,CAACD,KAAK,CAAC,CAAA;EACvC,OAAO4F,IAAI,CAAC1E,CAAC,EAAEmE,CAAC,EAAEC,CAAC,EAAEzD,CAAC,GAAGuB,MAAM,CAAC,CAAA;AAClC;;ACVA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS4E,OAAOA,CAAChI,KAAa,EAAEoD,MAAc,EAAU;AACtD,EAAA,OAAO2E,cAAc,CAAC/H,KAAK,EAAE,CAACoD,MAAM,CAAC,CAAA;AACvC;;ACRA;AACA;AACA;AACA;AACA;AACA,SAAS6E,oBAAoBA,CAACjI,KAAa,EAAW;AACpD,EAAA,OAAOiF,YAAY,CAACjF,KAAK,CAAC,GAAG,KAAK,CAAA;AACpC;;ACPA;AACA;AACA;AACA;AACA,SAASkI,aAAaA,CAAClI,KAAa,EAAU;AAC5C,EAAA,OAAOiI,oBAAoB,CAACjI,KAAK,CAAC,GAAG,MAAM,GAAG,MAAM,CAAA;AACtD;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmI,QAAQA,CAACnI,KAAa,EAAEoD,MAAc,EAAU;AACvD,EAAA,OAAO4B,UAAU,CAAChF,KAAK,EAAE,CAACoD,MAAM,CAAC,CAAA;AACnC;;ACRA;AACA;AACA;AACA,SAASgF,KAAKA,CAACpI,KAAa,EAAU;AACpC,EAAA,MAAM,CAACkB,CAAC,EAAEmE,CAAC,EAAEC,CAAC,EAAEzD,CAAC,CAAC,GAAG5B,WAAW,CAACD,KAAK,CAAC,CAAA;EAEvC,IAAI+C,GAAG,GAAI/B,CAAS,IAAK;AACvB,IAAA,MAAMU,CAAC,GAAGpC,KAAK,CAAC,CAAC,EAAE,GAAG,EAAE0B,CAAC,CAAC,CAACgC,QAAQ,CAAC,EAAE,CAAC,CAAA;AACvC;AACA;IACA,OAAOtB,CAAC,CAACU,MAAM,KAAK,CAAC,GAAI,CAAGV,CAAAA,EAAAA,CAAE,CAAC,CAAA,GAAGA,CAAC,CAAA;GACpC,CAAA;AAED,EAAA,OAAQ,CAAGqB,CAAAA,EAAAA,GAAG,CAAC7B,CAAC,CAAE,CAAE6B,EAAAA,GAAG,CAACsC,CAAC,CAAE,CAAA,EAAEtC,GAAG,CAACuC,CAAC,CAAE,CAAA,EAAEzD,CAAC,GAAG,CAAC,GAAGkB,GAAG,CAACrD,IAAI,CAAC8D,KAAK,CAAC3B,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAG,CAAC,CAAA,CAAA;AAC/E;;ACdA;AACA;AACA;AACA,SAASwG,MAAMA,CAACrI,KAAa,EAAU;AACrC,EAAA,OAAO4F,IAAI,CAAC,GAAG3F,WAAW,CAACD,KAAK,CAAC,CAAC,CAAA;AACpC;;ACLA;AACA;AACA;AACA,SAASsI,MAAMA,CAACtI,KAAa,EAAU;AACrC,EAAA,OAAO2E,IAAI,CAAC,GAAGJ,WAAW,CAACvE,KAAK,CAAC,CAAC,CAAA;AACpC;;;;"}