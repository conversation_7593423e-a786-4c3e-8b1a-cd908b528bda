# PowerShell script to start the frontend
Write-Host "Starting Crypto Tracker Frontend..." -ForegroundColor Green
Write-Host ""

# Navigate to the frontend directory
Set-Location -Path .\frontend

# Check if node_modules exists
if (Test-Path -Path .\node_modules) {
    Write-Host "Dependencies are already installed." -ForegroundColor Green
} else {
    Write-Host "Installing dependencies..." -ForegroundColor Yellow
    npm install
}

# Start the development server
Write-Host "Starting the development server..." -ForegroundColor Green
npm run dev

# Keep the window open
Read-Host -Prompt "Press Enter to exit"
