require('dotenv').config();
const mongoose = require('mongoose');

// Get the MongoDB URI from environment variables
const MONGO_URI = process.env.MONGO_URI;

console.log('Testing MongoDB connection...');
console.log(`Connection string: ${MONGO_URI?.replace(/\/\/([^:]+):([^@]+)@/, '//***:***@')}`);

// Connect to MongoDB
mongoose.connect(MONGO_URI, {
  serverSelectionTimeoutMS: 10000, // 10 seconds
  socketTimeoutMS: 45000, // 45 seconds
  connectTimeoutMS: 10000, // 10 seconds
})
.then(() => {
  console.log('\n✅ MongoDB Connected Successfully!');
  console.log(`Connected to: ${mongoose.connection.host}`);
  console.log(`Database name: ${mongoose.connection.name}`);
  console.log(`Connection state: ${mongoose.connection.readyState}`);
  
  // Close the connection
  mongoose.connection.close();
  console.log('Connection closed.');
})
.catch(err => {
  console.error('\n❌ MongoDB Connection Failed!');
  console.error(`Error: ${err.message}`);
  
  // Provide more helpful error messages based on common issues
  if (err.message.includes('ECONNREFUSED')) {
    console.error('\nPossible causes:');
    console.error('- MongoDB server is not running');
    console.error('- Connection string has incorrect host or port');
  } else if (err.message.includes('Authentication failed')) {
    console.error('\nPossible causes:');
    console.error('- Username or password in connection string is incorrect');
    console.error('- User does not have access to the database');
  } else if (err.message.includes('ETIMEDOUT')) {
    console.error('\nPossible causes:');
    console.error('- Network connectivity issues');
    console.error('- Firewall blocking the connection');
    console.error('- MongoDB Atlas IP whitelist restrictions');
  }
});
