import React from 'react';
import {
  Box,
  Heading,
  Text,
  Flex,
  Badge,
  Skeleton,
  useColorModeValue,
  Divider,
  SimpleGrid,
  Link,
  Progress,
  HStack,
  VStack,
  Icon,
  Tag,
  TagLabel,
  TagLeftIcon,
  Wrap,
  WrapItem,
} from '@chakra-ui/react';
import { InfoIcon, ExternalLinkIcon } from '@chakra-ui/icons';
import { FaTwitter, FaReddit, FaDiscord, FaTelegram } from 'react-icons/fa';
import { useSentimentAnalysis } from '../../hooks/useAI';

interface SentimentAnalysisProps {
  cryptoId: string;
  symbol: string;
}

const SentimentAnalysis: React.FC<SentimentAnalysisProps> = ({ cryptoId, symbol }) => {
  const { data, isLoading, error } = useSentimentAnalysis(cryptoId);
  
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  
  if (isLoading) {
    return (
      <Box p={5} shadow="md" borderWidth="1px" borderRadius="lg" bg={bgColor}>
        <Skeleton height="20px" width="200px" mb={4} />
        <Skeleton height="300px" />
      </Box>
    );
  }
  
  if (error || !data) {
    return (
      <Box p={5} shadow="md" borderWidth="1px" borderRadius="lg" bg={bgColor}>
        <Heading size="md" mb={4}>Market Sentiment</Heading>
        <Text color="red.500">Failed to load sentiment analysis</Text>
      </Box>
    );
  }
  
  // Get sentiment color
  const getSentimentColor = (sentiment: number) => {
    if (sentiment > 0.7) return 'green';
    if (sentiment > 0.55) return 'green.300';
    if (sentiment > 0.45) return 'blue';
    if (sentiment > 0.3) return 'orange';
    return 'red';
  };
  
  // Get sentiment category color
  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'Very Bullish':
        return 'green';
      case 'Bullish':
        return 'green.300';
      case 'Neutral':
        return 'blue';
      case 'Bearish':
        return 'orange';
      case 'Very Bearish':
        return 'red';
      default:
        return 'gray';
    }
  };
  
  // Get social platform icon
  const getSocialIcon = (platform: string) => {
    switch (platform) {
      case 'Twitter':
        return FaTwitter;
      case 'Reddit':
        return FaReddit;
      case 'Discord':
        return FaDiscord;
      case 'Telegram':
        return FaTelegram;
      default:
        return InfoIcon;
    }
  };
  
  return (
    <Box p={5} shadow="md" borderWidth="1px" borderRadius="lg" bg={bgColor} borderColor={borderColor}>
      <Flex justify="space-between" align="center" mb={4}>
        <Heading size="md">Market Sentiment ({symbol})</Heading>
        <Badge 
          colorScheme={getCategoryColor(data.sentimentCategory)} 
          fontSize="md" 
          px={3} 
          py={1}
        >
          {data.sentimentCategory}
        </Badge>
      </Flex>
      
      <Box mb={6}>
        <Text mb={2}>Overall Sentiment Score</Text>
        <Progress 
          value={data.overallSentiment * 100} 
          colorScheme={getSentimentColor(data.overallSentiment)}
          borderRadius="md"
          height="12px"
        />
        <Flex justify="space-between" mt={1}>
          <Text fontSize="xs">Bearish</Text>
          <Text fontSize="xs">Neutral</Text>
          <Text fontSize="xs">Bullish</Text>
        </Flex>
      </Box>
      
      <Divider my={4} />
      
      <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
        {/* News Sentiment */}
        <Box>
          <Heading size="sm" mb={3}>News Sentiment</Heading>
          <VStack align="stretch" spacing={3}>
            {data.news.map((item, index) => (
              <Box 
                key={index} 
                p={3} 
                borderWidth="1px" 
                borderRadius="md" 
                borderColor={borderColor}
                borderLeftWidth="4px"
                borderLeftColor={getSentimentColor(item.sentiment)}
              >
                <Flex justify="space-between" align="center" mb={1}>
                  <Badge>{item.source}</Badge>
                  <Text fontSize="xs" color="gray.500">
                    {new Date(item.date).toLocaleDateString()}
                  </Text>
                </Flex>
                <Link href={item.url} isExternal color="brand.500">
                  <Flex align="center">
                    <Text fontWeight="medium">{item.headline}</Text>
                    <ExternalLinkIcon ml={1} />
                  </Flex>
                </Link>
              </Box>
            ))}
          </VStack>
        </Box>
        
        {/* Social Media Sentiment */}
        <Box>
          <Heading size="sm" mb={3}>Social Media Sentiment</Heading>
          <SimpleGrid columns={2} spacing={4}>
            {data.social.map((item, index) => (
              <Box 
                key={index} 
                p={3} 
                borderWidth="1px" 
                borderRadius="md"
                borderColor={borderColor}
              >
                <Flex align="center" mb={2}>
                  <Icon as={getSocialIcon(item.platform)} mr={2} />
                  <Text fontWeight="medium">{item.platform}</Text>
                  {item.trending && (
                    <Badge ml={2} colorScheme="red">Trending</Badge>
                  )}
                </Flex>
                <Progress 
                  value={item.sentiment * 100} 
                  colorScheme={getSentimentColor(item.sentiment)}
                  size="sm"
                  mb={2}
                />
                <Text fontSize="sm">Volume: {item.volume.toLocaleString()} mentions</Text>
              </Box>
            ))}
          </SimpleGrid>
        </Box>
      </SimpleGrid>
      
      <Divider my={4} />
      
      {/* Word Cloud */}
      <Box mt={4}>
        <Heading size="sm" mb={3}>Trending Keywords</Heading>
        <Wrap spacing={2}>
          {data.wordCloud.map((word, index) => (
            <WrapItem key={index}>
              <Tag 
                size="md" 
                colorScheme={index < 3 ? 'brand' : 'gray'} 
                variant={index < 3 ? 'solid' : 'subtle'}
              >
                <TagLabel>{word}</TagLabel>
              </Tag>
            </WrapItem>
          ))}
        </Wrap>
      </Box>
      
      <Flex mt={4} align="center">
        <InfoIcon mr={2} color="gray.500" />
        <Text fontSize="sm" color="gray.500">
          Last updated: {new Date(data.timestamp).toLocaleString()}
        </Text>
      </Flex>
    </Box>
  );
};

export default SentimentAnalysis;
