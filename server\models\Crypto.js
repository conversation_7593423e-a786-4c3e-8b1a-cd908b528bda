/**
 * Cryptocurrency Model
 * 
 * This model defines the schema for cryptocurrencies in MongoDB.
 */

const mongoose = require('mongoose');

const cryptoSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    symbol: {
      type: String,
      required: true,
      trim: true,
      uppercase: true,
    },
    currentPrice: {
      type: Number,
      required: true,
    },
    marketCap: {
      type: Number,
      required: true,
    },
    volume24h: {
      type: Number,
      required: true,
    },
    priceChange24h: {
      type: Number,
      default: 0,
    },
    priceChangePercentage24h: {
      type: Number,
      default: 0,
    },
    priceChangePercentage1h: {
      type: Number,
      default: 0,
    },
    priceChangePercentage7d: {
      type: Number,
      default: 0,
    },
    circulatingSupply: {
      type: Number,
      default: 0,
    },
    totalSupply: {
      type: Number,
      default: 0,
    },
    maxSupply: {
      type: Number,
      default: null,
    },
    rank: {
      type: Number,
      default: null,
    },
    image: {
      type: String,
      default: null,
    },
    lastUpdated: {
      type: Date,
      default: Date.now,
    },
    sparklineData: {
      type: [Number],
      default: [],
    },
    coinGeckoId: {
      type: String,
      default: null,
    },
  },
  {
    timestamps: true,
  }
);

// Create indexes for better query performance
cryptoSchema.index({ symbol: 1 }, { unique: true });
cryptoSchema.index({ marketCap: -1 });
cryptoSchema.index({ rank: 1 });
cryptoSchema.index({ name: 'text', symbol: 'text' });

// Virtual for formatted price
cryptoSchema.virtual('formattedPrice').get(function() {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: this.currentPrice < 1 ? 4 : 2,
    maximumFractionDigits: this.currentPrice < 1 ? 6 : 2,
  }).format(this.currentPrice);
});

// Virtual for formatted market cap
cryptoSchema.virtual('formattedMarketCap').get(function() {
  if (this.marketCap >= 1e12) return `$${(this.marketCap / 1e12).toFixed(2)}T`;
  if (this.marketCap >= 1e9) return `$${(this.marketCap / 1e9).toFixed(2)}B`;
  if (this.marketCap >= 1e6) return `$${(this.marketCap / 1e6).toFixed(2)}M`;
  return `$${this.marketCap.toFixed(2)}`;
});

// Method to update price data
cryptoSchema.methods.updatePriceData = function(newPrice, newMarketCap, newVolume) {
  // Calculate price change
  const priceChange = newPrice - this.currentPrice;
  const priceChangePercentage = (priceChange / this.currentPrice) * 100;
  
  // Update fields
  this.priceChange24h = priceChange;
  this.priceChangePercentage24h = priceChangePercentage;
  this.currentPrice = newPrice;
  this.marketCap = newMarketCap;
  this.volume24h = newVolume;
  this.lastUpdated = Date.now();
  
  return this.save();
};

// Static method to find trending coins
cryptoSchema.statics.findTrending = function(limit = 5) {
  return this.find()
    .sort({ priceChangePercentage24h: -1 })
    .limit(limit);
};

// Static method to find top coins by market cap
cryptoSchema.statics.findTopByMarketCap = function(limit = 10) {
  return this.find()
    .sort({ marketCap: -1 })
    .limit(limit);
};

// Static method to search coins
cryptoSchema.statics.searchCoins = function(query) {
  return this.find({
    $or: [
      { name: { $regex: query, $options: 'i' } },
      { symbol: { $regex: query, $options: 'i' } },
    ],
  });
};

const Crypto = mongoose.model('Crypto', cryptoSchema);

module.exports = Crypto;
