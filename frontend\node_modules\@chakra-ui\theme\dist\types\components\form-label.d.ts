export declare const formLabelTheme: {
    baseStyle?: {
        fontSize: string;
        marginEnd: string;
        mb: string;
        fontWeight: string;
        transitionProperty: string;
        transitionDuration: string;
        opacity: number;
        _disabled: {
            opacity: number;
        };
    } | undefined;
    sizes?: {
        [key: string]: import("@chakra-ui/styled-system").SystemStyleInterpolation;
    } | undefined;
    variants?: {
        [key: string]: import("@chakra-ui/styled-system").SystemStyleInterpolation;
    } | undefined;
    defaultProps?: {
        size?: string | number | undefined;
        variant?: string | number | undefined;
        colorScheme?: string | undefined;
    } | undefined;
};
