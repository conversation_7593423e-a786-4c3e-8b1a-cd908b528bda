@echo off
echo ===================================
echo  Crypto Tracker Application Setup
echo ===================================
echo.

REM Check if Node.js is installed
where node >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH. Please install Node.js.
    pause
    exit /b 1
)

echo Node.js is installed:
node --version
echo.

REM Check if npm is installed
where npm >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo ERROR: npm is not installed or not in PATH. Please install npm.
    pause
    exit /b 1
)

echo npm is installed:
npm --version
echo.

REM Create node_modules directories if they don't exist
if not exist "server\node_modules" mkdir "server\node_modules"
if not exist "frontend\node_modules" mkdir "frontend\node_modules"

REM Install server dependencies
echo Installing server dependencies...
cd server
call npm install
if %ERRORLEVEL% neq 0 (
    echo WARNING: Error installing server dependencies. Will try to continue anyway.
) else (
    echo Server dependencies installed successfully.
)
cd ..
echo.

REM Install frontend dependencies
echo Installing frontend dependencies...
cd frontend
call npm install
if %ERRORLEVEL% neq 0 (
    echo WARNING: Error installing frontend dependencies. Will try to continue anyway.
) else (
    echo Frontend dependencies installed successfully.
)
cd ..
echo.

REM Kill any existing Node.js processes
echo Stopping any existing Node.js processes...
taskkill /f /im node.exe >nul 2>nul
echo.

REM Start the backend server
echo Starting backend server...
start cmd /k "cd server && node simple-cors-server.js"

REM Wait for the backend to start
echo Waiting for backend to initialize...
timeout /t 8 /nobreak >nul

REM Check if backend is running
echo Checking if backend is running...
curl -s http://localhost:3001/api/health >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo WARNING: Backend may not be running properly. Frontend will use mock data.
    echo Trying to access the API directly...
    curl -v http://localhost:3001/api
) else (
    echo Backend is running successfully.
    echo API response:
    curl -s http://localhost:3001/api
)
echo.

REM Start the frontend server
echo Starting frontend server...
start cmd /k "cd frontend && npm run dev"

echo.
echo ===================================
echo  Servers started successfully!
echo ===================================
echo.
echo Backend API: http://localhost:3001/api
echo Frontend: http://localhost:3002
echo.
echo NOTE: If the backend server is not running, the frontend will use mock data.
echo.
echo Press any key to stop all servers...
pause >nul

echo Stopping servers...
taskkill /f /im node.exe >nul 2>nul
echo All servers stopped.
pause
