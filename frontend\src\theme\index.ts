import { extendTheme, ThemeConfig } from '@chakra-ui/react';
import { mode } from '@chakra-ui/theme-tools';

// Color mode config
const config: ThemeConfig = {
  initialColorMode: 'dark',
  useSystemColorMode: false,
};

// Custom colors
const colors = {
  brand: {
    50: '#e3f2fd',
    100: '#bbdefb',
    200: '#90caf9',
    300: '#64b5f6',
    400: '#42a5f5',
    500: '#2196f3',
    600: '#1e88e5',
    700: '#1976d2',
    800: '#1565c0',
    900: '#0d47a1',
  },
  profit: {
    50: '#e8f5e9',
    100: '#c8e6c9',
    200: '#a5d6a7',
    300: '#81c784',
    400: '#66bb6a',
    500: '#4caf50',
    600: '#43a047',
    700: '#388e3c',
    800: '#2e7d32',
    900: '#1b5e20',
  },
  loss: {
    50: '#ffebee',
    100: '#ffcdd2',
    200: '#ef9a9a',
    300: '#e57373',
    400: '#ef5350',
    500: '#f44336',
    600: '#e53935',
    700: '#d32f2f',
    800: '#c62828',
    900: '#b71c1c',
  },
};

// Component style overrides
const components = {
  Card: {
    baseStyle: (props: any) => ({
      container: {
        bg: mode('white', 'gray.800')(props),
        borderRadius: 'lg',
        boxShadow: 'md',
        overflow: 'hidden',
      },
    }),
  },
  Badge: {
    baseStyle: {
      textTransform: 'none',
    },
  },
  Button: {
    baseStyle: {
      fontWeight: 'medium',
      borderRadius: 'md',
    },
  },
  Stat: {
    baseStyle: {
      container: {
        px: 0,
      },
    },
  },
  Table: {
    variants: {
      simple: (props: any) => ({
        th: {
          borderColor: mode('gray.200', 'gray.700')(props),
          fontSize: 'sm',
          fontWeight: 'medium',
          textTransform: 'none',
          letterSpacing: 'normal',
        },
        td: {
          borderColor: mode('gray.200', 'gray.700')(props),
        },
      }),
    },
  },
};

// Typography
const typography = {
  fonts: {
    heading: 'Inter, system-ui, sans-serif',
    body: 'Inter, system-ui, sans-serif',
  },
};

// Create the theme
const theme = extendTheme({
  config,
  colors,
  components,
  ...typography,
  styles: {
    global: (props: any) => ({
      body: {
        bg: mode('gray.50', 'gray.900')(props),
      },
    }),
  },
});

export default theme;
