/**
 * Simple script to start the Crypto Market server
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('Starting Crypto Market Server...');

// Path to the server.js file
const serverPath = path.join(__dirname, 'server', 'server.js');

// Spawn the server process
const serverProcess = spawn('node', [serverPath], {
  stdio: 'inherit',
  shell: true
});

serverProcess.on('error', (err) => {
  console.error('Failed to start server:', err);
});

console.log('Server process started. Press Ctrl+C to stop.');
