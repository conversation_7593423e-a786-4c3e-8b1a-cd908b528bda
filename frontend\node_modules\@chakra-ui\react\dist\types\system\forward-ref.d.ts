/**
 * All credit goes to <PERSON> (Reach UI), <PERSON><PERSON> (Reakit) and (fluentui)
 * for creating the base type definitions upon which we improved on
 */
import { ElementType } from "react";
import { ComponentWithAs, PropsOf, RightJoinProps } from "./system.types";
export declare function forwardRef<Props extends object, Component extends ElementType>(component: React.ForwardRefRenderFunction<any, RightJoinProps<PropsOf<Component>, Props> & {
    as?: ElementType;
}>): ComponentWithAs<Component, Props>;
