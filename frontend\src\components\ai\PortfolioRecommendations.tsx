import React, { useState } from 'react';
import {
  Box,
  Heading,
  Text,
  Flex,
  Badge,
  Skeleton,
  useColorModeValue,
  Divider,
  SimpleGrid,
  Button,
  ButtonGroup,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Progress,
  Tooltip,
  Icon,
  HStack,
  VStack,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
} from '@chakra-ui/react';
import { InfoIcon, QuestionIcon } from '@chakra-ui/icons';
import { usePortfolioRecommendations } from '../../hooks/useAI';
import { formatPercentage } from '../../utils/format';

interface PortfolioRecommendationsProps {
  initialRiskTolerance?: 'conservative' | 'moderate' | 'aggressive';
}

const PortfolioRecommendations: React.FC<PortfolioRecommendationsProps> = ({ 
  initialRiskTolerance = 'moderate' 
}) => {
  const [riskTolerance, setRiskTolerance] = useState<string>(initialRiskTolerance);
  const { data, isLoading, error } = usePortfolioRecommendations(riskTolerance);
  
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  
  if (isLoading) {
    return (
      <Box p={5} shadow="md" borderWidth="1px" borderRadius="lg" bg={bgColor}>
        <Skeleton height="20px" width="200px" mb={4} />
        <Skeleton height="300px" />
      </Box>
    );
  }
  
  if (error || !data) {
    return (
      <Box p={5} shadow="md" borderWidth="1px" borderRadius="lg" bg={bgColor}>
        <Heading size="md" mb={4}>AI Portfolio Recommendations</Heading>
        <Text color="red.500">Failed to load portfolio recommendations</Text>
      </Box>
    );
  }
  
  // Get risk color
  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'conservative':
        return 'blue';
      case 'moderate':
        return 'purple';
      case 'aggressive':
        return 'orange';
      default:
        return 'gray';
    }
  };
  
  // Get volatility color
  const getVolatilityColor = (volatility: string) => {
    switch (volatility) {
      case 'Low':
        return 'blue';
      case 'Medium':
        return 'purple';
      case 'High':
        return 'orange';
      default:
        return 'gray';
    }
  };
  
  return (
    <Box p={5} shadow="md" borderWidth="1px" borderRadius="lg" bg={bgColor} borderColor={borderColor}>
      <Flex 
        justify="space-between" 
        align={{ base: 'start', md: 'center' }} 
        mb={4}
        direction={{ base: 'column', md: 'row' }}
        gap={3}
      >
        <Heading size="md">AI Portfolio Recommendations</Heading>
        
        <ButtonGroup size="sm" isAttached variant="outline">
          <Button
            colorScheme={riskTolerance === 'conservative' ? 'blue' : 'gray'}
            onClick={() => setRiskTolerance('conservative')}
          >
            Conservative
          </Button>
          <Button
            colorScheme={riskTolerance === 'moderate' ? 'purple' : 'gray'}
            onClick={() => setRiskTolerance('moderate')}
          >
            Moderate
          </Button>
          <Button
            colorScheme={riskTolerance === 'aggressive' ? 'orange' : 'gray'}
            onClick={() => setRiskTolerance('aggressive')}
          >
            Aggressive
          </Button>
        </ButtonGroup>
      </Flex>
      
      <SimpleGrid columns={{ base: 1, md: 3 }} spacing={4} mb={6}>
        <Stat>
          <StatLabel>Risk Profile</StatLabel>
          <StatNumber>
            <Badge colorScheme={getRiskColor(data.riskTolerance)}>
              {data.riskTolerance.charAt(0).toUpperCase() + data.riskTolerance.slice(1)}
            </Badge>
          </StatNumber>
        </Stat>
        
        <Stat>
          <StatLabel>Expected Return</StatLabel>
          <StatNumber>{data.expectedReturn}</StatNumber>
          <StatHelpText>Annual</StatHelpText>
        </Stat>
        
        <Stat>
          <StatLabel>Volatility</StatLabel>
          <StatNumber>
            <Badge colorScheme={getVolatilityColor(data.volatility)}>
              {data.volatility}
            </Badge>
          </StatNumber>
          <StatHelpText>Rebalance: {data.rebalancingPeriod}</StatHelpText>
        </Stat>
      </SimpleGrid>
      
      <Divider my={4} />
      
      <Heading size="sm" mb={3}>Recommended Allocation</Heading>
      
      <Table variant="simple" size="sm">
        <Thead>
          <Tr>
            <Th>Asset</Th>
            <Th>Allocation</Th>
            <Th display={{ base: 'none', md: 'table-cell' }}>Reasoning</Th>
          </Tr>
        </Thead>
        <Tbody>
          {data.portfolio.map((asset, index) => (
            <Tr key={index}>
              <Td>
                <Flex align="center">
                  <Text fontWeight="medium">{asset.name}</Text>
                  <Badge ml={2} colorScheme="gray">{asset.currency}</Badge>
                </Flex>
              </Td>
              <Td>
                <Flex align="center" width="100%">
                  <Text mr={2} minW="45px">{(asset.allocation * 100).toFixed(1)}%</Text>
                  <Progress 
                    value={asset.allocation * 100} 
                    colorScheme="brand"
                    size="sm"
                    width="100%"
                    maxW="150px"
                  />
                </Flex>
              </Td>
              <Td display={{ base: 'none', md: 'table-cell' }}>
                <Text fontSize="xs">{asset.reasoning}</Text>
              </Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
      
      <Flex mt={4} align="center">
        <InfoIcon mr={2} color="gray.500" />
        <Text fontSize="sm" color="gray.500">
          These recommendations are generated by AI and should not be considered financial advice.
          Always do your own research before investing.
        </Text>
      </Flex>
    </Box>
  );
};

export default PortfolioRecommendations;
