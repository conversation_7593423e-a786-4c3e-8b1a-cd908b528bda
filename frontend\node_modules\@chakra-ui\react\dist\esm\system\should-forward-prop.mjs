'use client';
import { propNames } from '@chakra-ui/styled-system';

const allPropNames = /* @__PURE__ */ new Set([
  ...propNames,
  "textStyle",
  "layerStyle",
  "apply",
  "noOfLines",
  "focusBorderColor",
  "errorBorderColor",
  "as",
  "__css",
  "css",
  "sx"
]);
const validHTMLProps = /* @__PURE__ */ new Set([
  "htmlWidth",
  "htmlHeight",
  "htmlSize",
  "htmlTranslate"
]);
function shouldForwardProp(prop) {
  return (validHTMLProps.has(prop) || !allPropNames.has(prop)) && prop[0] !== "_";
}

export { shouldForwardProp };
