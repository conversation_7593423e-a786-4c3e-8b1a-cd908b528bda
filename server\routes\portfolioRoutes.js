/**
 * Portfolio Routes
 * 
 * This file defines all routes related to portfolio management.
 */

const express = require('express');
const router = express.Router();
const portfolioController = require('../controllers/portfolioController');
const { protect } = require('../middleware/authMiddleware');

// Apply authentication middleware to all portfolio routes
router.use(protect);

/**
 * @route   GET /api/portfolios
 * @desc    Get all portfolios for a user
 * @access  Private
 */
router.get('/', portfolioController.getPortfolios);

/**
 * @route   POST /api/portfolios
 * @desc    Create a new portfolio
 * @access  Private
 */
router.post('/', portfolioController.createPortfolio);

/**
 * @route   GET /api/portfolios/:id
 * @desc    Get a specific portfolio by ID
 * @access  Private
 */
router.get('/:id', portfolioController.getPortfolioById);

/**
 * @route   PUT /api/portfolios/:id
 * @desc    Update a portfolio
 * @access  Private
 */
router.put('/:id', portfolioController.updatePortfolio);

/**
 * @route   DELETE /api/portfolios/:id
 * @desc    Delete a portfolio
 * @access  Private
 */
router.delete('/:id', portfolioController.deletePortfolio);

/**
 * @route   POST /api/portfolios/:id/transactions
 * @desc    Add a transaction to a portfolio
 * @access  Private
 */
router.post('/:id/transactions', portfolioController.addTransaction);

/**
 * @route   PUT /api/portfolios/:portfolioId/transactions/:transactionId
 * @desc    Update a transaction
 * @access  Private
 */
router.put('/:portfolioId/transactions/:transactionId', portfolioController.updateTransaction);

/**
 * @route   DELETE /api/portfolios/:portfolioId/transactions/:transactionId
 * @desc    Delete a transaction
 * @access  Private
 */
router.delete('/:portfolioId/transactions/:transactionId', portfolioController.deleteTransaction);

/**
 * @route   GET /api/portfolios/:id/performance
 * @desc    Get portfolio performance
 * @access  Private
 */
router.get('/:id/performance', portfolioController.getPortfolioPerformance);

module.exports = router;
