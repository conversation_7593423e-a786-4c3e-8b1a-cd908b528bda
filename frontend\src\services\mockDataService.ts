// Mock data service for when the API is unavailable
import { v4 as uuidv4 } from 'uuid';

// Generate random price change percentage between -10% and +10%
const getRandomPriceChange = () => {
  return (Math.random() * 20 - 10).toFixed(2);
};

// Generate random volume between 100M and 10B
const getRandomVolume = () => {
  return Math.floor(Math.random() * 9900000000 + 100000000);
};

// Generate random market cap between 1B and 100B
const getRandomMarketCap = (price: number) => {
  return price * Math.floor(Math.random() * 990000000 + 10000000);
};

// Top 50 cryptocurrencies with realistic data
export const mockCryptos = [
  {
    _id: uuidv4(),
    name: 'Bitcoin',
    symbol: 'BTC',
    currentPrice: 50000 + Math.random() * 5000,
    priceChangePercentage24h: parseFloat(getRandomPriceChange()),
    volume24h: getRandomVolume(),
    marketCap: 1000000000000,
    rank: 1,
    image: 'https://cryptologos.cc/logos/bitcoin-btc-logo.png',
  },
  {
    _id: uuidv4(),
    name: 'Ethereum',
    symbol: 'ETH',
    currentPrice: 3000 + Math.random() * 300,
    priceChangePercentage24h: parseFloat(getRandomPriceChange()),
    volume24h: getRandomVolume(),
    marketCap: ************,
    rank: 2,
    image: 'https://cryptologos.cc/logos/ethereum-eth-logo.png',
  },
  {
    _id: uuidv4(),
    name: 'Tether',
    symbol: 'USDT',
    currentPrice: 0.99 + Math.random() * 0.02,
    priceChangePercentage24h: parseFloat((Math.random() * 0.5 - 0.25).toFixed(2)),
    volume24h: getRandomVolume(),
    marketCap: 80000000000,
    rank: 3,
    image: 'https://cryptologos.cc/logos/tether-usdt-logo.png',
  },
  {
    _id: uuidv4(),
    name: 'BNB',
    symbol: 'BNB',
    currentPrice: 400 + Math.random() * 40,
    priceChangePercentage24h: parseFloat(getRandomPriceChange()),
    volume24h: getRandomVolume(),
    marketCap: 65000000000,
    rank: 4,
    image: 'https://cryptologos.cc/logos/bnb-bnb-logo.png',
  },
  {
    _id: uuidv4(),
    name: 'Solana',
    symbol: 'SOL',
    currentPrice: 100 + Math.random() * 20,
    priceChangePercentage24h: parseFloat(getRandomPriceChange()),
    volume24h: getRandomVolume(),
    marketCap: 40000000000,
    rank: 5,
    image: 'https://cryptologos.cc/logos/solana-sol-logo.png',
  },
  {
    _id: uuidv4(),
    name: 'XRP',
    symbol: 'XRP',
    currentPrice: 0.5 + Math.random() * 0.1,
    priceChangePercentage24h: parseFloat(getRandomPriceChange()),
    volume24h: getRandomVolume(),
    marketCap: 25000000000,
    rank: 6,
    image: 'https://cryptologos.cc/logos/xrp-xrp-logo.png',
  },
  {
    _id: uuidv4(),
    name: 'Cardano',
    symbol: 'ADA',
    currentPrice: 0.4 + Math.random() * 0.1,
    priceChangePercentage24h: parseFloat(getRandomPriceChange()),
    volume24h: getRandomVolume(),
    marketCap: 15000000000,
    rank: 7,
    image: 'https://cryptologos.cc/logos/cardano-ada-logo.png',
  },
  {
    _id: uuidv4(),
    name: 'Dogecoin',
    symbol: 'DOGE',
    currentPrice: 0.08 + Math.random() * 0.02,
    priceChangePercentage24h: parseFloat(getRandomPriceChange()),
    volume24h: getRandomVolume(),
    marketCap: 12000000000,
    rank: 8,
    image: 'https://cryptologos.cc/logos/dogecoin-doge-logo.png',
  },
  {
    _id: uuidv4(),
    name: 'Polkadot',
    symbol: 'DOT',
    currentPrice: 6 + Math.random() * 1,
    priceChangePercentage24h: parseFloat(getRandomPriceChange()),
    volume24h: getRandomVolume(),
    marketCap: 8000000000,
    rank: 9,
    image: 'https://cryptologos.cc/logos/polkadot-new-dot-logo.png',
  },
  {
    _id: uuidv4(),
    name: 'Polygon',
    symbol: 'MATIC',
    currentPrice: 0.8 + Math.random() * 0.2,
    priceChangePercentage24h: parseFloat(getRandomPriceChange()),
    volume24h: getRandomVolume(),
    marketCap: 7500000000,
    rank: 10,
    image: 'https://cryptologos.cc/logos/polygon-matic-logo.png',
  },
  {
    _id: uuidv4(),
    name: 'Litecoin',
    symbol: 'LTC',
    currentPrice: 70 + Math.random() * 10,
    priceChangePercentage24h: parseFloat(getRandomPriceChange()),
    volume24h: getRandomVolume(),
    marketCap: 5000000000,
    rank: 11,
    image: 'https://cryptologos.cc/logos/litecoin-ltc-logo.png',
  },
  {
    _id: uuidv4(),
    name: 'Chainlink',
    symbol: 'LINK',
    currentPrice: 12 + Math.random() * 2,
    priceChangePercentage24h: parseFloat(getRandomPriceChange()),
    volume24h: getRandomVolume(),
    marketCap: 6000000000,
    rank: 12,
    image: 'https://cryptologos.cc/logos/chainlink-link-logo.png',
  },
  {
    _id: uuidv4(),
    name: 'Uniswap',
    symbol: 'UNI',
    currentPrice: 5 + Math.random() * 1,
    priceChangePercentage24h: parseFloat(getRandomPriceChange()),
    volume24h: getRandomVolume(),
    marketCap: 4000000000,
    rank: 13,
    image: 'https://cryptologos.cc/logos/uniswap-uni-logo.png',
  },
  {
    _id: uuidv4(),
    name: 'Avalanche',
    symbol: 'AVAX',
    currentPrice: 20 + Math.random() * 5,
    priceChangePercentage24h: parseFloat(getRandomPriceChange()),
    volume24h: getRandomVolume(),
    marketCap: 7000000000,
    rank: 14,
    image: 'https://cryptologos.cc/logos/avalanche-avax-logo.png',
  },
  {
    _id: uuidv4(),
    name: 'Stellar',
    symbol: 'XLM',
    currentPrice: 0.1 + Math.random() * 0.02,
    priceChangePercentage24h: parseFloat(getRandomPriceChange()),
    volume24h: getRandomVolume(),
    marketCap: 3000000000,
    rank: 15,
    image: 'https://cryptologos.cc/logos/stellar-xlm-logo.png',
  },
  // Add more cryptocurrencies as needed
];

// Generate more mock cryptocurrencies to have a total of 100
for (let i = mockCryptos.length; i < 100; i++) {
  const price = Math.random() * 10 + 0.1;
  mockCryptos.push({
    _id: uuidv4(),
    name: `Crypto ${i + 1}`,
    symbol: `CRY${i + 1}`,
    currentPrice: price,
    priceChangePercentage24h: parseFloat(getRandomPriceChange()),
    volume24h: getRandomVolume(),
    marketCap: getRandomMarketCap(price),
    rank: i + 1,
    image: `https://via.placeholder.com/32/4a90e2/ffffff?text=${String.fromCharCode(65 + (i % 26))}`,
  });
}

// Mock API response for getting cryptocurrencies
export const getMockCryptos = (page = 1, limit = 20, sortBy = 'marketCap', sortOrder = 'desc') => {
  // Sort the cryptocurrencies
  const sortedCryptos = [...mockCryptos].sort((a, b) => {
    const aValue = a[sortBy as keyof typeof a];
    const bValue = b[sortBy as keyof typeof b];

    if (typeof aValue === 'number' && typeof bValue === 'number') {
      return sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
    }

    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return sortOrder === 'asc'
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    }

    return 0;
  });

  // Paginate the results
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedCryptos = sortedCryptos.slice(startIndex, endIndex);

  // Calculate total pages
  const totalPages = Math.ceil(mockCryptos.length / limit);

  return {
    success: true,
    data: {
      cryptos: paginatedCryptos,
      page,
      limit,
      total: mockCryptos.length,
      pages: totalPages,
    },
  };
};

// Mock API response for getting a single cryptocurrency
export const getMockCryptoById = (id: string) => {
  const crypto = mockCryptos.find(c => c._id === id);

  if (!crypto) {
    return {
      success: false,
      message: 'Cryptocurrency not found',
    };
  }

  return {
    success: true,
    data: crypto,
  };
};

// Mock API response for getting market stats
export const getMockMarketStats = () => {
  const totalMarketCap = mockCryptos.reduce((sum, crypto) => sum + crypto.marketCap, 0);
  const total24hVolume = mockCryptos.reduce((sum, crypto) => sum + crypto.volume24h, 0);
  const btcDominance = (mockCryptos[0].marketCap / totalMarketCap) * 100;
  const ethDominance = (mockCryptos[1].marketCap / totalMarketCap) * 100;

  return {
    success: true,
    data: {
      totalMarketCap,
      total24hVolume,
      btcDominance,
      ethDominance,
      activeCryptocurrencies: mockCryptos.length,
      markets: 500,
    },
  };
};

export default {
  getMockCryptos,
  getMockCryptoById,
  getMockMarketStats,
};
