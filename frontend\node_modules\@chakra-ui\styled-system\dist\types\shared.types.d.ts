export interface BaseThemeTypings {
    conditions: never;
    borders: string;
    colors: string;
    breakpoints: string;
    colorSchemes: string;
    fonts: string;
    fontSizes: string;
    fontWeights: string;
    gradients: string;
    layerStyles: string;
    letterSpacings: string;
    lineHeights: string;
    radii: string;
    shadows: string;
    sizes: string;
    space: string;
    textStyles: string;
    zIndices: string;
    components: {
        [componentName: string]: {
            sizes: string;
            variants: string;
        };
    };
}
export {};
