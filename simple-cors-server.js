const express = require('express');
const cors = require('cors');
const app = express();

// Enable CORS for all routes
app.use(cors({
  origin: '*', // Allow all origins
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}));

// Add CORS preflight
app.options('*', cors());

// Parse JSON bodies
app.use(express.json());

// Logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'API is running',
    status: 'ok'
  });
});

// API root endpoint
app.get('/api', (req, res) => {
  res.json({
    success: true,
    message: 'API is running',
    version: '1.0.0'
  });
});

// Mock data
const mockCryptos = [
  {
    _id: '1',
    name: 'Bitcoin',
    symbol: 'BTC',
    currentPrice: 50000,
    marketCap: 1000000000000,
    volume24h: 50000000000,
    priceChange24h: 1000,
    priceChangePercentage24h: 2,
    circulatingSupply: 19000000,
    totalSupply: 21000000,
    maxSupply: 21000000,
    lastUpdated: new Date(),
    image: 'https://assets.coingecko.com/coins/images/1/large/bitcoin.png'
  },
  {
    _id: '2',
    name: 'Ethereum',
    symbol: 'ETH',
    currentPrice: 3000,
    marketCap: 400000000000,
    volume24h: 20000000000,
    priceChange24h: 100,
    priceChangePercentage24h: 3.5,
    circulatingSupply: 120000000,
    totalSupply: 120000000,
    maxSupply: null,
    lastUpdated: new Date(),
    image: 'https://assets.coingecko.com/coins/images/279/large/ethereum.png'
  }
];

// Get all cryptos
app.get('/api/cryptos', (req, res) => {
  console.log('Received request for /api/cryptos');
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.perPage) || 20;
    
    // Calculate pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedCryptos = mockCryptos.slice(startIndex, endIndex);
    
    const response = {
      success: true,
      data: {
        cryptos: paginatedCryptos,
        page,
        pages: Math.ceil(mockCryptos.length / limit),
        total: mockCryptos.length,
      },
    };
    
    console.log('Sending response for /api/cryptos:', JSON.stringify(response).substring(0, 100) + '...');
    res.json(response);
  } catch (error) {
    console.error('Error fetching cryptos:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching cryptocurrencies',
      error: error.message
    });
  }
});

// Get trending cryptos
app.get('/api/cryptos/trending', (req, res) => {
  console.log('Received request for /api/cryptos/trending');
  try {
    // Use all cryptos as trending
    const trendingCryptos = mockCryptos.map(crypto => ({
      _id: crypto._id,
      name: crypto.name,
      symbol: crypto.symbol,
      image: crypto.image,
      rank: parseInt(crypto._id),
      priceBtc: crypto.currentPrice / 50000, // Approximate BTC value
      score: 3 - parseInt(crypto._id), // Higher score for lower ID
    }));
    
    const response = {
      success: true,
      data: trendingCryptos,
    };
    
    console.log('Sending response for /api/cryptos/trending:', JSON.stringify(response).substring(0, 100) + '...');
    res.json(response);
  } catch (error) {
    console.error('Error in trending cryptos endpoint:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching trending cryptocurrencies',
      error: error.message
    });
  }
});

// Start server
const PORT = 3001;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`API available at http://localhost:${PORT}/api`);
});
