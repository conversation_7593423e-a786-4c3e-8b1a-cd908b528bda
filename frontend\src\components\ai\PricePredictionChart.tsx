import React from 'react';
import {
  Box,
  Heading,
  Text,
  Flex,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Badge,
  Skeleton,
  useColorModeValue,
} from '@chakra-ui/react';
import { InfoIcon } from '@chakra-ui/icons';
import { ResponsiveContainer, AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip } from 'recharts';
import { usePricePrediction } from '../../hooks/useAI';
import { formatCurrency } from '../../utils/format';

interface PricePredictionChartProps {
  cryptoId: string;
  symbol: string;
}

const PricePredictionChart: React.FC<PricePredictionChartProps> = ({ cryptoId, symbol }) => {
  const { data, isLoading, error } = usePricePrediction(cryptoId);
  
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const areaColor = useColorModeValue('brand.500', 'brand.200');
  
  if (isLoading) {
    return (
      <Box p={5} shadow="md" borderWidth="1px" borderRadius="lg" bg={bgColor}>
        <Skeleton height="20px" width="200px" mb={4} />
        <Skeleton height="300px" />
      </Box>
    );
  }
  
  if (error || !data) {
    return (
      <Box p={5} shadow="md" borderWidth="1px" borderRadius="lg" bg={bgColor}>
        <Heading size="md" mb={4}>Price Prediction</Heading>
        <Text color="red.500">Failed to load price predictions</Text>
      </Box>
    );
  }
  
  const chartData = data.predictions.map(pred => ({
    date: pred.date,
    price: pred.price,
    day: `Day ${pred.day}`,
  }));
  
  // Add current price as day 0
  chartData.unshift({
    date: new Date().toISOString().split('T')[0],
    price: data.currentPrice,
    day: 'Today',
  });
  
  // Calculate price change
  const lastPrediction = data.predictions[data.predictions.length - 1];
  const priceChange = lastPrediction.price - data.currentPrice;
  const percentChange = (priceChange / data.currentPrice) * 100;
  
  return (
    <Box p={5} shadow="md" borderWidth="1px" borderRadius="lg" bg={bgColor} borderColor={borderColor}>
      <Flex justify="space-between" align="center" mb={4}>
        <Heading size="md">AI Price Prediction ({symbol})</Heading>
        <Badge colorScheme={data.confidence > 70 ? 'green' : data.confidence > 50 ? 'yellow' : 'red'}>
          {data.confidence}% Confidence
        </Badge>
      </Flex>
      
      <Flex mb={6} wrap="wrap">
        <Stat flex="1" minW="150px">
          <StatLabel>Current Price</StatLabel>
          <StatNumber>{formatCurrency(data.currentPrice)}</StatNumber>
        </Stat>
        
        <Stat flex="1" minW="150px">
          <StatLabel>7-Day Prediction</StatLabel>
          <StatNumber>{formatCurrency(lastPrediction.price)}</StatNumber>
          <StatHelpText color={percentChange >= 0 ? 'green.500' : 'red.500'}>
            {percentChange >= 0 ? '↑' : '↓'} {Math.abs(percentChange).toFixed(2)}%
          </StatHelpText>
        </Stat>
      </Flex>
      
      <Box h="300px">
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart data={chartData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
            <defs>
              <linearGradient id="colorPrice" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor={areaColor} stopOpacity={0.8} />
                <stop offset="95%" stopColor={areaColor} stopOpacity={0} />
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
            <XAxis dataKey="day" />
            <YAxis 
              domain={['auto', 'auto']}
              tickFormatter={(value) => formatCurrency(value, 'USD')}
            />
            <Tooltip 
              formatter={(value: number) => [formatCurrency(value, 'USD'), 'Predicted Price']}
              labelFormatter={(label) => `${label}`}
            />
            <Area 
              type="monotone" 
              dataKey="price" 
              stroke={areaColor} 
              fillOpacity={1} 
              fill="url(#colorPrice)" 
            />
          </AreaChart>
        </ResponsiveContainer>
      </Box>
      
      <Flex mt={4} align="center">
        <InfoIcon mr={2} color="gray.500" />
        <Text fontSize="sm" color="gray.500">
          Prediction methodology: {data.methodology}
        </Text>
      </Flex>
    </Box>
  );
};

export default PricePredictionChart;
