!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("react")):"function"==typeof define&&define.amd?define(["react"],t):e.withSideEffect=t(e.React)}(this,function(e){"use strict";var t="default"in e?e.default:e;function n(e,t){return(n=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}return function(o,r){if("production"!==process.env.NODE_ENV){if("function"!=typeof o)throw new Error("Expected reducePropsToState to be a function.");if("function"!=typeof r)throw new Error("Expected handleStateChangeOnClient to be a function.")}return function(i){if("production"!==process.env.NODE_ENV&&"function"!=typeof i)throw new Error("Expected WrappedComponent to be a React component.");var c,u=[];function p(){c=o(u.map(function(e){return e.props})),r(c)}var f,a,d,s=function(e){var o,r;function f(){return e.apply(this,arguments)||this}r=e,(o=f).prototype=Object.create(r.prototype),o.prototype.constructor=o,n(o,r),f.peek=function(){return c};var a=f.prototype;return a.componentDidMount=function(){u.push(this),p()},a.componentDidUpdate=function(){p()},a.componentWillUnmount=function(){var e=u.indexOf(this);u.splice(e,1),p()},a.render=function(){return t.createElement(i,this.props)},f}(e.PureComponent);return f=s,a="displayName",d="SideEffect("+function(e){return e.displayName||e.name||"Component"}(i)+")",a in f?Object.defineProperty(f,a,{value:d,enumerable:!0,configurable:!0,writable:!0}):f[a]=d,s}}});
