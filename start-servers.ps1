Write-Host "Starting Crypto Application Servers..." -ForegroundColor Green

# Start the backend server
Start-Process -FilePath "cmd.exe" -ArgumentList "/c cd server && node simple-cors-server.js"

# Wait for the backend to initialize
Write-Host "Waiting for backend to initialize..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

# Start the frontend server
Start-Process -FilePath "cmd.exe" -ArgumentList "/c cd frontend && npm run dev"

Write-Host "Servers started successfully!" -ForegroundColor Green
Write-Host "Backend: http://localhost:3001" -ForegroundColor Cyan
Write-Host "Frontend: http://localhost:3002" -ForegroundColor Cyan
