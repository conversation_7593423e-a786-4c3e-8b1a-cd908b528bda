/**
 * AI Routes
 *
 * This file defines all routes related to AI-powered features.
 */

const express = require('express');
const router = express.Router();
const aiController = require('../controllers/aiController');
const { protect } = require('../middleware/authMiddleware');
const enhancedCoinGeckoService = require('../services/enhancedCoinGeckoService');

/**
 * @route   GET /api/ai/price-prediction/:id
 * @desc    Get price prediction for a cryptocurrency
 * @access  Public
 */
router.get('/price-prediction/:id', aiController.getPricePrediction);

/**
 * @route   GET /api/ai/trading-signals/:id
 * @desc    Get trading signals for a cryptocurrency
 * @access  Public
 */
router.get('/trading-signals/:id', aiController.getTradingSignals);

/**
 * @route   GET /api/ai/sentiment/:id
 * @desc    Get sentiment analysis for a cryptocurrency
 * @access  Public
 */
router.get('/sentiment/:id', aiController.getSentimentAnalysis);

/**
 * @route   GET /api/ai/portfolio-recommendations
 * @desc    Get portfolio recommendations
 * @access  Private
 */
router.get('/portfolio-recommendations', protect, aiController.getPortfolioRecommendations);

/**
 * @route   GET /api/ai/dashboard
 * @desc    Get AI insights dashboard data
 * @access  Public
 */
router.get('/dashboard', aiController.getAiDashboard);

/**
 * @route   GET /api/ai/insights/:id
 * @desc    Get all AI insights for a specific cryptocurrency
 * @access  Public
 */
router.get('/insights/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Get cryptocurrency data
    const crypto = await enhancedCoinGeckoService.getCoinDetails(id);

    // Format crypto data for AI service
    const formattedCrypto = {
      _id: crypto.id,
      name: crypto.name,
      symbol: crypto.symbol,
      currentPrice: crypto.market_data?.current_price?.usd || 0,
      priceChangePercentage24h: crypto.market_data?.price_change_percentage_24h || 0,
    };

    // Get all insights
    const prediction = await aiController.getPricePrediction({ params: { id }, query: {} }, { json: data => data });
    const signals = await aiController.getTradingSignals({ params: { id } }, { json: data => data });
    const sentiment = await aiController.getSentimentAnalysis({ params: { id } }, { json: data => data });

    res.json({
      success: true,
      data: {
        crypto: formattedCrypto,
        prediction: prediction.data,
        signals: signals.data,
        sentiment: sentiment.data,
      },
    });
  } catch (error) {
    console.error(`Error in insights controller for ${req.params.id}:`, error);
    return res.status(500).json({
      success: false,
      message: 'Failed to generate insights',
      error: error.message,
    });
  }
});

// Legacy routes for backward compatibility
router.get('/predictions/:id', aiController.getPricePrediction);
router.get('/signals/:id', aiController.getTradingSignals);

module.exports = router;
