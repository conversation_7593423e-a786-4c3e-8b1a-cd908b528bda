module.exports={A:{A:{"1":"F A B","2":"K D E nC"},B:{"1":"0 9 C L M G N O P Q H R S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x y z AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB I"},C:{"1":"0 1 2 3 4 5 6 7 8 9 G N O P RB SB TB UB VB WB XB YB ZB aB bB cB dB eB fB gB hB iB jB kB lB mB nB oB pB qB rB sB tB uB vB wB NC xB OC yB zB 0B 1B 2B 3B 4B 5B 6B 7B 8B 9B AC BC CC DC EC Q H R PC S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x y z AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB I QC FC RC pC qC","2":"oC MC J QB K D E F A B C L M rC sC"},D:{"1":"0 9 UB VB WB XB YB ZB aB bB cB dB eB fB gB hB iB jB kB lB mB nB oB pB qB rB sB tB uB vB wB NC xB OC yB zB 0B 1B 2B 3B 4B 5B 6B 7B 8B 9B AC BC CC DC EC Q H R S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x y z AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB I QC FC RC","2":"1 2 3 4 5 6 7 8 J QB K D E F A B C L M G N O P RB SB TB"},E:{"1":"B C L M G TC GC HC yC zC 0C UC VC IC 1C JC WC XC YC ZC aC 2C KC bC cC dC eC fC 3C LC gC hC iC jC kC 4C","2":"J QB K D E F A tC SC uC vC wC xC"},F:{"1":"0 1 2 3 4 5 6 7 8 O P RB SB TB UB VB WB XB YB ZB aB bB cB dB eB fB gB hB iB jB kB lB mB nB oB pB qB rB sB tB uB vB wB xB yB zB 0B 1B 2B 3B 4B 5B 6B 7B 8B 9B AC BC CC DC EC Q H R PC S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x y z HC","2":"F B G N 5C 6C 7C 8C GC lC 9C","16":"C"},G:{"1":"ID JD KD LD MD ND OD PD QD RD SD TD UC VC IC UD JC WC XC YC ZC aC VD KC bC cC dC eC fC WD LC gC hC iC jC kC","2":"E SC AD mC BD CD DD ED FD GD HD"},H:{"2":"XD"},I:{"1":"I cD dD","2":"MC J YD ZD aD bD mC"},J:{"2":"D A"},K:{"1":"H HC","2":"A B GC lC","16":"C"},L:{"1":"I"},M:{"1":"FC"},N:{"1":"A B"},O:{"1":"IC"},P:{"1":"1 2 3 4 5 6 7 8 J eD fD gD hD iD TC jD kD lD mD nD JC KC LC oD"},Q:{"1":"pD"},R:{"1":"qD"},S:{"1":"rD sD"}},B:5,C:"KeyboardEvent.getModifierState()",D:true};
