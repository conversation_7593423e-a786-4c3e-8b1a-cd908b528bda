const express = require('express');
const cors = require('cors');
const mongoose = require('mongoose');
const { v4: uuidv4 } = require('uuid');

// Create Express app
const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// MongoDB Connection String - Using local mock data instead of actual MongoDB connection
// const MONGODB_URI = 'mongodb+srv://ziqra:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0';

// Mock data storage (in-memory database)
let mockCryptos = [];

// Generate random price change percentage between -10% and +10%
const getRandomPriceChange = () => {
  return parseFloat((Math.random() * 20 - 10).toFixed(2));
};

// Generate random volume between 100M and 10B
const getRandomVolume = () => {
  return Math.floor(Math.random() * 9900000000 + 100000000);
};

// Initialize mock data
const initMockData = () => {
  // Top cryptocurrencies with realistic data
  mockCryptos = [
    {
      _id: uuidv4(),
      name: 'Bitcoin',
      symbol: 'BTC',
      currentPrice: 50000 + Math.random() * 5000,
      priceChangePercentage24h: getRandomPriceChange(),
      volume24h: getRandomVolume(),
      marketCap: 1000000000000,
      rank: 1,
      image: 'https://cryptologos.cc/logos/bitcoin-btc-logo.png',
    },
    {
      _id: uuidv4(),
      name: 'Ethereum',
      symbol: 'ETH',
      currentPrice: 3000 + Math.random() * 300,
      priceChangePercentage24h: getRandomPriceChange(),
      volume24h: getRandomVolume(),
      marketCap: ************,
      rank: 2,
      image: 'https://cryptologos.cc/logos/ethereum-eth-logo.png',
    },
    {
      _id: uuidv4(),
      name: 'Tether',
      symbol: 'USDT',
      currentPrice: 0.99 + Math.random() * 0.02,
      priceChangePercentage24h: parseFloat((Math.random() * 0.5 - 0.25).toFixed(2)),
      volume24h: getRandomVolume(),
      marketCap: 80000000000,
      rank: 3,
      image: 'https://cryptologos.cc/logos/tether-usdt-logo.png',
    },
    {
      _id: uuidv4(),
      name: 'BNB',
      symbol: 'BNB',
      currentPrice: 400 + Math.random() * 40,
      priceChangePercentage24h: getRandomPriceChange(),
      volume24h: getRandomVolume(),
      marketCap: 65000000000,
      rank: 4,
      image: 'https://cryptologos.cc/logos/bnb-bnb-logo.png',
    },
    {
      _id: uuidv4(),
      name: 'Solana',
      symbol: 'SOL',
      currentPrice: 100 + Math.random() * 20,
      priceChangePercentage24h: getRandomPriceChange(),
      volume24h: getRandomVolume(),
      marketCap: 40000000000,
      rank: 5,
      image: 'https://cryptologos.cc/logos/solana-sol-logo.png',
    }
  ];

  // Generate more mock cryptocurrencies to have a total of 100
  for (let i = mockCryptos.length; i < 100; i++) {
    const price = Math.random() * 10 + 0.1;
    mockCryptos.push({
      _id: uuidv4(),
      name: `Crypto ${i + 1}`,
      symbol: `CRY${i + 1}`,
      currentPrice: price,
      priceChangePercentage24h: getRandomPriceChange(),
      volume24h: getRandomVolume(),
      marketCap: price * Math.floor(Math.random() * 990000000 + 10000000),
      rank: i + 1,
      image: `https://via.placeholder.com/32/2196f3/ffffff?text=${i + 1}`,
    });
  }
};

// Initialize mock data
initMockData();

// We're using mock data instead of MongoDB for simplicity

// Routes
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'API is running',
    timestamp: new Date().toISOString(),
    mongodb: {
      connected: true,
      host: 'mock-db',
      name: 'crypto-tracker',
      error: null,
      fallbackMode: true,
    }
  });
});

// Get trending cryptocurrencies
app.get('/api/cryptos/trending', (req, res) => {
  try {
    // Sort by price change percentage and get top 5
    const trendingCryptos = [...mockCryptos]
      .sort((a, b) => b.priceChangePercentage24h - a.priceChangePercentage24h)
      .slice(0, 5);

    res.json({
      success: true,
      data: trendingCryptos
    });
  } catch (error) {
    console.error('Error fetching trending cryptocurrencies:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch trending cryptocurrencies',
      error: error.message
    });
  }
});

// Search cryptocurrencies
app.get('/api/cryptos/search/:query', (req, res) => {
  try {
    const query = req.params.query.toLowerCase();
    const cryptos = mockCryptos.filter(crypto =>
      crypto.name.toLowerCase().includes(query) ||
      crypto.symbol.toLowerCase().includes(query)
    ).slice(0, 10);

    res.json({
      success: true,
      data: {
        coins: cryptos,
        categories: [],
        exchanges: []
      }
    });
  } catch (error) {
    console.error(`Error searching cryptocurrencies for "${req.params.query}":`, error);
    res.status(500).json({
      success: false,
      message: 'Failed to search cryptocurrencies',
      error: error.message
    });
  }
});

// Get all cryptocurrencies
app.get('/api/cryptos', (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.perPage) || 20;
    const sortBy = req.query.sortBy || 'marketCap';
    const sortOrder = req.query.sortOrder || 'desc';

    // Sort the cryptocurrencies
    const sortedCryptos = [...mockCryptos].sort((a, b) => {
      if (sortOrder === 'asc') {
        return a[sortBy] - b[sortBy];
      } else {
        return b[sortBy] - a[sortBy];
      }
    });

    // Paginate the results
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedCryptos = sortedCryptos.slice(startIndex, endIndex);

    res.json({
      success: true,
      data: {
        cryptos: paginatedCryptos,
        page,
        pages: Math.ceil(mockCryptos.length / limit),
        total: mockCryptos.length,
      }
    });
  } catch (error) {
    console.error('Error fetching cryptocurrencies:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch cryptocurrencies',
      error: error.message
    });
  }
});

// Get cryptocurrency by ID
app.get('/api/cryptos/:id', (req, res) => {
  try {
    const crypto = mockCryptos.find(c => c._id === req.params.id);

    if (!crypto) {
      return res.status(404).json({
        success: false,
        message: 'Cryptocurrency not found'
      });
    }

    res.json({
      success: true,
      data: crypto
    });
  } catch (error) {
    console.error(`Error fetching cryptocurrency ${req.params.id}:`, error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch cryptocurrency',
      error: error.message
    });
  }
});

// Authentication routes
app.post('/api/auth/login', (req, res) => {
  // Mock login - in a real app, this would validate credentials against the database
  const { email, password } = req.body;

  if (email === '<EMAIL>' && password === 'password') {
    res.json({
      success: true,
      data: {
        id: uuidv4(),
        username: 'testuser',
        email: '<EMAIL>',
        token: 'mock-jwt-token',
        role: 'user'
      }
    });
  } else {
    res.status(401).json({
      success: false,
      message: 'Invalid credentials'
    });
  }
});

app.post('/api/auth/register', (req, res) => {
  // Mock registration - in a real app, this would create a new user in the database
  const { username, email, password } = req.body;

  res.json({
    success: true,
    data: {
      id: uuidv4(),
      username,
      email,
      token: 'mock-jwt-token',
      role: 'user'
    }
  });
});

app.get('/api/auth/me', (req, res) => {
  // Mock user profile - in a real app, this would fetch the user from the database
  const token = req.headers.authorization?.split(' ')[1];

  if (token === 'mock-jwt-token') {
    res.json({
      success: true,
      data: {
        id: uuidv4(),
        username: 'testuser',
        email: '<EMAIL>',
        role: 'user'
      }
    });
  } else {
    res.status(401).json({
      success: false,
      message: 'Unauthorized'
    });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
