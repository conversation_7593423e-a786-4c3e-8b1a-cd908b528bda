/**
 * Watchlist Routes
 *
 * This file defines all routes related to watchlist management.
 */

const express = require('express');
const router = express.Router();
const watchlistController = require('../controllers/watchlistController');
const { protect } = require('../middleware/authMiddleware');
const { validateRequest } = require('../middleware/validationMiddleware');
const { watchlistCreateSchema, watchlistAddCryptoSchema } = require('../utils/validation');

// Apply authentication middleware to all watchlist routes
router.use(protect);

/**
 * @route   GET /api/watchlists
 * @desc    Get all watchlists for a user
 * @access  Private
 */
router.get('/', watchlistController.getWatchlists);

/**
 * @route   POST /api/watchlists
 * @desc    Create a new watchlist
 * @access  Private
 */
router.post('/', validateRequest(watchlistCreateSchema), watchlistController.createWatchlist);

/**
 * @route   GET /api/watchlists/default
 * @desc    Get default watchlist for a user
 * @access  Private
 */
router.get('/default', watchlistController.getDefaultWatchlist);

/**
 * @route   GET /api/watchlists/:id
 * @desc    Get a specific watchlist by ID
 * @access  Private
 */
router.get('/:id', watchlistController.getWatchlistById);

/**
 * @route   PUT /api/watchlists/:id
 * @desc    Update a watchlist
 * @access  Private
 */
router.put('/:id', validateRequest(watchlistCreateSchema), watchlistController.updateWatchlist);

/**
 * @route   DELETE /api/watchlists/:id
 * @desc    Delete a watchlist
 * @access  Private
 */
router.delete('/:id', watchlistController.deleteWatchlist);

/**
 * @route   POST /api/watchlists/:id/cryptos
 * @desc    Add a cryptocurrency to a watchlist
 * @access  Private
 */
router.post('/:id/cryptos', validateRequest(watchlistAddCryptoSchema), watchlistController.addCryptoToWatchlist);

/**
 * @route   DELETE /api/watchlists/:id/cryptos/:cryptoId
 * @desc    Remove a cryptocurrency from a watchlist
 * @access  Private
 */
router.delete('/:id/cryptos/:cryptoId', watchlistController.removeCryptoFromWatchlist);

module.exports = router;
