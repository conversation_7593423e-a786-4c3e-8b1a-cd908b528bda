export declare const avatarTheme: {
    baseStyle?: ((props: import("@chakra-ui/styled-system").StyleFunctionProps) => {
        badge: {
            [x: string]: string | {
                [x: string]: string;
            };
            borderRadius: string;
            border: string;
            borderColor: string;
            _dark: {
                [x: string]: string;
            };
        };
        excessLabel: {
            [x: string]: string | {
                [x: string]: string;
            };
            bg: string;
            fontSize: string;
            width: string;
            height: string;
            lineHeight: string;
            _dark: {
                [x: string]: string;
            };
        };
        container: {
            [x: string]: string | {
                [x: string]: string;
            };
            bg: string;
            fontSize: string;
            color: string;
            borderColor: string;
            verticalAlign: string;
            width: string;
            height: string;
            "&:not([data-loaded])": {
                [x: string]: string;
            };
            _dark: {
                [x: string]: string;
            };
        };
        label: {
            fontSize: string;
            lineHeight: string;
        };
    }) | undefined;
    sizes?: {
        "2xs": {
            container: {
                [x: string]: string | 1 | {
                    sm: string;
                    md: string;
                    lg: string;
                    xl: string;
                } | 0.5 | 1.5 | 2 | 2.5 | 3 | 3.5 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 12 | 14 | 16 | 20 | 24 | 28 | 32 | 36 | 40 | 44 | 48 | 52 | 56 | 60 | 64 | 72 | 80 | 96;
            };
            excessLabel: {
                [x: string]: string | 1 | {
                    sm: string;
                    md: string;
                    lg: string;
                    xl: string;
                } | 0.5 | 1.5 | 2 | 2.5 | 3 | 3.5 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 12 | 14 | 16 | 20 | 24 | 28 | 32 | 36 | 40 | 44 | 48 | 52 | 56 | 60 | 64 | 72 | 80 | 96;
            };
        };
        xs: {
            container: {
                [x: string]: string | 1 | {
                    sm: string;
                    md: string;
                    lg: string;
                    xl: string;
                } | 0.5 | 1.5 | 2 | 2.5 | 3 | 3.5 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 12 | 14 | 16 | 20 | 24 | 28 | 32 | 36 | 40 | 44 | 48 | 52 | 56 | 60 | 64 | 72 | 80 | 96;
            };
            excessLabel: {
                [x: string]: string | 1 | {
                    sm: string;
                    md: string;
                    lg: string;
                    xl: string;
                } | 0.5 | 1.5 | 2 | 2.5 | 3 | 3.5 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 12 | 14 | 16 | 20 | 24 | 28 | 32 | 36 | 40 | 44 | 48 | 52 | 56 | 60 | 64 | 72 | 80 | 96;
            };
        };
        sm: {
            container: {
                [x: string]: string | 1 | {
                    sm: string;
                    md: string;
                    lg: string;
                    xl: string;
                } | 0.5 | 1.5 | 2 | 2.5 | 3 | 3.5 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 12 | 14 | 16 | 20 | 24 | 28 | 32 | 36 | 40 | 44 | 48 | 52 | 56 | 60 | 64 | 72 | 80 | 96;
            };
            excessLabel: {
                [x: string]: string | 1 | {
                    sm: string;
                    md: string;
                    lg: string;
                    xl: string;
                } | 0.5 | 1.5 | 2 | 2.5 | 3 | 3.5 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 12 | 14 | 16 | 20 | 24 | 28 | 32 | 36 | 40 | 44 | 48 | 52 | 56 | 60 | 64 | 72 | 80 | 96;
            };
        };
        md: {
            container: {
                [x: string]: string | 1 | {
                    sm: string;
                    md: string;
                    lg: string;
                    xl: string;
                } | 0.5 | 1.5 | 2 | 2.5 | 3 | 3.5 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 12 | 14 | 16 | 20 | 24 | 28 | 32 | 36 | 40 | 44 | 48 | 52 | 56 | 60 | 64 | 72 | 80 | 96;
            };
            excessLabel: {
                [x: string]: string | 1 | {
                    sm: string;
                    md: string;
                    lg: string;
                    xl: string;
                } | 0.5 | 1.5 | 2 | 2.5 | 3 | 3.5 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 12 | 14 | 16 | 20 | 24 | 28 | 32 | 36 | 40 | 44 | 48 | 52 | 56 | 60 | 64 | 72 | 80 | 96;
            };
        };
        lg: {
            container: {
                [x: string]: string | 1 | {
                    sm: string;
                    md: string;
                    lg: string;
                    xl: string;
                } | 0.5 | 1.5 | 2 | 2.5 | 3 | 3.5 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 12 | 14 | 16 | 20 | 24 | 28 | 32 | 36 | 40 | 44 | 48 | 52 | 56 | 60 | 64 | 72 | 80 | 96;
            };
            excessLabel: {
                [x: string]: string | 1 | {
                    sm: string;
                    md: string;
                    lg: string;
                    xl: string;
                } | 0.5 | 1.5 | 2 | 2.5 | 3 | 3.5 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 12 | 14 | 16 | 20 | 24 | 28 | 32 | 36 | 40 | 44 | 48 | 52 | 56 | 60 | 64 | 72 | 80 | 96;
            };
        };
        xl: {
            container: {
                [x: string]: string | 1 | {
                    sm: string;
                    md: string;
                    lg: string;
                    xl: string;
                } | 0.5 | 1.5 | 2 | 2.5 | 3 | 3.5 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 12 | 14 | 16 | 20 | 24 | 28 | 32 | 36 | 40 | 44 | 48 | 52 | 56 | 60 | 64 | 72 | 80 | 96;
            };
            excessLabel: {
                [x: string]: string | 1 | {
                    sm: string;
                    md: string;
                    lg: string;
                    xl: string;
                } | 0.5 | 1.5 | 2 | 2.5 | 3 | 3.5 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 12 | 14 | 16 | 20 | 24 | 28 | 32 | 36 | 40 | 44 | 48 | 52 | 56 | 60 | 64 | 72 | 80 | 96;
            };
        };
        "2xl": {
            container: {
                [x: string]: string | 1 | {
                    sm: string;
                    md: string;
                    lg: string;
                    xl: string;
                } | 0.5 | 1.5 | 2 | 2.5 | 3 | 3.5 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 12 | 14 | 16 | 20 | 24 | 28 | 32 | 36 | 40 | 44 | 48 | 52 | 56 | 60 | 64 | 72 | 80 | 96;
            };
            excessLabel: {
                [x: string]: string | 1 | {
                    sm: string;
                    md: string;
                    lg: string;
                    xl: string;
                } | 0.5 | 1.5 | 2 | 2.5 | 3 | 3.5 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 12 | 14 | 16 | 20 | 24 | 28 | 32 | 36 | 40 | 44 | 48 | 52 | 56 | 60 | 64 | 72 | 80 | 96;
            };
        };
        full: {
            container: {
                [x: string]: string | 1 | {
                    sm: string;
                    md: string;
                    lg: string;
                    xl: string;
                } | 0.5 | 1.5 | 2 | 2.5 | 3 | 3.5 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 12 | 14 | 16 | 20 | 24 | 28 | 32 | 36 | 40 | 44 | 48 | 52 | 56 | 60 | 64 | 72 | 80 | 96;
            };
            excessLabel: {
                [x: string]: string | 1 | {
                    sm: string;
                    md: string;
                    lg: string;
                    xl: string;
                } | 0.5 | 1.5 | 2 | 2.5 | 3 | 3.5 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 12 | 14 | 16 | 20 | 24 | 28 | 32 | 36 | 40 | 44 | 48 | 52 | 56 | 60 | 64 | 72 | 80 | 96;
            };
        };
    } | undefined;
    variants?: {
        [key: string]: import("@chakra-ui/styled-system").PartsStyleInterpolation<{
            keys: ("container" | "label" | "badge" | "excessLabel" | "group")[];
        }>;
    } | undefined;
    defaultProps?: {
        size?: "md" | "full" | "2xs" | "xs" | "sm" | "lg" | "xl" | "2xl" | undefined;
        variant?: string | number | undefined;
        colorScheme?: string | undefined;
    } | undefined;
    parts: ("container" | "label" | "badge" | "excessLabel" | "group")[];
};
