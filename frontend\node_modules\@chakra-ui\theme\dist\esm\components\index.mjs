import { accordionTheme } from './accordion.mjs';
import { alertTheme } from './alert.mjs';
import { avatarTheme } from './avatar.mjs';
import { badgeTheme } from './badge.mjs';
import { breadcrumbTheme } from './breadcrumb.mjs';
import { buttonTheme } from './button.mjs';
import { cardTheme } from './card.mjs';
import { checkboxTheme } from './checkbox.mjs';
import { closeButtonTheme } from './close-button.mjs';
import { codeTheme } from './code.mjs';
import { containerTheme } from './container.mjs';
import { dividerTheme } from './divider.mjs';
import { drawerTheme } from './drawer.mjs';
import { editableTheme } from './editable.mjs';
import { formTheme } from './form-control.mjs';
import { formErrorTheme } from './form-error.mjs';
import { formLabelTheme } from './form-label.mjs';
import { headingTheme } from './heading.mjs';
import { inputTheme } from './input.mjs';
import { kbdTheme } from './kbd.mjs';
import { linkTheme } from './link.mjs';
import { listTheme } from './list.mjs';
import { menuTheme } from './menu.mjs';
import { modalTheme } from './modal.mjs';
import { numberInputTheme } from './number-input.mjs';
import { pinInputTheme } from './pin-input.mjs';
import { popoverTheme } from './popover.mjs';
import { progressTheme } from './progress.mjs';
import { radioTheme } from './radio.mjs';
import { selectTheme } from './select.mjs';
import { skeletonTheme } from './skeleton.mjs';
import { skipLinkTheme } from './skip-link.mjs';
import { sliderTheme } from './slider.mjs';
import { spinnerTheme } from './spinner.mjs';
import { statTheme } from './stat.mjs';
import { stepperTheme } from './stepper.mjs';
import { switchTheme } from './switch.mjs';
import { tableTheme } from './table.mjs';
import { tabsTheme } from './tabs.mjs';
import { tagTheme } from './tag.mjs';
import { textareaTheme } from './textarea.mjs';
import { tooltipTheme } from './tooltip.mjs';

const components = {
  Accordion: accordionTheme,
  Alert: alertTheme,
  Avatar: avatarTheme,
  Badge: badgeTheme,
  Breadcrumb: breadcrumbTheme,
  Button: buttonTheme,
  Checkbox: checkboxTheme,
  CloseButton: closeButtonTheme,
  Code: codeTheme,
  Container: containerTheme,
  Divider: dividerTheme,
  Drawer: drawerTheme,
  Editable: editableTheme,
  Form: formTheme,
  FormError: formErrorTheme,
  FormLabel: formLabelTheme,
  Heading: headingTheme,
  Input: inputTheme,
  Kbd: kbdTheme,
  Link: linkTheme,
  List: listTheme,
  Menu: menuTheme,
  Modal: modalTheme,
  NumberInput: numberInputTheme,
  PinInput: pinInputTheme,
  Popover: popoverTheme,
  Progress: progressTheme,
  Radio: radioTheme,
  Select: selectTheme,
  Skeleton: skeletonTheme,
  SkipLink: skipLinkTheme,
  Slider: sliderTheme,
  Spinner: spinnerTheme,
  Stat: statTheme,
  Switch: switchTheme,
  Table: tableTheme,
  Tabs: tabsTheme,
  Tag: tagTheme,
  Textarea: textareaTheme,
  Tooltip: tooltipTheme,
  Card: cardTheme,
  Stepper: stepperTheme
};

export { accordionTheme as Accordion, alertTheme as Alert, avatarTheme as Avatar, badgeTheme as Badge, breadcrumbTheme as Breadcrumb, buttonTheme as Button, checkboxTheme as Checkbox, closeButtonTheme as CloseButton, codeTheme as Code, containerTheme as Container, dividerTheme as Divider, drawerTheme as Drawer, editableTheme as Editable, formTheme as Form, formErrorTheme as FormError, formLabelTheme as FormLabel, headingTheme as Heading, inputTheme as Input, kbdTheme as Kbd, linkTheme as Link, listTheme as List, menuTheme as Menu, modalTheme as Modal, numberInputTheme as NumberInput, pinInputTheme as PinInput, popoverTheme as Popover, progressTheme as Progress, radioTheme as Radio, selectTheme as Select, skeletonTheme as Skeleton, skipLinkTheme as SkipLink, sliderTheme as Slider, spinnerTheme as Spinner, statTheme as Stat, stepperTheme as Stepper, switchTheme as Switch, tableTheme as Table, tabsTheme as Tabs, tagTheme as Tag, textareaTheme as Textarea, tooltipTheme as Tooltip, components };
