{"name": "@zag-js/element-size", "version": "0.31.1", "description": "Observer the size of an element over time", "keywords": ["js", "utils", "element-size"], "author": "<PERSON><PERSON> <<EMAIL>>", "homepage": "https://github.com/chakra-ui/zag#readme", "license": "MIT", "repository": "https://github.com/chakra-ui/zag/tree/main/packages/utilities/element-size", "sideEffects": false, "files": ["dist", "src"], "publishConfig": {"access": "public"}, "bugs": {"url": "https://github.com/chakra-ui/zag/issues"}, "clean-package": "../../../clean-package.config.json", "main": "dist/index.js", "devDependencies": {"clean-package": "2.2.0"}, "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json"}, "scripts": {"build": "tsup", "lint": "eslint src --ext .ts,.tsx", "typecheck": "tsc --noEmit"}}