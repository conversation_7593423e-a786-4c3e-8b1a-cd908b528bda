import React, { useEffect, useState } from 'react';
import {
  Box,
  Text,
  Badge,
  Flex,
  Button,
  useToast,
  <PERSON>ltip,
  Spinner,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Collapse,
  useDisclosure,
  useColorModeValue,
} from '@chakra-ui/react';
import { CheckCircleIcon, WarningIcon, RepeatIcon, InfoIcon } from '@chakra-ui/icons';
import { checkApiHealth } from '../../services/api';

interface ApiHealthCheckProps {
  showDetails?: boolean;
  compact?: boolean;
}

const ApiHealthCheck: React.FC<ApiHealthCheckProps> = ({
  showDetails = false,
  compact = false
}) => {
  const [status, setStatus] = useState<{
    isAvailable: boolean;
    message?: string;
    lastChecked: Date | null;
    isChecking: boolean;
  }>({
    isAvailable: true,
    lastChecked: null,
    isChecking: true,
  });

  const { isOpen, onToggle } = useDisclosure({
    defaultIsOpen: showDetails,
  });

  const toast = useToast();

  const checkConnection = async () => {
    setStatus(prev => ({ ...prev, isChecking: true }));
    try {
      // Try multiple endpoints to ensure we're testing the connection thoroughly
      let result;

      // First try the health endpoint
      try {
        result = await checkApiHealth();
      } catch (healthError) {
        console.log('Health endpoint check failed, trying root API endpoint');

        // If health endpoint fails, try the root API endpoint
        try {
          const response = await fetch('http://localhost:3001/api', {
            method: 'GET',
            headers: { 'Accept': 'application/json' },
            signal: AbortSignal.timeout(3000)
          });

          if (response.ok) {
            result = { isAvailable: true };
          } else {
            result = {
              isAvailable: false,
              message: `API responded with status ${response.status}`
            };
          }
        } catch (rootError) {
          // Both checks failed
          result = {
            isAvailable: false,
            message: 'All connection attempts failed. Server may be down.'
          };
        }
      }

      setStatus({
        isAvailable: result.isAvailable,
        message: result.message,
        lastChecked: new Date(),
        isChecking: false,
      });

      if (result.isAvailable) {
        toast({
          title: 'Connected to API',
          description: 'The connection to the backend API is working properly.',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      } else {
        toast({
          title: 'Using Mock Data',
          description: result.message || 'Could not connect to the backend API. Using mock data instead.',
          status: 'warning',
          duration: 5000,
          isClosable: true,
        });
      }
    } catch (error) {
      setStatus({
        isAvailable: false,
        message: error instanceof Error ? error.message : 'An unknown error occurred',
        lastChecked: new Date(),
        isChecking: false,
      });

      toast({
        title: 'Using Mock Data',
        description: 'Connection to API failed. Using mock data instead.',
        status: 'warning',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  useEffect(() => {
    checkConnection();

    // Set up periodic health checks
    const intervalId = setInterval(() => {
      checkConnection();
    }, 60000); // Check every minute

    // Set up event listener for API health check events
    const handleApiHealthCheck = (event: CustomEvent) => {
      setStatus(prev => ({
        ...prev,
        isAvailable: event.detail.isAvailable,
        message: event.detail.message,
        lastChecked: new Date(),
        isChecking: false,
      }));
    };

    window.addEventListener('api-health-check', handleApiHealthCheck as EventListener);

    return () => {
      clearInterval(intervalId);
      window.removeEventListener('api-health-check', handleApiHealthCheck as EventListener);
    };
  }, []);

  // Format the last checked time
  const formatLastChecked = () => {
    return status.lastChecked ? status.lastChecked.toLocaleTimeString() : 'Never';
  };

  // If compact mode is enabled, show a simple badge
  if (compact) {
    return (
      <Tooltip
        label={status.isAvailable ? 'Connected to API' : 'API connection failed'}
        aria-label="API connection status"
      >
        <Badge
          colorScheme={status.isAvailable ? 'green' : 'red'}
          position="fixed"
          bottom="4"
          right="4"
          p="2"
          borderRadius="full"
          cursor="pointer"
          onClick={checkConnection}
        >
          {status.isChecking ? (
            <Spinner size="xs" />
          ) : status.isAvailable ? (
            <CheckCircleIcon />
          ) : (
            <WarningIcon />
          )}
        </Badge>
      </Tooltip>
    );
  }

  // If not showing details, use the simple version
  if (!showDetails) {
    return (
      <Box
        p={4}
        borderWidth="1px"
        borderRadius="lg"
        shadow="md"
        bg={useColorModeValue(
          status.isAvailable ? 'green.50' : 'red.50',
          status.isAvailable ? 'green.900' : 'red.900'
        )}
        borderColor={useColorModeValue(
          status.isAvailable ? 'green.200' : 'red.200',
          status.isAvailable ? 'green.700' : 'red.700'
        )}
        opacity={status.isAvailable ? 0.9 : 1}
      >
        <Flex justify="space-between" align="center">
          <Flex align="center">
            {status.isChecking ? (
              <Spinner size="sm" mr={2} />
            ) : status.isAvailable ? (
              <CheckCircleIcon color="green.500" mr={2} />
            ) : (
              <WarningIcon color="red.500" mr={2} />
            )}
            <Text fontWeight="medium">
              {status.isChecking
                ? 'Checking API connection...'
                : status.isAvailable
                ? 'Connected to API'
                : 'API connection failed'}
            </Text>
          </Flex>
          <Button
            leftIcon={<RepeatIcon />}
            size="sm"
            onClick={checkConnection}
            isLoading={status.isChecking}
            colorScheme={status.isAvailable ? 'green' : 'red'}
            variant="outline"
          >
            Check Again
          </Button>
        </Flex>
        {status.lastChecked && (
          <Text fontSize="xs" mt={2} color="gray.500">
            Last checked: {formatLastChecked()}
          </Text>
        )}
      </Box>
    );
  }

  // Full version with details
  return (
    <Box>
      <Alert
        status={status.isAvailable ? 'success' : 'error'}
        variant="subtle"
        borderRadius="md"
        mb={isOpen ? 2 : 0}
      >
        <AlertIcon />
        <Flex flex="1" justify="space-between" align="center">
          <Box>
            <AlertTitle>
              API Status: {status.isAvailable ? 'Connected' : 'Disconnected'}
            </AlertTitle>
            <AlertDescription display="block">
              {status.message || (status.isAvailable
                ? 'Successfully connected to the API server'
                : 'Connection to API server timed out')}
            </AlertDescription>
          </Box>
          <Flex align="center">
            <Text fontSize="sm" color="gray.500" mr={4}>
              Last checked: {formatLastChecked()}
            </Text>
            <Button
              size="sm"
              leftIcon={status.isChecking ? <Spinner size="xs" /> : <RepeatIcon />}
              onClick={checkConnection}
              isLoading={status.isChecking}
              variant="outline"
              colorScheme={status.isAvailable ? 'green' : 'red'}
              mr={2}
            >
              Check
            </Button>
            <Button
              size="sm"
              leftIcon={<InfoIcon />}
              onClick={onToggle}
              variant="ghost"
            >
              {isOpen ? 'Hide' : 'Details'}
            </Button>
          </Flex>
        </Flex>
      </Alert>

      <Collapse in={isOpen} animateOpacity>
        <Box
          p={4}
          bg={useColorModeValue('gray.50', 'gray.700')}
          borderRadius="md"
          fontSize="sm"
        >
          <Text fontWeight="bold" mb={2}>
            API Connection Details
          </Text>
          <Text mb={3}>
            The application connects to a backend API server that provides cryptocurrency data.
            {!status.isAvailable && ' Currently using mock data as a fallback.'}
          </Text>

          {status.isAvailable ? (
            <Alert status="success" variant="left-accent" mt={2} mb={2} fontSize="sm">
              <AlertIcon />
              <Box>
                <AlertTitle>Connected Successfully</AlertTitle>
                <AlertDescription>
                  The application is receiving live data from the API server.
                </AlertDescription>
              </Box>
            </Alert>
          ) : (
            <Alert status="info" variant="left-accent" mt={2} mb={2} fontSize="sm">
              <AlertIcon />
              <Box>
                <AlertTitle>Using Fallback Data</AlertTitle>
                <AlertDescription>
                  The application is currently using mock data. Some features may be limited.
                </AlertDescription>
              </Box>
            </Alert>
          )}

          {!status.isAvailable && (
            <Box mt={4}>
              <Text fontWeight="bold" mb={2}>
                Troubleshooting
              </Text>
              <Text>
                If you're experiencing connection issues:
              </Text>
              <Box as="ul" pl={5} mt={2}>
                <Box as="li" mb={1}>
                  Make sure the backend server is running at http://localhost:3001
                </Box>
                <Box as="li" mb={1}>
                  Check your network connection
                </Box>
                <Box as="li" mb={1}>
                  The API might be experiencing rate limiting from CoinGecko
                </Box>
                <Box as="li" mb={1}>
                  You can continue using the app with mock data in the meantime
                </Box>
              </Box>
            </Box>
          )}
        </Box>
      </Collapse>
    </Box>
  );
};

export default ApiHealthCheck;
