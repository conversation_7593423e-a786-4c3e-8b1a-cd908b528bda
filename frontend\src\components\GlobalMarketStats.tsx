import React, { useEffect, useState } from 'react';
import {
  Box,
  Grid,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Card,
  CardBody,
  useColorModeValue,
  Spinner,
  Text,
  VStack,
} from '@chakra-ui/react';
import { coinGeckoApi, CoinGeckoGlobalData } from '../services/coinGeckoApi';

interface GlobalStats {
  totalMarketCap: string;
  totalVolume: string;
  marketCapChange: number;
  btcDominance: number;
  ethDominance: number;
  activeCryptocurrencies: number;
}

const GlobalMarketStats: React.FC = () => {
  const [stats, setStats] = useState<GlobalStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const cardBg = useColorModeValue('white', 'gray.800');

  const formatCurrency = (value: number): string => {
    if (value >= 1e12) return `$${(value / 1e12).toFixed(2)}T`;
    if (value >= 1e9) return `$${(value / 1e9).toFixed(2)}B`;
    if (value >= 1e6) return `$${(value / 1e6).toFixed(2)}M`;
    return `$${value.toLocaleString()}`;
  };

  useEffect(() => {
    const fetchGlobalStats = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const globalData: CoinGeckoGlobalData = await coinGeckoApi.getGlobalData();
        
        const stats: GlobalStats = {
          totalMarketCap: formatCurrency(globalData.data.total_market_cap.usd),
          totalVolume: formatCurrency(globalData.data.total_volume.usd),
          marketCapChange: globalData.data.market_cap_change_percentage_24h_usd,
          btcDominance: globalData.data.market_cap_percentage.btc,
          ethDominance: globalData.data.market_cap_percentage.eth,
          activeCryptocurrencies: globalData.data.active_cryptocurrencies,
        };
        
        setStats(stats);
        console.log('✅ Global market stats loaded successfully');
      } catch (err) {
        console.error('❌ Failed to fetch global stats:', err);
        setError('Failed to load global market data');
        
        // Fallback mock data
        setStats({
          totalMarketCap: '$1.65T',
          totalVolume: '$85.2B',
          marketCapChange: 2.34,
          btcDominance: 52.1,
          ethDominance: 17.8,
          activeCryptocurrencies: 10847,
        });
      } finally {
        setLoading(false);
      }
    };

    fetchGlobalStats();
    
    // Refresh every 5 minutes
    const interval = setInterval(fetchGlobalStats, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <Card bg={cardBg}>
        <CardBody>
          <VStack spacing={4} py={4}>
            <Spinner size="lg" color="blue.500" />
            <Text>Loading global market data...</Text>
          </VStack>
        </CardBody>
      </Card>
    );
  }

  if (error || !stats) {
    return (
      <Card bg={cardBg}>
        <CardBody>
          <Text color="red.500" textAlign="center">
            {error || 'Failed to load global market data'}
          </Text>
        </CardBody>
      </Card>
    );
  }

  return (
    <Grid templateColumns={{ base: '1fr', md: 'repeat(2, 1fr)', lg: 'repeat(3, 1fr)' }} gap={6}>
      <Card bg={cardBg}>
        <CardBody>
          <Stat>
            <StatLabel>Total Market Cap</StatLabel>
            <StatNumber fontSize="2xl">{stats.totalMarketCap}</StatNumber>
            <StatHelpText>
              <StatArrow type={stats.marketCapChange >= 0 ? 'increase' : 'decrease'} />
              {Math.abs(stats.marketCapChange).toFixed(2)}% (24h)
            </StatHelpText>
          </Stat>
        </CardBody>
      </Card>

      <Card bg={cardBg}>
        <CardBody>
          <Stat>
            <StatLabel>24h Trading Volume</StatLabel>
            <StatNumber fontSize="2xl">{stats.totalVolume}</StatNumber>
            <StatHelpText>
              Global trading activity
            </StatHelpText>
          </Stat>
        </CardBody>
      </Card>

      <Card bg={cardBg}>
        <CardBody>
          <Stat>
            <StatLabel>Active Cryptocurrencies</StatLabel>
            <StatNumber fontSize="2xl">{stats.activeCryptocurrencies.toLocaleString()}</StatNumber>
            <StatHelpText>
              Total coins tracked
            </StatHelpText>
          </Stat>
        </CardBody>
      </Card>

      <Card bg={cardBg}>
        <CardBody>
          <Stat>
            <StatLabel>Bitcoin Dominance</StatLabel>
            <StatNumber fontSize="2xl" color="orange.500">{stats.btcDominance.toFixed(1)}%</StatNumber>
            <StatHelpText>
              BTC market share
            </StatHelpText>
          </Stat>
        </CardBody>
      </Card>

      <Card bg={cardBg}>
        <CardBody>
          <Stat>
            <StatLabel>Ethereum Dominance</StatLabel>
            <StatNumber fontSize="2xl" color="blue.500">{stats.ethDominance.toFixed(1)}%</StatNumber>
            <StatHelpText>
              ETH market share
            </StatHelpText>
          </Stat>
        </CardBody>
      </Card>

      <Card bg={cardBg}>
        <CardBody>
          <Stat>
            <StatLabel>Market Status</StatLabel>
            <StatNumber fontSize="xl" color="green.500">🟢 Live</StatNumber>
            <StatHelpText>
              Real-time data
            </StatHelpText>
          </Stat>
        </CardBody>
      </Card>
    </Grid>
  );
};

export default GlobalMarketStats;
