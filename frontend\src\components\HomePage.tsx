import React from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  Button,
  VStack,
  HStack,
  Grid,
  GridItem,
  Card,
  CardBody,
  Badge,
  Icon,
  useColorModeValue,
  Flex,
  Spacer,
  Spinner,
  Image,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
} from '@chakra-ui/react';
import { FaBitcoin, FaEthereum, FaChartLine, FaWallet, FaEye, FaRobot, FaSync } from 'react-icons/fa';
import { useLiveCryptoData } from '../hooks/useLiveCryptoData';

const HomePage: React.FC = () => {
  const bgColor = useColorModeValue('gray.50', 'gray.900');
  const cardBg = useColorModeValue('white', 'gray.800');

  // Use live crypto data hook
  const { data: cryptoData, loading, error, refetch, lastUpdated } = useLiveCryptoData(['bitcoin', 'ethereum', 'cardano', 'solana'], 4);

  const features = [
    {
      icon: FaChartLine,
      title: 'Real-time Market Data',
      description: 'Track live cryptocurrency prices and market trends',
      color: 'blue.500',
    },
    {
      icon: FaWallet,
      title: 'Portfolio Management',
      description: 'Manage your crypto investments and track performance',
      color: 'green.500',
    },
    {
      icon: FaEye,
      title: 'Watchlists',
      description: 'Create custom watchlists and set price alerts',
      color: 'purple.500',
    },
    {
      icon: FaRobot,
      title: 'AI Insights',
      description: 'Get AI-powered market analysis and predictions',
      color: 'orange.500',
    },
  ];



  return (
    <Box bg={bgColor} minH="100vh">
      <Container maxW="7xl" py={8}>
        {/* Hero Section */}
        <VStack spacing={8} textAlign="center" mb={12}>
          <Heading
            size="2xl"
            bgGradient="linear(to-r, blue.400, purple.500)"
            bgClip="text"
            fontWeight="extrabold"
          >
            🚀 Crypto Tracker
          </Heading>
          <Text fontSize="xl" color="gray.600" maxW="2xl">
            Your comprehensive cryptocurrency tracking platform with real-time data,
            portfolio management, and AI-powered insights.
          </Text>
          <HStack spacing={4}>
            <Button colorScheme="blue" size="lg">
              Get Started
            </Button>
            <Button variant="outline" size="lg" onClick={() => window.location.reload()}>
              View Full Market
            </Button>
          </HStack>
        </VStack>

        {/* Features Grid */}
        <Grid templateColumns={{ base: '1fr', md: 'repeat(2, 1fr)', lg: 'repeat(4, 1fr)' }} gap={6} mb={12}>
          {features.map((feature, index) => (
            <GridItem key={index}>
              <Card bg={cardBg} h="full" _hover={{ transform: 'translateY(-2px)', shadow: 'lg' }} transition="all 0.2s">
                <CardBody textAlign="center">
                  <VStack spacing={4}>
                    <Icon as={feature.icon} boxSize={8} color={feature.color} />
                    <Heading size="md">{feature.title}</Heading>
                    <Text color="gray.600" fontSize="sm">
                      {feature.description}
                    </Text>
                  </VStack>
                </CardBody>
              </Card>
            </GridItem>
          ))}
        </Grid>

        {/* Top Cryptocurrencies */}
        <Box>
          <Flex align="center" justify="space-between" mb={6}>
            <Heading size="lg">
              Live Cryptocurrency Prices
            </Heading>
            <HStack spacing={2}>
              {lastUpdated && (
                <Text fontSize="sm" color="gray.500">
                  Updated: {lastUpdated.toLocaleTimeString()}
                </Text>
              )}
              <Button
                size="sm"
                variant="outline"
                leftIcon={<FaSync />}
                onClick={refetch}
                isLoading={loading}
                loadingText="Updating"
              >
                Refresh
              </Button>
            </HStack>
          </Flex>

          {error && (
            <Alert status="warning" mb={4} borderRadius="md">
              <AlertIcon />
              <Box>
                <AlertTitle>API Connection Issue</AlertTitle>
                <AlertDescription>
                  Using cached data. {error}
                </AlertDescription>
              </Box>
            </Alert>
          )}

          {loading && cryptoData.length === 0 ? (
            <Flex justify="center" py={8}>
              <VStack spacing={4}>
                <Spinner size="lg" color="blue.500" />
                <Text>Loading live cryptocurrency data...</Text>
              </VStack>
            </Flex>
          ) : (
            <Grid templateColumns={{ base: '1fr', md: 'repeat(2, 1fr)' }} gap={4}>
              {cryptoData.map((crypto, index) => (
                <Card key={crypto.id || index} bg={cardBg} _hover={{ transform: 'translateY(-2px)', shadow: 'lg' }} transition="all 0.2s">
                  <CardBody>
                    <Flex align="center">
                      <HStack spacing={3}>
                        {crypto.image ? (
                          <Image
                            src={crypto.image}
                            alt={crypto.name}
                            boxSize={8}
                            borderRadius="full"
                          />
                        ) : (
                          <Icon
                            as={crypto.symbol === 'BTC' ? FaBitcoin : FaEthereum}
                            boxSize={6}
                            color={crypto.symbol === 'BTC' ? 'orange.500' : 'blue.500'}
                          />
                        )}
                        <VStack align="start" spacing={0}>
                          <Text fontWeight="bold">{crypto.name}</Text>
                          <HStack spacing={2}>
                            <Text fontSize="sm" color="gray.500">{crypto.symbol}</Text>
                            {crypto.rank && (
                              <Badge size="sm" colorScheme="gray">
                                #{crypto.rank}
                              </Badge>
                            )}
                          </HStack>
                        </VStack>
                      </HStack>
                      <Spacer />
                      <VStack align="end" spacing={0}>
                        <Text fontWeight="bold" fontSize="lg">{crypto.price}</Text>
                        <Badge colorScheme={crypto.positive ? 'green' : 'red'} fontSize="sm">
                          {crypto.change}
                        </Badge>
                      </VStack>
                    </Flex>
                  </CardBody>
                </Card>
              ))}
            </Grid>
          )}
        </Box>

        {/* Status Section */}
        <Box mt={12} textAlign="center">
          <Card bg="green.50" borderColor="green.200" borderWidth={1}>
            <CardBody>
              <VStack spacing={2}>
                <Text fontSize="lg" fontWeight="bold" color="green.800">
                  ✅ Application Status: Fully Operational
                </Text>
                <Text color="green.700">
                  All systems are running smoothly. Ready to track your crypto portfolio!
                </Text>
              </VStack>
            </CardBody>
          </Card>
        </Box>
      </Container>
    </Box>
  );
};

export default HomePage;
