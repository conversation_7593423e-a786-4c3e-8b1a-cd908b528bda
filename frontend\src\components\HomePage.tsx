import React, { useState, useEffect } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import {
  Box,
  Container,
  Heading,
  Text,
  Button,
  VStack,
  HStack,
  Grid,
  GridItem,
  Card,
  CardBody,
  Badge,
  Icon,
  useColorModeValue,
  Flex,
  Spacer,
  Spinner,
  Image,
  SimpleGrid,
  Avatar,
  Input,
  InputGroup,
  InputRightElement,
  IconButton,
  Divider,
  Stack,
  Center,
  keyframes,
  useBreakpointValue,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Tooltip,
  useToast,
} from '@chakra-ui/react';
import {
  FiShield,
  FiZap,
  FiGlobe,
  FiTrendingUp,
  FiStar,
  FiArrowRight,
  FiMail,
  FiCheck,
  FiLock,
  FiActivity,
  FiPlay
} from 'react-icons/fi';
import {
  SiEthereum,
  SiBitcoin,
  SiLitecoin,
  SiCardano,
  SiPolygon,
  SiSolana
} from 'react-icons/si';
import { useLiveCryptoData } from '../hooks/useLiveCryptoData';
import { formatCurrency, formatPercentage } from '../utils/format';

// Animations
const float = keyframes`
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
`;

const glow = keyframes`
  0% { box-shadow: 0 0 20px rgba(0, 255, 255, 0.3); }
  50% { box-shadow: 0 0 30px rgba(0, 255, 255, 0.6); }
  100% { box-shadow: 0 0 20px rgba(0, 255, 255, 0.3); }
`;

const slideIn = keyframes`
  from { transform: translateX(-100px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
`;

const HomePage: React.FC = () => {
  const [email, setEmail] = useState('');
  const [isSubscribed, setIsSubscribed] = useState(false);
  const toast = useToast();

  // Use live crypto data hook for ticker
  const { data: cryptoData, loading, error, refetch, lastUpdated } = useLiveCryptoData(['bitcoin', 'ethereum', 'cardano', 'solana', 'binancecoin', 'ripple', 'polkadot', 'chainlink'], 8);

  // Newsletter subscription handler
  const handleSubscribe = () => {
    if (email) {
      setIsSubscribed(true);
      toast({
        title: "Successfully subscribed!",
        description: "You'll receive the latest crypto updates.",
        status: "success",
        duration: 3000,
        isClosable: true,
      });
      setEmail('');
    }
  };

  const features = [
    {
      icon: FiLock,
      title: 'Decentralized Security',
      description: 'Your assets are protected by cutting-edge blockchain technology and multi-layer security protocols.',
      color: 'cyan',
      gradient: 'linear(to-r, cyan.400, blue.500)'
    },
    {
      icon: FiZap,
      title: 'Lightning Fast',
      description: 'Execute transactions in milliseconds with our optimized network infrastructure.',
      color: 'yellow',
      gradient: 'linear(to-r, yellow.400, orange.500)'
    },
    {
      icon: FiGlobe,
      title: 'Global Access',
      description: 'Trade cryptocurrencies 24/7 from anywhere in the world with instant settlement.',
      color: 'green',
      gradient: 'linear(to-r, green.400, teal.500)'
    },
    {
      icon: FiActivity,
      title: 'AI-Powered Insights',
      description: 'Get intelligent market analysis and predictions powered by advanced machine learning.',
      color: 'purple',
      gradient: 'linear(to-r, purple.400, pink.500)'
    }
  ];

  const supportedCoins = [
    { icon: SiBitcoin, name: 'Bitcoin', symbol: 'BTC', color: '#F7931A' },
    { icon: SiEthereum, name: 'Ethereum', symbol: 'ETH', color: '#627EEA' },
    { icon: SiCardano, name: 'Cardano', symbol: 'ADA', color: '#0033AD' },
    { icon: SiSolana, name: 'Solana', symbol: 'SOL', color: '#9945FF' },
    { icon: SiPolygon, name: 'Polygon', symbol: 'MATIC', color: '#8247E5' },
    { icon: SiLitecoin, name: 'Litecoin', symbol: 'LTC', color: '#BFBBBB' }
  ];

  const testimonials = [
    {
      name: 'Sarah Chen',
      role: 'Crypto Trader',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
      content: 'The AI insights have completely transformed my trading strategy. I\'ve seen a 40% improvement in my portfolio performance.',
      rating: 5
    },
    {
      name: 'Michael Rodriguez',
      role: 'DeFi Enthusiast',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
      content: 'Lightning-fast transactions and the most intuitive interface I\'ve ever used. This platform is the future of crypto.',
      rating: 5
    },
    {
      name: 'Emily Johnson',
      role: 'Investment Analyst',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
      content: 'The security features give me complete peace of mind. I can focus on trading without worrying about my assets.',
      rating: 5
    }
  ];



  return (
    <Box
      bg="linear-gradient(135deg, #0F0F23 0%, #1A1A2E 50%, #16213E 100%)"
      minH="100vh"
      position="relative"
      overflow="hidden"
    >
      {/* Background Effects */}
      <Box
        position="absolute"
        top="0"
        left="0"
        right="0"
        bottom="0"
        opacity="0.1"
        bgImage="radial-gradient(circle at 25% 25%, cyan 0%, transparent 50%), radial-gradient(circle at 75% 75%, purple 0%, transparent 50%)"
      />

      {/* Real-time Price Ticker */}
      <Box
        bg="rgba(0, 0, 0, 0.3)"
        backdropFilter="blur(10px)"
        borderBottom="1px solid rgba(255, 255, 255, 0.1)"
        py={2}
        overflow="hidden"
        position="relative"
      >
        <Box
          display="flex"
          animation={`${slideIn} 20s linear infinite`}
          whiteSpace="nowrap"
        >
          {cryptoData && cryptoData.map((coin, index) => (
            <Flex key={index} align="center" mx={8} minW="200px">
              <Text color="white" fontWeight="bold" mr={2}>
                {coin.symbol.toUpperCase()}
              </Text>
              <Text color="white" mr={2}>
                {formatCurrency(coin.current_price)}
              </Text>
              <Text
                color={coin.price_change_percentage_24h >= 0 ? 'green.400' : 'red.400'}
                fontSize="sm"
              >
                {formatPercentage(coin.price_change_percentage_24h)}
              </Text>
            </Flex>
          ))}
        </Box>
      </Box>

      <Container maxW="7xl" py={8} position="relative" zIndex={1}>
        {/* Hero Section */}
        <VStack spacing={12} textAlign="center" mb={20}>
          <VStack spacing={6}>
            <Heading
              size="4xl"
              bgGradient="linear(to-r, cyan.400, blue.400, purple.500)"
              bgClip="text"
              fontWeight="extrabold"
              animation={`${float} 3s ease-in-out infinite`}
              textShadow="0 0 30px rgba(0, 255, 255, 0.3)"
            >
              The Future of Crypto
            </Heading>
            <Text
              fontSize="2xl"
              color="gray.300"
              maxW="4xl"
              lineHeight="1.6"
            >
              Experience the next generation of cryptocurrency trading with
              <Text as="span" color="cyan.400" fontWeight="bold"> AI-powered insights</Text>,
              <Text as="span" color="purple.400" fontWeight="bold"> lightning-fast transactions</Text>, and
              <Text as="span" color="green.400" fontWeight="bold"> military-grade security</Text>.
            </Text>
          </VStack>

          <HStack spacing={6} flexWrap="wrap" justify="center">
            <Button
              as={RouterLink}
              to="/market"
              size="lg"
              bg="linear-gradient(135deg, cyan.400, blue.500)"
              color="white"
              _hover={{
                transform: 'translateY(-2px)',
                boxShadow: '0 10px 25px rgba(0, 255, 255, 0.3)',
              }}
              _active={{ transform: 'translateY(0)' }}
              transition="all 0.3s ease"
              leftIcon={<FiTrendingUp />}
              animation={`${glow} 2s ease-in-out infinite`}
            >
              Explore Markets
            </Button>
            <Button
              size="lg"
              variant="outline"
              borderColor="purple.400"
              color="purple.400"
              _hover={{
                bg: 'purple.400',
                color: 'white',
                transform: 'translateY(-2px)',
              }}
              transition="all 0.3s ease"
              leftIcon={<FiPlay />}
            >
              Watch Demo
            </Button>
          </HStack>

          {/* Market Stats */}
          <SimpleGrid columns={{ base: 2, md: 4 }} spacing={8} w="full" maxW="4xl">
            <Stat textAlign="center">
              <StatLabel color="gray.400">Total Market Cap</StatLabel>
              <StatNumber color="white" fontSize="2xl">$2.1T</StatNumber>
              <StatHelpText color="green.400">
                <StatArrow type="increase" />
                5.2%
              </StatHelpText>
            </Stat>
            <Stat textAlign="center">
              <StatLabel color="gray.400">24h Volume</StatLabel>
              <StatNumber color="white" fontSize="2xl">$89.2B</StatNumber>
              <StatHelpText color="green.400">
                <StatArrow type="increase" />
                12.1%
              </StatHelpText>
            </Stat>
            <Stat textAlign="center">
              <StatLabel color="gray.400">Active Users</StatLabel>
              <StatNumber color="white" fontSize="2xl">2.5M+</StatNumber>
              <StatHelpText color="cyan.400">Growing daily</StatHelpText>
            </Stat>
            <Stat textAlign="center">
              <StatLabel color="gray.400">Supported Coins</StatLabel>
              <StatNumber color="white" fontSize="2xl">500+</StatNumber>
              <StatHelpText color="purple.400">And counting</StatHelpText>
            </Stat>
          </SimpleGrid>
        </VStack>

        {/* Features Section */}
        <VStack spacing={16} mb={20}>
          <VStack spacing={4} textAlign="center">
            <Heading
              size="2xl"
              color="white"
              bgGradient="linear(to-r, cyan.400, purple.500)"
              bgClip="text"
            >
              Why Choose Our Platform?
            </Heading>
            <Text fontSize="lg" color="gray.400" maxW="2xl">
              Built for the future of finance with cutting-edge technology and user-centric design
            </Text>
          </VStack>

          <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={8} w="full">
            {features.map((feature, index) => (
              <Box
                key={index}
                bg="rgba(255, 255, 255, 0.05)"
                backdropFilter="blur(10px)"
                border="1px solid rgba(255, 255, 255, 0.1)"
                borderRadius="xl"
                p={8}
                textAlign="center"
                _hover={{
                  transform: 'translateY(-10px)',
                  bg: 'rgba(255, 255, 255, 0.08)',
                  borderColor: feature.color + '.400',
                  boxShadow: `0 20px 40px rgba(0, 0, 0, 0.3)`,
                }}
                transition="all 0.4s ease"
                position="relative"
                overflow="hidden"
              >
                {/* Gradient overlay */}
                <Box
                  position="absolute"
                  top="0"
                  left="0"
                  right="0"
                  bottom="0"
                  bgGradient={feature.gradient}
                  opacity="0"
                  _groupHover={{ opacity: 0.1 }}
                  transition="opacity 0.3s ease"
                />

                <VStack spacing={6} position="relative" zIndex={1}>
                  <Box
                    p={4}
                    borderRadius="full"
                    bg={`${feature.color}.400`}
                    color="white"
                    animation={`${float} 3s ease-in-out infinite`}
                    style={{ animationDelay: `${index * 0.5}s` }}
                  >
                    <Icon as={feature.icon} boxSize={8} />
                  </Box>
                  <Heading size="md" color="white">
                    {feature.title}
                  </Heading>
                  <Text color="gray.300" fontSize="sm" lineHeight="1.6">
                    {feature.description}
                  </Text>
                </VStack>
              </Box>
            ))}
          </SimpleGrid>
        </VStack>

        {/* Supported Coins Section */}
        <VStack spacing={16} mb={20}>
          <VStack spacing={4} textAlign="center">
            <Heading
              size="2xl"
              color="white"
              bgGradient="linear(to-r, green.400, blue.500)"
              bgClip="text"
            >
              Supported Cryptocurrencies
            </Heading>
            <Text fontSize="lg" color="gray.400" maxW="2xl">
              Trade the most popular cryptocurrencies with real-time data and instant execution
            </Text>
          </VStack>

          <SimpleGrid columns={{ base: 2, md: 3, lg: 6 }} spacing={8} w="full">
            {supportedCoins.map((coin, index) => (
              <VStack
                key={index}
                spacing={4}
                p={6}
                bg="rgba(255, 255, 255, 0.05)"
                backdropFilter="blur(10px)"
                border="1px solid rgba(255, 255, 255, 0.1)"
                borderRadius="xl"
                _hover={{
                  transform: 'translateY(-5px)',
                  borderColor: coin.color,
                  boxShadow: `0 10px 30px ${coin.color}40`,
                }}
                transition="all 0.3s ease"
                cursor="pointer"
              >
                <Icon
                  as={coin.icon}
                  boxSize={12}
                  color={coin.color}
                  animation={`${float} 3s ease-in-out infinite`}
                  style={{ animationDelay: `${index * 0.2}s` }}
                />
                <VStack spacing={1}>
                  <Text color="white" fontWeight="bold" fontSize="lg">
                    {coin.symbol}
                  </Text>
                  <Text color="gray.400" fontSize="sm">
                    {coin.name}
                  </Text>
                </VStack>
              </VStack>
            ))}
          </SimpleGrid>
        </VStack>

        {/* Testimonials Section */}
        <VStack spacing={16} mb={20}>
          <VStack spacing={4} textAlign="center">
            <Heading
              size="2xl"
              color="white"
              bgGradient="linear(to-r, purple.400, pink.500)"
              bgClip="text"
            >
              What Our Users Say
            </Heading>
            <Text fontSize="lg" color="gray.400" maxW="2xl">
              Join thousands of satisfied traders who trust our platform
            </Text>
          </VStack>

          <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8} w="full">
            {testimonials.map((testimonial, index) => (
              <Box
                key={index}
                bg="rgba(255, 255, 255, 0.05)"
                backdropFilter="blur(10px)"
                border="1px solid rgba(255, 255, 255, 0.1)"
                borderRadius="xl"
                p={8}
                _hover={{
                  transform: 'translateY(-5px)',
                  bg: 'rgba(255, 255, 255, 0.08)',
                }}
                transition="all 0.3s ease"
              >
                <VStack spacing={6} align="start">
                  <HStack spacing={1}>
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Icon key={i} as={FiStar} color="yellow.400" />
                    ))}
                  </HStack>
                  <Text color="gray.300" fontSize="md" lineHeight="1.6">
                    "{testimonial.content}"
                  </Text>
                  <HStack spacing={4}>
                    <Avatar
                      src={testimonial.avatar}
                      name={testimonial.name}
                      size="md"
                    />
                    <VStack spacing={0} align="start">
                      <Text color="white" fontWeight="bold">
                        {testimonial.name}
                      </Text>
                      <Text color="gray.400" fontSize="sm">
                        {testimonial.role}
                      </Text>
                    </VStack>
                  </HStack>
                </VStack>
              </Box>
            ))}
          </SimpleGrid>
        </VStack>

        {/* Newsletter Section */}
        <Box
          bg="rgba(255, 255, 255, 0.05)"
          backdropFilter="blur(10px)"
          border="1px solid rgba(255, 255, 255, 0.1)"
          borderRadius="2xl"
          p={12}
          textAlign="center"
          mb={20}
        >
          <VStack spacing={8}>
            <VStack spacing={4}>
              <Heading
                size="xl"
                color="white"
                bgGradient="linear(to-r, cyan.400, purple.500)"
                bgClip="text"
              >
                Stay Ahead of the Market
              </Heading>
              <Text fontSize="lg" color="gray.400" maxW="2xl">
                Get the latest crypto news, market analysis, and exclusive insights delivered to your inbox
              </Text>
            </VStack>

            {!isSubscribed ? (
              <HStack spacing={4} maxW="md" w="full">
                <InputGroup size="lg">
                  <Input
                    placeholder="Enter your email address"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    bg="rgba(255, 255, 255, 0.1)"
                    border="1px solid rgba(255, 255, 255, 0.2)"
                    color="white"
                    _placeholder={{ color: 'gray.400' }}
                    _focus={{
                      borderColor: 'cyan.400',
                      boxShadow: '0 0 0 1px cyan.400',
                    }}
                  />
                  <InputRightElement>
                    <IconButton
                      aria-label="Subscribe"
                      icon={<FiMail />}
                      onClick={handleSubscribe}
                      bg="cyan.400"
                      color="white"
                      _hover={{ bg: 'cyan.500' }}
                      size="sm"
                    />
                  </InputRightElement>
                </InputGroup>
              </HStack>
            ) : (
              <VStack spacing={4}>
                <Icon as={FiCheck} boxSize={12} color="green.400" />
                <Text color="green.400" fontSize="lg" fontWeight="bold">
                  Thank you for subscribing!
                </Text>
              </VStack>
            )}
          </VStack>
        </Box>

        {/* CTA Section */}
        <VStack spacing={8} textAlign="center" mb={12}>
          <Heading
            size="2xl"
            color="white"
            bgGradient="linear(to-r, cyan.400, blue.400, purple.500)"
            bgClip="text"
          >
            Ready to Start Trading?
          </Heading>
          <Text fontSize="lg" color="gray.400" maxW="2xl">
            Join millions of users who trust our platform for their cryptocurrency needs
          </Text>
          <HStack spacing={6}>
            <Button
              as={RouterLink}
              to="/register"
              size="lg"
              bg="linear-gradient(135deg, cyan.400, blue.500)"
              color="white"
              _hover={{
                transform: 'translateY(-2px)',
                boxShadow: '0 10px 25px rgba(0, 255, 255, 0.3)',
              }}
              transition="all 0.3s ease"
              rightIcon={<FiArrowRight />}
            >
              Get Started Free
            </Button>
            <Button
              as={RouterLink}
              to="/market"
              size="lg"
              variant="outline"
              borderColor="purple.400"
              color="purple.400"
              _hover={{
                bg: 'purple.400',
                color: 'white',
                transform: 'translateY(-2px)',
              }}
              transition="all 0.3s ease"
            >
              Explore Markets
            </Button>
          </HStack>
        </VStack>
      </Container>
    </Box>
  );
};

export default HomePage;
