@echo off
echo ===================================
echo  Crypto Tracker Frontend Only
echo ===================================
echo.

REM Check if Node.js is installed
where node >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH. Please install Node.js.
    pause
    exit /b 1
)

echo Node.js is installed: 
node --version
echo.

REM Check if npm is installed
where npm >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo ERROR: npm is not installed or not in PATH. Please install npm.
    pause
    exit /b 1
)

echo npm is installed: 
npm --version
echo.

REM Kill any existing Node.js processes
echo Stopping any existing Node.js processes...
taskkill /f /im node.exe >nul 2>nul
echo.

REM Install frontend dependencies
echo Installing frontend dependencies...
cd frontend
call npm install
if %ERRORLEVEL% neq 0 (
    echo WARNING: Error installing frontend dependencies. Will try to continue anyway.
) else (
    echo Frontend dependencies installed successfully.
)
echo.

REM Start the frontend server
echo Starting frontend server...
echo NOTE: The frontend will use mock data since we're not running the backend.
echo.
start cmd /k "cd frontend && npm run dev"

REM Wait for the frontend to start
echo Waiting for frontend to initialize...
timeout /t 8 /nobreak >nul

REM Check if frontend is running
echo Checking if frontend is running...
curl -s http://localhost:3002 >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo WARNING: Frontend may not be running properly.
    echo Trying to access the frontend directly...
    curl -v http://localhost:3002
) else (
    echo Frontend is running successfully.
)
echo.

echo.
echo ===================================
echo  Crypto Tracker Frontend
echo ===================================
echo.
echo Frontend: http://localhost:3002
echo.
echo NOTE: The frontend will use mock data since the backend is not running.
echo.
echo Press any key to stop the frontend server...
pause >nul

echo Stopping frontend server...
taskkill /f /im node.exe >nul 2>nul
echo Frontend server stopped.
pause
