{"version": 3, "sources": ["../src/index.ts", "../src/track-size.ts", "../src/track-sizes.ts"], "sourcesContent": ["export { trackElementSize } from \"./track-size\"\nexport { trackElementsSize } from \"./track-sizes\"\nexport type { ElementSize, ElementSizeCallback } from \"./track-size\"\n", "export interface ElementSize {\n  width: number\n  height: number\n}\n\nexport type ElementSizeCallback = (size: ElementSize | undefined) => void\n\nexport function trackElementSize(element: HTMLElement | null, callback: ElementSizeCallback) {\n  if (!element) {\n    callback(undefined)\n    return\n  }\n\n  callback({ width: element.offsetWidth, height: element.offsetHeight })\n\n  const win = element.ownerDocument.defaultView ?? window\n\n  const observer = new win.ResizeObserver((entries) => {\n    if (!Array.isArray(entries) || !entries.length) return\n\n    const [entry] = entries\n    let width: number\n    let height: number\n\n    if (\"borderBoxSize\" in entry) {\n      const borderSizeEntry = entry[\"borderBoxSize\"]\n      const borderSize = Array.isArray(borderSizeEntry) ? borderSizeEntry[0] : borderSizeEntry\n      width = borderSize[\"inlineSize\"]\n      height = borderSize[\"blockSize\"]\n    } else {\n      width = element.offsetWidth\n      height = element.offsetHeight\n    }\n\n    callback({ width, height })\n  })\n\n  observer.observe(element, { box: \"border-box\" })\n\n  return () => observer.unobserve(element)\n}\n", "import { trackElementSize, type ElementSize } from \"./track-size\"\n\nexport interface TrackElementsSizeOptions<T extends HTMLElement | null> {\n  getNodes: () => T[]\n  observeMutation?: boolean\n  callback: (size: ElementSize | undefined, index: number) => void\n}\n\nexport function trackElementsSize<T extends HTMLElement | null>(options: TrackElementsSizeOptions<T>) {\n  const { getNodes, observeMutation = true, callback } = options\n\n  const cleanups: Array<VoidFunction | undefined> = []\n\n  let firstNode: T | null = null\n\n  function trigger() {\n    const elements = getNodes()\n    firstNode = elements[0]\n    const fns = elements.map((element, index) =>\n      trackElementSize(element, (size) => {\n        callback(size, index)\n      }),\n    )\n    cleanups.push(...fns)\n  }\n\n  trigger()\n\n  if (observeMutation) {\n    const fn = trackMutation(firstNode, trigger)\n    cleanups.push(fn)\n  }\n\n  return () => {\n    cleanups.forEach((cleanup) => {\n      cleanup?.()\n    })\n  }\n}\n\nfunction trackMutation(el: HTMLElement | null, cb: () => void) {\n  if (!el || !el.parentElement) return\n  const win = el.ownerDocument?.defaultView ?? window\n  const observer = new win.MutationObserver(() => {\n    cb()\n  })\n  observer.observe(el.parentElement, { childList: true })\n  return () => {\n    observer.disconnect()\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACOO,SAAS,iBAAiB,SAA6B,UAA+B;AAC3F,MAAI,CAAC,SAAS;AACZ,aAAS,MAAS;AAClB;AAAA,EACF;AAEA,WAAS,EAAE,OAAO,QAAQ,aAAa,QAAQ,QAAQ,aAAa,CAAC;AAErE,QAAM,MAAM,QAAQ,cAAc,eAAe;AAEjD,QAAM,WAAW,IAAI,IAAI,eAAe,CAAC,YAAY;AACnD,QAAI,CAAC,MAAM,QAAQ,OAAO,KAAK,CAAC,QAAQ;AAAQ;AAEhD,UAAM,CAAC,KAAK,IAAI;AAChB,QAAI;AACJ,QAAI;AAEJ,QAAI,mBAAmB,OAAO;AAC5B,YAAM,kBAAkB,MAAM,eAAe;AAC7C,YAAM,aAAa,MAAM,QAAQ,eAAe,IAAI,gBAAgB,CAAC,IAAI;AACzE,cAAQ,WAAW,YAAY;AAC/B,eAAS,WAAW,WAAW;AAAA,IACjC,OAAO;AACL,cAAQ,QAAQ;AAChB,eAAS,QAAQ;AAAA,IACnB;AAEA,aAAS,EAAE,OAAO,OAAO,CAAC;AAAA,EAC5B,CAAC;AAED,WAAS,QAAQ,SAAS,EAAE,KAAK,aAAa,CAAC;AAE/C,SAAO,MAAM,SAAS,UAAU,OAAO;AACzC;;;AChCO,SAAS,kBAAgD,SAAsC;AACpG,QAAM,EAAE,UAAU,kBAAkB,MAAM,SAAS,IAAI;AAEvD,QAAM,WAA4C,CAAC;AAEnD,MAAI,YAAsB;AAE1B,WAAS,UAAU;AACjB,UAAM,WAAW,SAAS;AAC1B,gBAAY,SAAS,CAAC;AACtB,UAAM,MAAM,SAAS;AAAA,MAAI,CAAC,SAAS,UACjC,iBAAiB,SAAS,CAAC,SAAS;AAClC,iBAAS,MAAM,KAAK;AAAA,MACtB,CAAC;AAAA,IACH;AACA,aAAS,KAAK,GAAG,GAAG;AAAA,EACtB;AAEA,UAAQ;AAER,MAAI,iBAAiB;AACnB,UAAM,KAAK,cAAc,WAAW,OAAO;AAC3C,aAAS,KAAK,EAAE;AAAA,EAClB;AAEA,SAAO,MAAM;AACX,aAAS,QAAQ,CAAC,YAAY;AAC5B,gBAAU;AAAA,IACZ,CAAC;AAAA,EACH;AACF;AAEA,SAAS,cAAc,IAAwB,IAAgB;AAC7D,MAAI,CAAC,MAAM,CAAC,GAAG;AAAe;AAC9B,QAAM,MAAM,GAAG,eAAe,eAAe;AAC7C,QAAM,WAAW,IAAI,IAAI,iBAAiB,MAAM;AAC9C,OAAG;AAAA,EACL,CAAC;AACD,WAAS,QAAQ,GAAG,eAAe,EAAE,WAAW,KAAK,CAAC;AACtD,SAAO,MAAM;AACX,aAAS,WAAW;AAAA,EACtB;AACF;", "names": []}