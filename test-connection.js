/**
 * Test script to verify the connection between frontend and backend
 */

const http = require('http');

// Test the API endpoint
const testApiEndpoint = () => {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: '/api',
      method: 'GET',
      timeout: 5000
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          console.log('API Response:', response);
          resolve({ success: true, status: res.statusCode, data: response });
        } catch (error) {
          console.error('Error parsing API response:', error);
          reject({ success: false, error: 'Invalid JSON response' });
        }
      });
    });

    req.on('error', (error) => {
      console.error('API Request Error:', error.message);
      reject({ success: false, error: error.message });
    });

    req.on('timeout', () => {
      req.destroy();
      console.error('API Request Timeout');
      reject({ success: false, error: 'Request timed out' });
    });

    req.end();
  });
};

// Test the health endpoint
const testHealthEndpoint = () => {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/health',
      method: 'GET',
      timeout: 5000
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          console.log('Health Response:', response);
          resolve({ success: true, status: res.statusCode, data: response });
        } catch (error) {
          console.error('Error parsing health response:', error);
          reject({ success: false, error: 'Invalid JSON response' });
        }
      });
    });

    req.on('error', (error) => {
      console.error('Health Request Error:', error.message);
      reject({ success: false, error: error.message });
    });

    req.on('timeout', () => {
      req.destroy();
      console.error('Health Request Timeout');
      reject({ success: false, error: 'Request timed out' });
    });

    req.end();
  });
};

// Test the cryptos endpoint
const testCryptosEndpoint = () => {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/cryptos',
      method: 'GET',
      timeout: 5000
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          console.log('Cryptos Response:', response);
          resolve({ success: true, status: res.statusCode, data: response });
        } catch (error) {
          console.error('Error parsing cryptos response:', error);
          reject({ success: false, error: 'Invalid JSON response' });
        }
      });
    });

    req.on('error', (error) => {
      console.error('Cryptos Request Error:', error.message);
      reject({ success: false, error: error.message });
    });

    req.on('timeout', () => {
      req.destroy();
      console.error('Cryptos Request Timeout');
      reject({ success: false, error: 'Request timed out' });
    });

    req.end();
  });
};

// Run all tests
const runTests = async () => {
  console.log('Testing backend server...');
  console.log('------------------------');
  
  try {
    console.log('1. Testing API endpoint...');
    const apiResult = await testApiEndpoint();
    console.log('API Test Result:', apiResult.success ? 'SUCCESS' : 'FAILED');
    console.log('------------------------');
    
    console.log('2. Testing Health endpoint...');
    const healthResult = await testHealthEndpoint();
    console.log('Health Test Result:', healthResult.success ? 'SUCCESS' : 'FAILED');
    console.log('------------------------');
    
    console.log('3. Testing Cryptos endpoint...');
    const cryptosResult = await testCryptosEndpoint();
    console.log('Cryptos Test Result:', cryptosResult.success ? 'SUCCESS' : 'FAILED');
    console.log('------------------------');
    
    console.log('All tests completed.');
    console.log('Summary:');
    console.log('- API Endpoint:', apiResult.success ? 'OK' : 'FAILED');
    console.log('- Health Endpoint:', healthResult.success ? 'OK' : 'FAILED');
    console.log('- Cryptos Endpoint:', cryptosResult.success ? 'OK' : 'FAILED');
    
    if (apiResult.success && healthResult.success && cryptosResult.success) {
      console.log('✅ Backend server is running properly!');
    } else {
      console.log('❌ Backend server has issues. Check the logs for details.');
    }
  } catch (error) {
    console.error('Error running tests:', error);
    console.log('❌ Backend server is not running or has issues.');
  }
};

// Run the tests
runTests();
