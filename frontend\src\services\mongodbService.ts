/**
 * MongoDB Service
 * 
 * This service handles the connection to MongoDB and provides methods
 * for checking the connection status.
 */

import api from './api';

export interface MongoDBStatus {
  connected: boolean;
  host: string | null;
  name: string | null;
  error: string | null;
  fallbackMode: boolean;
}

const mongodbService = {
  /**
   * Check MongoDB connection status
   * @returns MongoDB connection status
   */
  checkConnection: async (): Promise<MongoDBStatus> => {
    try {
      const response = await api.get('/health');
      
      if (response.data.success && response.data.mongodb) {
        return response.data.mongodb;
      }
      
      return {
        connected: false,
        host: null,
        name: null,
        error: 'Could not retrieve MongoDB status',
        fallbackMode: true
      };
    } catch (error) {
      console.error('Error checking MongoDB connection:', error);
      return {
        connected: false,
        host: null,
        name: null,
        error: 'Failed to check MongoDB connection status',
        fallbackMode: true
      };
    }
  },
  
  /**
   * Initialize MongoDB connection
   * This method should be called when the application starts
   */
  initConnection: async (): Promise<void> => {
    try {
      const status = await mongodbService.checkConnection();
      console.log('MongoDB connection status:', status);
      
      if (!status.connected) {
        console.warn('MongoDB connection failed. Some features may be limited.');
        console.log('Using fallback mode:', status.fallbackMode);
      }
    } catch (error) {
      console.error('Error initializing MongoDB connection:', error);
    }
  }
};

export default mongodbService;
