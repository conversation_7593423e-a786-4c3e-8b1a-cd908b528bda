/**
 * AI Service for Crypto Application
 * 
 * This service provides AI-powered features for cryptocurrency analysis,
 * including price predictions, sentiment analysis, and trading signals.
 */

// Simple moving average calculation
const calculateSMA = (prices, period) => {
  if (prices.length < period) return null;
  
  const sum = prices.slice(prices.length - period).reduce((a, b) => a + b, 0);
  return sum / period;
};

// Exponential moving average calculation
const calculateEMA = (prices, period) => {
  if (prices.length < period) return null;
  
  const k = 2 / (period + 1);
  let ema = prices.slice(0, period).reduce((a, b) => a + b, 0) / period;
  
  for (let i = period; i < prices.length; i++) {
    ema = prices[i] * k + ema * (1 - k);
  }
  
  return ema;
};

// Relative Strength Index calculation
const calculateRSI = (prices, period = 14) => {
  if (prices.length < period + 1) return null;
  
  const changes = [];
  for (let i = 1; i < prices.length; i++) {
    changes.push(prices[i] - prices[i - 1]);
  }
  
  const gains = changes.map(change => change > 0 ? change : 0);
  const losses = changes.map(change => change < 0 ? Math.abs(change) : 0);
  
  let avgGain = gains.slice(0, period).reduce((a, b) => a + b, 0) / period;
  let avgLoss = losses.slice(0, period).reduce((a, b) => a + b, 0) / period;
  
  for (let i = period; i < changes.length; i++) {
    avgGain = (avgGain * (period - 1) + gains[i]) / period;
    avgLoss = (avgLoss * (period - 1) + losses[i]) / period;
  }
  
  if (avgLoss === 0) return 100;
  
  const rs = avgGain / avgLoss;
  return 100 - (100 / (1 + rs));
};

// Generate mock price predictions
const generatePricePrediction = (crypto) => {
  const currentPrice = crypto.currentPrice;
  const volatility = Math.abs(crypto.priceChangePercentage24h) / 100;
  
  // Generate predictions for next 7 days
  const predictions = [];
  let predictedPrice = currentPrice;
  
  for (let i = 1; i <= 7; i++) {
    // Simple random walk model with trend based on recent performance
    const trend = crypto.priceChangePercentage24h > 0 ? 1 : -1;
    const randomFactor = (Math.random() - 0.5) * 2; // Random value between -1 and 1
    const dailyChange = predictedPrice * volatility * (trend * 0.7 + randomFactor * 0.3);
    
    predictedPrice += dailyChange;
    if (predictedPrice < 0) predictedPrice = currentPrice * 0.1; // Prevent negative prices
    
    predictions.push({
      day: i,
      price: predictedPrice,
      date: new Date(Date.now() + i * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    });
  }
  
  return {
    currency: crypto.symbol,
    currentPrice: crypto.currentPrice,
    predictions,
    confidence: Math.floor(Math.random() * 30) + 50, // Random confidence between 50-80%
    methodology: "Time series analysis with volatility adjustment"
  };
};

// Generate trading signals based on technical indicators
const generateTradingSignals = (crypto, historicalPrices = []) => {
  // If no historical prices provided, generate mock data
  if (!historicalPrices.length) {
    historicalPrices = [];
    const volatility = Math.abs(crypto.priceChangePercentage24h) / 100;
    let price = crypto.currentPrice;
    
    // Generate 30 days of historical prices
    for (let i = 30; i > 0; i--) {
      const randomChange = (Math.random() - 0.5) * 2 * volatility * price;
      price = price - randomChange;
      if (price < 0) price = 0.1;
      historicalPrices.unshift(price);
    }
    
    // Add current price
    historicalPrices.push(crypto.currentPrice);
  }
  
  // Calculate technical indicators
  const sma20 = calculateSMA(historicalPrices, 20);
  const sma50 = calculateSMA(historicalPrices, 50);
  const ema12 = calculateEMA(historicalPrices, 12);
  const ema26 = calculateEMA(historicalPrices, 26);
  const rsi = calculateRSI(historicalPrices);
  
  // Determine signals
  const signals = [];
  
  // SMA crossover
  if (sma20 > sma50) {
    signals.push({
      indicator: "SMA Crossover",
      signal: "BUY",
      strength: "Medium",
      description: "20-day SMA crossed above 50-day SMA, indicating potential upward momentum."
    });
  } else if (sma20 < sma50) {
    signals.push({
      indicator: "SMA Crossover",
      signal: "SELL",
      strength: "Medium",
      description: "20-day SMA crossed below 50-day SMA, indicating potential downward momentum."
    });
  }
  
  // MACD (simplified)
  const macd = ema12 - ema26;
  if (macd > 0) {
    signals.push({
      indicator: "MACD",
      signal: "BUY",
      strength: "Strong",
      description: "MACD is positive, indicating bullish momentum."
    });
  } else if (macd < 0) {
    signals.push({
      indicator: "MACD",
      signal: "SELL",
      strength: "Strong",
      description: "MACD is negative, indicating bearish momentum."
    });
  }
  
  // RSI
  if (rsi < 30) {
    signals.push({
      indicator: "RSI",
      signal: "BUY",
      strength: "Strong",
      description: "RSI below 30 indicates the asset may be oversold."
    });
  } else if (rsi > 70) {
    signals.push({
      indicator: "RSI",
      signal: "SELL",
      strength: "Strong",
      description: "RSI above 70 indicates the asset may be overbought."
    });
  }
  
  // Price vs SMA
  if (crypto.currentPrice > sma50 * 1.1) {
    signals.push({
      indicator: "Price vs SMA",
      signal: "CAUTION",
      strength: "Weak",
      description: "Price is significantly above 50-day SMA, potential for reversion to mean."
    });
  } else if (crypto.currentPrice < sma50 * 0.9) {
    signals.push({
      indicator: "Price vs SMA",
      signal: "WATCH",
      strength: "Weak",
      description: "Price is significantly below 50-day SMA, potential for upward correction."
    });
  }
  
  // Overall recommendation
  let buySignals = signals.filter(s => s.signal === "BUY").length;
  let sellSignals = signals.filter(s => s.signal === "SELL").length;
  
  let recommendation;
  if (buySignals > sellSignals) {
    recommendation = "BUY";
  } else if (sellSignals > buySignals) {
    recommendation = "SELL";
  } else {
    recommendation = "HOLD";
  }
  
  return {
    currency: crypto.symbol,
    currentPrice: crypto.currentPrice,
    indicators: {
      sma20,
      sma50,
      ema12,
      ema26,
      macd,
      rsi
    },
    signals,
    recommendation,
    timestamp: new Date().toISOString()
  };
};

// Generate sentiment analysis from mock news and social media
const generateSentimentAnalysis = (crypto) => {
  // Mock news sources
  const newsSources = [
    "CryptoNews",
    "BlockchainTimes",
    "CoinDesk",
    "CoinTelegraph",
    "Bloomberg Crypto",
    "Forbes Digital Assets",
    "The Block",
    "Decrypt"
  ];
  
  // Generate random sentiment scores
  const randomSentiment = () => {
    const baseScore = Math.random();
    // Bias the sentiment based on price change
    const bias = crypto.priceChangePercentage24h / 200; // Small bias based on price movement
    let score = baseScore + bias;
    if (score > 1) score = 1;
    if (score < 0) score = 0;
    return score;
  };
  
  // Generate mock news items
  const news = [];
  const newsCount = Math.floor(Math.random() * 5) + 3; // 3-7 news items
  
  for (let i = 0; i < newsCount; i++) {
    const source = newsSources[Math.floor(Math.random() * newsSources.length)];
    const sentiment = randomSentiment();
    let headline;
    
    if (sentiment > 0.7) {
      headline = `${crypto.name} Shows Strong Potential as ${crypto.symbol} Gains Momentum`;
    } else if (sentiment > 0.4) {
      headline = `Market Analysis: What's Next for ${crypto.symbol}?`;
    } else {
      headline = `Concerns Rise as ${crypto.name} Faces Market Pressure`;
    }
    
    news.push({
      source,
      headline,
      url: `https://example.com/news/${crypto.symbol.toLowerCase()}/${Math.floor(Math.random() * 1000)}`,
      sentiment,
      date: new Date(Date.now() - Math.floor(Math.random() * 3) * 24 * 60 * 60 * 1000).toISOString()
    });
  }
  
  // Generate social media sentiment
  const socialPlatforms = ["Twitter", "Reddit", "Discord", "Telegram"];
  const social = socialPlatforms.map(platform => {
    const sentiment = randomSentiment();
    const volume = Math.floor(Math.random() * 10000) + 500;
    
    return {
      platform,
      sentiment,
      volume,
      trending: sentiment > 0.6 && volume > 5000
    };
  });
  
  // Calculate overall sentiment
  const newsAvgSentiment = news.reduce((sum, item) => sum + item.sentiment, 0) / news.length;
  const socialAvgSentiment = social.reduce((sum, item) => sum + item.sentiment, 0) / social.length;
  const overallSentiment = (newsAvgSentiment * 0.6) + (socialAvgSentiment * 0.4);
  
  // Determine sentiment category
  let sentimentCategory;
  if (overallSentiment > 0.7) {
    sentimentCategory = "Very Bullish";
  } else if (overallSentiment > 0.55) {
    sentimentCategory = "Bullish";
  } else if (overallSentiment > 0.45) {
    sentimentCategory = "Neutral";
  } else if (overallSentiment > 0.3) {
    sentimentCategory = "Bearish";
  } else {
    sentimentCategory = "Very Bearish";
  }
  
  return {
    currency: crypto.symbol,
    news,
    social,
    overallSentiment,
    sentimentCategory,
    wordCloud: ["blockchain", "crypto", crypto.name.toLowerCase(), crypto.symbol.toLowerCase(), "market", "trading", "investment", "technology", "future", "digital"],
    timestamp: new Date().toISOString()
  };
};

// Generate portfolio recommendations
const generatePortfolioRecommendations = (cryptos, riskTolerance = "moderate") => {
  // Sort cryptos by market cap (as a proxy for stability)
  const sortedCryptos = [...cryptos].sort((a, b) => b.marketCap - a.marketCap);
  
  // Assign weights based on risk tolerance
  let weights;
  switch (riskTolerance) {
    case "conservative":
      // Focus on top market cap coins
      weights = sortedCryptos.map((_, index) => {
        if (index < 2) return 0.3;
        if (index < 4) return 0.15;
        if (index < 6) return 0.05;
        return 0;
      });
      break;
    case "moderate":
      // Balanced approach
      weights = sortedCryptos.map((_, index) => {
        if (index < 2) return 0.25;
        if (index < 4) return 0.15;
        if (index < 6) return 0.1;
        return 0;
      });
      break;
    case "aggressive":
      // More diversified with higher weights for smaller coins
      weights = sortedCryptos.map((_, index) => {
        if (index < 2) return 0.2;
        if (index < 4) return 0.15;
        if (index < 6) return 0.1;
        return 0.05;
      });
      break;
    default:
      weights = sortedCryptos.map((_, index) => 1 / sortedCryptos.length);
  }
  
  // Normalize weights to sum to 1
  const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);
  const normalizedWeights = weights.map(weight => weight / totalWeight);
  
  // Create portfolio recommendation
  const portfolio = sortedCryptos.map((crypto, index) => ({
    currency: crypto.symbol,
    name: crypto.name,
    allocation: normalizedWeights[index],
    reasoning: index < 2 
      ? "Core holding with strong market position" 
      : index < 4 
        ? "Medium-cap with growth potential" 
        : "Smaller allocation for diversification"
  })).filter(item => item.allocation > 0);
  
  return {
    riskTolerance,
    portfolio,
    expectedReturn: riskTolerance === "conservative" ? "8-12%" : riskTolerance === "moderate" ? "15-25%" : "25-40%",
    volatility: riskTolerance === "conservative" ? "Low" : riskTolerance === "moderate" ? "Medium" : "High",
    rebalancingPeriod: "Monthly",
    timestamp: new Date().toISOString()
  };
};

module.exports = {
  generatePricePrediction,
  generateTradingSignals,
  generateSentimentAnalysis,
  generatePortfolioRecommendations
};
