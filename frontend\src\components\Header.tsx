import React from 'react';
import {
  Box,
  Flex,
  Heading,
  Spacer,
  Button,
  HStack,
  useColorModeValue,
  Container,
  IconButton,
  useColorMode,
} from '@chakra-ui/react';
import { FaMoon, FaSun } from 'react-icons/fa';

type Page = 'home' | 'market' | 'portfolio' | 'watchlist' | 'ai-insights';

interface HeaderProps {
  currentPage?: Page;
  onPageChange?: (page: Page) => void;
}

const Header: React.FC<HeaderProps> = ({ currentPage = 'home', onPageChange }) => {
  const { colorMode, toggleColorMode } = useColorMode();
  const bg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  const handlePageChange = (page: Page) => {
    if (onPageChange) {
      onPageChange(page);
    }
  };

  return (
    <Box bg={bg} borderBottom="1px" borderColor={borderColor} py={4}>
      <Container maxW="7xl">
        <Flex align="center">
          <Heading
            size="lg"
            color="blue.500"
            cursor="pointer"
            onClick={() => handlePageChange('home')}
          >
            🚀 Crypto Tracker
          </Heading>
          <Spacer />
          <HStack spacing={4}>
            <Button
              variant={currentPage === 'home' ? 'solid' : 'ghost'}
              colorScheme={currentPage === 'home' ? 'blue' : 'gray'}
              onClick={() => handlePageChange('home')}
            >
              Home
            </Button>
            <Button
              variant={currentPage === 'market' ? 'solid' : 'ghost'}
              colorScheme={currentPage === 'market' ? 'blue' : 'gray'}
              onClick={() => handlePageChange('market')}
            >
              Market
            </Button>
            <Button
              variant={currentPage === 'portfolio' ? 'solid' : 'ghost'}
              colorScheme={currentPage === 'portfolio' ? 'blue' : 'gray'}
              onClick={() => handlePageChange('portfolio')}
            >
              Portfolio
            </Button>
            <Button
              variant={currentPage === 'watchlist' ? 'solid' : 'ghost'}
              colorScheme={currentPage === 'watchlist' ? 'blue' : 'gray'}
              onClick={() => handlePageChange('watchlist')}
            >
              Watchlist
            </Button>
            <Button
              variant={currentPage === 'ai-insights' ? 'solid' : 'ghost'}
              colorScheme={currentPage === 'ai-insights' ? 'blue' : 'gray'}
              onClick={() => handlePageChange('ai-insights')}
            >
              AI Insights
            </Button>
            <IconButton
              aria-label="Toggle color mode"
              icon={colorMode === 'light' ? <FaMoon /> : <FaSun />}
              onClick={toggleColorMode}
              variant="ghost"
            />
            <Button colorScheme="blue">Sign In</Button>
          </HStack>
        </Flex>
      </Container>
    </Box>
  );
};

export default Header;
