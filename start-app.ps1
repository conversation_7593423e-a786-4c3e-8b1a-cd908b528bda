# PowerShell script to start both frontend and backend

# Function to check if a directory exists
function Test-DirectoryExists {
    param (
        [string]$Path
    )
    return Test-Path -Path $Path -PathType Container
}

# Function to check if npm is installed
function Test-NpmInstalled {
    try {
        $npmVersion = npm --version
        return $true
    }
    catch {
        return $false
    }
}

# Check if npm is installed
if (-not (Test-NpmInstalled)) {
    Write-Host "Error: npm is not installed or not in PATH. Please install Node.js and npm." -ForegroundColor Red
    exit 1
}

# Check if directories exist
if (-not (Test-DirectoryExists -Path ".\frontend")) {
    Write-Host "Error: frontend directory not found." -ForegroundColor Red
    exit 1
}

if (-not (Test-DirectoryExists -Path ".\backend")) {
    Write-Host "Error: backend directory not found." -ForegroundColor Red
    exit 1
}

# Start backend in a new window
Write-Host "Starting backend server..." -ForegroundColor Green
Start-Process powershell -ArgumentList "-Command", "cd backend; npm install; npm run dev"

# Wait a moment for backend to start
Start-Sleep -Seconds 2

# Start frontend in a new window
Write-Host "Starting frontend application..." -ForegroundColor Green
Start-Process powershell -ArgumentList "-Command", "cd frontend; npm run dev"

Write-Host "Both applications are starting. Check the new PowerShell windows for details." -ForegroundColor Cyan
Write-Host "Frontend will be available at: http://localhost:5173" -ForegroundColor Cyan
Write-Host "Backend API will be available at: http://localhost:3001/api" -ForegroundColor Cyan
