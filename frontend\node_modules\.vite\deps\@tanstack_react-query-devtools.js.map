{"version": 3, "sources": ["../../@tanstack/react-query-devtools/src/ReactQueryDevtools.tsx", "../../@tanstack/query-devtools/build/dev.js", "../../@tanstack/react-query-devtools/src/ReactQueryDevtoolsPanel.tsx", "../../@tanstack/react-query-devtools/src/index.ts"], "sourcesContent": ["'use client'\nimport * as React from 'react'\nimport { onlineManager, useQueryClient } from '@tanstack/react-query'\nimport { TanstackQueryDevtools } from '@tanstack/query-devtools'\nimport type {\n  DevtoolsButtonPosition,\n  DevtoolsErrorType,\n  DevtoolsPosition,\n} from '@tanstack/query-devtools'\nimport type { QueryClient } from '@tanstack/react-query'\n\nexport interface DevtoolsOptions {\n  /**\n   * Set this true if you want the dev tools to default to being open\n   */\n  initialIsOpen?: boolean\n  /**\n   * The position of the React Query logo to open and close the devtools panel.\n   * 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'\n   * Defaults to 'bottom-right'.\n   */\n  buttonPosition?: DevtoolsButtonPosition\n  /**\n   * The position of the React Query devtools panel.\n   * 'top' | 'bottom' | 'left' | 'right'\n   * Defaults to 'bottom'.\n   */\n  position?: DevtoolsPosition\n  /**\n   * Custom instance of QueryClient\n   */\n  client?: QueryClient\n  /**\n   * Use this so you can define custom errors that can be shown in the devtools.\n   */\n  errorTypes?: Array<DevtoolsErrorType>\n  /**\n   * Use this to pass a nonce to the style tag that is added to the document head. This is useful if you are using a Content Security Policy (CSP) nonce to allow inline styles.\n   */\n  styleNonce?: string\n  /**\n   * Use this so you can attach the devtool's styles to specific element in the DOM.\n   */\n  shadowDOMTarget?: ShadowRoot\n}\n\nexport function ReactQueryDevtools(\n  props: DevtoolsOptions,\n): React.ReactElement | null {\n  const queryClient = useQueryClient(props.client)\n  const ref = React.useRef<HTMLDivElement>(null)\n  const {\n    buttonPosition,\n    position,\n    initialIsOpen,\n    errorTypes,\n    styleNonce,\n    shadowDOMTarget,\n  } = props\n  const [devtools] = React.useState(\n    new TanstackQueryDevtools({\n      client: queryClient,\n      queryFlavor: 'React Query',\n      version: '5',\n      onlineManager,\n      buttonPosition,\n      position,\n      initialIsOpen,\n      errorTypes,\n      styleNonce,\n      shadowDOMTarget,\n    }),\n  )\n\n  React.useEffect(() => {\n    devtools.setClient(queryClient)\n  }, [queryClient, devtools])\n\n  React.useEffect(() => {\n    if (buttonPosition) {\n      devtools.setButtonPosition(buttonPosition)\n    }\n  }, [buttonPosition, devtools])\n\n  React.useEffect(() => {\n    if (position) {\n      devtools.setPosition(position)\n    }\n  }, [position, devtools])\n\n  React.useEffect(() => {\n    devtools.setInitialIsOpen(initialIsOpen || false)\n  }, [initialIsOpen, devtools])\n\n  React.useEffect(() => {\n    devtools.setErrorTypes(errorTypes || [])\n  }, [errorTypes, devtools])\n\n  React.useEffect(() => {\n    if (ref.current) {\n      devtools.mount(ref.current)\n    }\n\n    return () => {\n      devtools.unmount()\n    }\n  }, [devtools])\n\n  return <div dir=\"ltr\" className=\"tsqd-parent-container\" ref={ref}></div>\n}\n", "import { createSignal, render, lazy, setupStyleSheet, createComponent, mergeProps } from './chunk/V5T5VJKG.js';\n\n// src/TanstackQueryDevtools.tsx\nvar TanstackQueryDevtools = class {\n  #client;\n  #onlineManager;\n  #queryFlavor;\n  #version;\n  #isMounted = false;\n  #styleNonce;\n  #shadowDOMTarget;\n  #buttonPosition;\n  #position;\n  #initialIsOpen;\n  #errorTypes;\n  #Component;\n  #dispose;\n  constructor(config) {\n    const {\n      client,\n      queryFlavor,\n      version,\n      onlineManager,\n      buttonPosition,\n      position,\n      initialIsOpen,\n      errorTypes,\n      styleNonce,\n      shadowDOMTarget\n    } = config;\n    this.#client = createSignal(client);\n    this.#queryFlavor = queryFlavor;\n    this.#version = version;\n    this.#onlineManager = onlineManager;\n    this.#styleNonce = styleNonce;\n    this.#shadowDOMTarget = shadowDOMTarget;\n    this.#buttonPosition = createSignal(buttonPosition);\n    this.#position = createSignal(position);\n    this.#initialIsOpen = createSignal(initialIsOpen);\n    this.#errorTypes = createSignal(errorTypes);\n  }\n  setButtonPosition(position) {\n    this.#buttonPosition[1](position);\n  }\n  setPosition(position) {\n    this.#position[1](position);\n  }\n  setInitialIsOpen(isOpen) {\n    this.#initialIsOpen[1](isOpen);\n  }\n  setErrorTypes(errorTypes) {\n    this.#errorTypes[1](errorTypes);\n  }\n  setClient(client) {\n    this.#client[1](client);\n  }\n  mount(el) {\n    if (this.#isMounted) {\n      throw new Error(\"Devtools is already mounted\");\n    }\n    const dispose = render(() => {\n      const _self$ = this;\n      const [btnPosition] = this.#buttonPosition;\n      const [pos] = this.#position;\n      const [isOpen] = this.#initialIsOpen;\n      const [errors] = this.#errorTypes;\n      const [queryClient] = this.#client;\n      let Devtools;\n      if (this.#Component) {\n        Devtools = this.#Component;\n      } else {\n        Devtools = lazy(() => import('./DevtoolsComponent/OHEVZFKG.js'));\n        this.#Component = Devtools;\n      }\n      setupStyleSheet(this.#styleNonce, this.#shadowDOMTarget);\n      return createComponent(Devtools, mergeProps({\n        get queryFlavor() {\n          return _self$.#queryFlavor;\n        },\n        get version() {\n          return _self$.#version;\n        },\n        get onlineManager() {\n          return _self$.#onlineManager;\n        },\n        get shadowDOMTarget() {\n          return _self$.#shadowDOMTarget;\n        }\n      }, {\n        get client() {\n          return queryClient();\n        },\n        get buttonPosition() {\n          return btnPosition();\n        },\n        get position() {\n          return pos();\n        },\n        get initialIsOpen() {\n          return isOpen();\n        },\n        get errorTypes() {\n          return errors();\n        }\n      }));\n    }, el);\n    this.#isMounted = true;\n    this.#dispose = dispose;\n  }\n  unmount() {\n    if (!this.#isMounted) {\n      throw new Error(\"Devtools is not mounted\");\n    }\n    this.#dispose?.();\n    this.#isMounted = false;\n  }\n};\n\n// src/TanstackQueryDevtoolsPanel.tsx\nvar TanstackQueryDevtoolsPanel = class {\n  #client;\n  #onlineManager;\n  #queryFlavor;\n  #version;\n  #isMounted = false;\n  #styleNonce;\n  #shadowDOMTarget;\n  #buttonPosition;\n  #position;\n  #initialIsOpen;\n  #errorTypes;\n  #onClose;\n  #Component;\n  #dispose;\n  constructor(config) {\n    const {\n      client,\n      queryFlavor,\n      version,\n      onlineManager,\n      buttonPosition,\n      position,\n      initialIsOpen,\n      errorTypes,\n      styleNonce,\n      shadowDOMTarget,\n      onClose\n    } = config;\n    this.#client = createSignal(client);\n    this.#queryFlavor = queryFlavor;\n    this.#version = version;\n    this.#onlineManager = onlineManager;\n    this.#styleNonce = styleNonce;\n    this.#shadowDOMTarget = shadowDOMTarget;\n    this.#buttonPosition = createSignal(buttonPosition);\n    this.#position = createSignal(position);\n    this.#initialIsOpen = createSignal(initialIsOpen);\n    this.#errorTypes = createSignal(errorTypes);\n    this.#onClose = createSignal(onClose);\n  }\n  setButtonPosition(position) {\n    this.#buttonPosition[1](position);\n  }\n  setPosition(position) {\n    this.#position[1](position);\n  }\n  setInitialIsOpen(isOpen) {\n    this.#initialIsOpen[1](isOpen);\n  }\n  setErrorTypes(errorTypes) {\n    this.#errorTypes[1](errorTypes);\n  }\n  setClient(client) {\n    this.#client[1](client);\n  }\n  setOnClose(onClose) {\n    this.#onClose[1](() => onClose);\n  }\n  mount(el) {\n    if (this.#isMounted) {\n      throw new Error(\"Devtools is already mounted\");\n    }\n    const dispose = render(() => {\n      const _self$ = this;\n      const [btnPosition] = this.#buttonPosition;\n      const [pos] = this.#position;\n      const [isOpen] = this.#initialIsOpen;\n      const [errors] = this.#errorTypes;\n      const [queryClient] = this.#client;\n      const [onClose] = this.#onClose;\n      let Devtools;\n      if (this.#Component) {\n        Devtools = this.#Component;\n      } else {\n        Devtools = lazy(() => import('./DevtoolsPanelComponent/ZXCTK5ZC.js'));\n        this.#Component = Devtools;\n      }\n      setupStyleSheet(this.#styleNonce, this.#shadowDOMTarget);\n      return createComponent(Devtools, mergeProps({\n        get queryFlavor() {\n          return _self$.#queryFlavor;\n        },\n        get version() {\n          return _self$.#version;\n        },\n        get onlineManager() {\n          return _self$.#onlineManager;\n        },\n        get shadowDOMTarget() {\n          return _self$.#shadowDOMTarget;\n        }\n      }, {\n        get client() {\n          return queryClient();\n        },\n        get buttonPosition() {\n          return btnPosition();\n        },\n        get position() {\n          return pos();\n        },\n        get initialIsOpen() {\n          return isOpen();\n        },\n        get errorTypes() {\n          return errors();\n        },\n        get onClose() {\n          return onClose();\n        }\n      }));\n    }, el);\n    this.#isMounted = true;\n    this.#dispose = dispose;\n  }\n  unmount() {\n    if (!this.#isMounted) {\n      throw new Error(\"Devtools is not mounted\");\n    }\n    this.#dispose?.();\n    this.#isMounted = false;\n  }\n};\n\nexport { TanstackQueryDevtools, TanstackQueryDevtoolsPanel };\n", "'use client'\nimport * as React from 'react'\nimport { onlineManager, useQueryClient } from '@tanstack/react-query'\nimport { TanstackQueryDevtoolsPanel } from '@tanstack/query-devtools'\nimport type { DevtoolsErrorType } from '@tanstack/query-devtools'\nimport type { QueryClient } from '@tanstack/react-query'\n\nexport interface DevtoolsPanelOptions {\n  /**\n   * Custom instance of QueryClient\n   */\n  client?: QueryClient\n  /**\n   * Use this so you can define custom errors that can be shown in the devtools.\n   */\n  errorTypes?: Array<DevtoolsErrorType>\n  /**\n   * Use this to pass a nonce to the style tag that is added to the document head. This is useful if you are using a Content Security Policy (CSP) nonce to allow inline styles.\n   */\n  styleNonce?: string\n  /**\n   * Use this so you can attach the devtool's styles to specific element in the DOM.\n   */\n  shadowDOMTarget?: ShadowRoot\n\n  /**\n   * Custom styles for the devtools panel\n   * @default { height: '500px' }\n   * @example { height: '100%' }\n   * @example { height: '100%', width: '100%' }\n   */\n  style?: React.CSSProperties\n\n  /**\n   * Callback function that is called when the devtools panel is closed\n   */\n  onClose?: () => unknown\n}\n\nexport function ReactQueryDevtoolsPanel(\n  props: DevtoolsPanelOptions,\n): React.ReactElement | null {\n  const queryClient = useQueryClient(props.client)\n  const ref = React.useRef<HTMLDivElement>(null)\n  const { errorTypes, styleNonce, shadowDOMTarget } = props\n  const [devtools] = React.useState(\n    new TanstackQueryDevtoolsPanel({\n      client: queryClient,\n      queryFlavor: 'React Query',\n      version: '5',\n      onlineManager,\n      buttonPosition: 'bottom-left',\n      position: 'bottom',\n      initialIsOpen: true,\n      errorTypes,\n      styleNonce,\n      shadowDOMTarget,\n      onClose: props.onClose,\n    }),\n  )\n\n  React.useEffect(() => {\n    devtools.setClient(queryClient)\n  }, [queryClient, devtools])\n\n  React.useEffect(() => {\n    devtools.setOnClose(props.onClose ?? (() => {}))\n  }, [props.onClose, devtools])\n\n  React.useEffect(() => {\n    devtools.setErrorTypes(errorTypes || [])\n  }, [errorTypes, devtools])\n\n  React.useEffect(() => {\n    if (ref.current) {\n      devtools.mount(ref.current)\n    }\n\n    return () => {\n      devtools.unmount()\n    }\n  }, [devtools])\n\n  return (\n    <div\n      style={{ height: '500px', ...props.style }}\n      className=\"tsqd-parent-container\"\n      ref={ref}\n    ></div>\n  )\n}\n", "'use client'\n\nimport * as Devtools from './ReactQueryDevtools'\nimport * as DevtoolsPanel from './ReactQueryDevtoolsPanel'\n\nexport const ReactQueryDevtools: (typeof Devtools)['ReactQueryDevtools'] =\n  process.env.NODE_ENV !== 'development'\n    ? function () {\n        return null\n      }\n    : Devtools.ReactQueryDevtools\n\nexport const ReactQueryDevtoolsPanel: (typeof DevtoolsPanel)['ReactQueryDevtoolsPanel'] =\n  process.env.NODE_ENV !== 'development'\n    ? function () {\n        return null\n      }\n    : DevtoolsPanel.ReactQueryDevtoolsPanel\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,YAAuB;;;ACDvB;AAGA,IAAI,yBAAwB,WAAM;AAAA,EAchC,YAAY,QAAQ;AAbpB;AACA;AACA;AACA;AACA,mCAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEE,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA,eAAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,uBAAK,SAAU,aAAa,MAAM;AAClC,uBAAK,cAAe;AACpB,uBAAK,UAAW;AAChB,uBAAK,gBAAiBA;AACtB,uBAAK,aAAc;AACnB,uBAAK,kBAAmB;AACxB,uBAAK,iBAAkB,aAAa,cAAc;AAClD,uBAAK,WAAY,aAAa,QAAQ;AACtC,uBAAK,gBAAiB,aAAa,aAAa;AAChD,uBAAK,aAAc,aAAa,UAAU;AAAA,EAC5C;AAAA,EACA,kBAAkB,UAAU;AAC1B,uBAAK,iBAAgB,CAAC,EAAE,QAAQ;AAAA,EAClC;AAAA,EACA,YAAY,UAAU;AACpB,uBAAK,WAAU,CAAC,EAAE,QAAQ;AAAA,EAC5B;AAAA,EACA,iBAAiB,QAAQ;AACvB,uBAAK,gBAAe,CAAC,EAAE,MAAM;AAAA,EAC/B;AAAA,EACA,cAAc,YAAY;AACxB,uBAAK,aAAY,CAAC,EAAE,UAAU;AAAA,EAChC;AAAA,EACA,UAAU,QAAQ;AAChB,uBAAK,SAAQ,CAAC,EAAE,MAAM;AAAA,EACxB;AAAA,EACA,MAAM,IAAI;AACR,QAAI,mBAAK,aAAY;AACnB,YAAM,IAAI,MAAM,6BAA6B;AAAA,IAC/C;AACA,UAAM,UAAU,OAAO,MAAM;AAC3B,YAAM,SAAS;AACf,YAAM,CAAC,WAAW,IAAI,mBAAK;AAC3B,YAAM,CAAC,GAAG,IAAI,mBAAK;AACnB,YAAM,CAAC,MAAM,IAAI,mBAAK;AACtB,YAAM,CAAC,MAAM,IAAI,mBAAK;AACtB,YAAM,CAAC,WAAW,IAAI,mBAAK;AAC3B,UAAI;AACJ,UAAI,mBAAK,aAAY;AACnB,mBAAW,mBAAK;AAAA,MAClB,OAAO;AACL,mBAAW,KAAK,MAAM,OAAO,wBAAiC,CAAC;AAC/D,2BAAK,YAAa;AAAA,MACpB;AACA,sBAAgB,mBAAK,cAAa,mBAAK,iBAAgB;AACvD,aAAO,gBAAgB,UAAU,WAAW;AAAA,QAC1C,IAAI,cAAc;AAChB,iBAAO,qBAAO;AAAA,QAChB;AAAA,QACA,IAAI,UAAU;AACZ,iBAAO,qBAAO;AAAA,QAChB;AAAA,QACA,IAAI,gBAAgB;AAClB,iBAAO,qBAAO;AAAA,QAChB;AAAA,QACA,IAAI,kBAAkB;AACpB,iBAAO,qBAAO;AAAA,QAChB;AAAA,MACF,GAAG;AAAA,QACD,IAAI,SAAS;AACX,iBAAO,YAAY;AAAA,QACrB;AAAA,QACA,IAAI,iBAAiB;AACnB,iBAAO,YAAY;AAAA,QACrB;AAAA,QACA,IAAI,WAAW;AACb,iBAAO,IAAI;AAAA,QACb;AAAA,QACA,IAAI,gBAAgB;AAClB,iBAAO,OAAO;AAAA,QAChB;AAAA,QACA,IAAI,aAAa;AACf,iBAAO,OAAO;AAAA,QAChB;AAAA,MACF,CAAC,CAAC;AAAA,IACJ,GAAG,EAAE;AACL,uBAAK,YAAa;AAClB,uBAAK,UAAW;AAAA,EAClB;AAAA,EACA,UAAU;AA7GZ,QAAAC;AA8GI,QAAI,CAAC,mBAAK,aAAY;AACpB,YAAM,IAAI,MAAM,yBAAyB;AAAA,IAC3C;AACA,KAAAA,MAAA,mBAAK,cAAL,gBAAAA,IAAA;AACA,uBAAK,YAAa;AAAA,EACpB;AACF,GAhHE,yBACA,gCACA,8BACA,0BACA,4BACA,6BACA,kCACA,iCACA,2BACA,gCACA,6BACA,4BACA,0BAb0B;AAH5B,IAAAC,UAAAC,iBAAAC,eAAAC,WAAAC,aAAAC,cAAAC,mBAAAC,kBAAAC,YAAAC,iBAAAC,cAAA,UAAAC,aAAAC,WAAAb;AAuHA,IAAI,8BAA6BA,MAAA,MAAM;AAAA,EAerC,YAAY,QAAQ;AAdpB,uBAAAC;AACA,uBAAAC;AACA,uBAAAC;AACA,uBAAAC;AACA,uBAAAC,aAAa;AACb,uBAAAC;AACA,uBAAAC;AACA,uBAAAC;AACA,uBAAAC;AACA,uBAAAC;AACA,uBAAAC;AACA;AACA,uBAAAC;AACA,uBAAAC;AAEE,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA,eAAAd;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,uBAAKE,UAAU,aAAa,MAAM;AAClC,uBAAKE,eAAe;AACpB,uBAAKC,WAAW;AAChB,uBAAKF,iBAAiBH;AACtB,uBAAKO,cAAc;AACnB,uBAAKC,mBAAmB;AACxB,uBAAKC,kBAAkB,aAAa,cAAc;AAClD,uBAAKC,YAAY,aAAa,QAAQ;AACtC,uBAAKC,iBAAiB,aAAa,aAAa;AAChD,uBAAKC,cAAc,aAAa,UAAU;AAC1C,uBAAK,UAAW,aAAa,OAAO;AAAA,EACtC;AAAA,EACA,kBAAkB,UAAU;AAC1B,uBAAKH,kBAAgB,CAAC,EAAE,QAAQ;AAAA,EAClC;AAAA,EACA,YAAY,UAAU;AACpB,uBAAKC,YAAU,CAAC,EAAE,QAAQ;AAAA,EAC5B;AAAA,EACA,iBAAiB,QAAQ;AACvB,uBAAKC,iBAAe,CAAC,EAAE,MAAM;AAAA,EAC/B;AAAA,EACA,cAAc,YAAY;AACxB,uBAAKC,cAAY,CAAC,EAAE,UAAU;AAAA,EAChC;AAAA,EACA,UAAU,QAAQ;AAChB,uBAAKV,UAAQ,CAAC,EAAE,MAAM;AAAA,EACxB;AAAA,EACA,WAAW,SAAS;AAClB,uBAAK,UAAS,CAAC,EAAE,MAAM,OAAO;AAAA,EAChC;AAAA,EACA,MAAM,IAAI;AACR,QAAI,mBAAKI,cAAY;AACnB,YAAM,IAAI,MAAM,6BAA6B;AAAA,IAC/C;AACA,UAAM,UAAU,OAAO,MAAM;AAC3B,YAAM,SAAS;AACf,YAAM,CAAC,WAAW,IAAI,mBAAKG;AAC3B,YAAM,CAAC,GAAG,IAAI,mBAAKC;AACnB,YAAM,CAAC,MAAM,IAAI,mBAAKC;AACtB,YAAM,CAAC,MAAM,IAAI,mBAAKC;AACtB,YAAM,CAAC,WAAW,IAAI,mBAAKV;AAC3B,YAAM,CAAC,OAAO,IAAI,mBAAK;AACvB,UAAI;AACJ,UAAI,mBAAKW,cAAY;AACnB,mBAAW,mBAAKA;AAAA,MAClB,OAAO;AACL,mBAAW,KAAK,MAAM,OAAO,wBAAsC,CAAC;AACpE,2BAAKA,aAAa;AAAA,MACpB;AACA,sBAAgB,mBAAKN,eAAa,mBAAKC,kBAAgB;AACvD,aAAO,gBAAgB,UAAU,WAAW;AAAA,QAC1C,IAAI,cAAc;AAChB,iBAAO,qBAAOJ;AAAA,QAChB;AAAA,QACA,IAAI,UAAU;AACZ,iBAAO,qBAAOC;AAAA,QAChB;AAAA,QACA,IAAI,gBAAgB;AAClB,iBAAO,qBAAOF;AAAA,QAChB;AAAA,QACA,IAAI,kBAAkB;AACpB,iBAAO,qBAAOK;AAAA,QAChB;AAAA,MACF,GAAG;AAAA,QACD,IAAI,SAAS;AACX,iBAAO,YAAY;AAAA,QACrB;AAAA,QACA,IAAI,iBAAiB;AACnB,iBAAO,YAAY;AAAA,QACrB;AAAA,QACA,IAAI,WAAW;AACb,iBAAO,IAAI;AAAA,QACb;AAAA,QACA,IAAI,gBAAgB;AAClB,iBAAO,OAAO;AAAA,QAChB;AAAA,QACA,IAAI,aAAa;AACf,iBAAO,OAAO;AAAA,QAChB;AAAA,QACA,IAAI,UAAU;AACZ,iBAAO,QAAQ;AAAA,QACjB;AAAA,MACF,CAAC,CAAC;AAAA,IACJ,GAAG,EAAE;AACL,uBAAKF,aAAa;AAClB,uBAAKQ,WAAW;AAAA,EAClB;AAAA,EACA,UAAU;AA3OZ,QAAAb;AA4OI,QAAI,CAAC,mBAAKK,cAAY;AACpB,YAAM,IAAI,MAAM,yBAAyB;AAAA,IAC3C;AACA,KAAAL,MAAA,mBAAKa,eAAL,gBAAAb,IAAA;AACA,uBAAKK,aAAa;AAAA,EACpB;AACF,GA1HEJ,WAAA,eACAC,kBAAA,eACAC,gBAAA,eACAC,YAAA,eACAC,cAAA,eACAC,eAAA,eACAC,oBAAA,eACAC,mBAAA,eACAC,aAAA,eACAC,kBAAA,eACAC,eAAA,eACA,0BACAC,cAAA,eACAC,YAAA,eAd+Bb;;;ADXxB,yBAAA;AA9DF,SAAS,mBACd,OAC2B;AAC3B,QAAM,cAAc,eAAe,MAAM,MAAM;AAC/C,QAAM,MAAY,aAAuB,IAAI;AAC7C,QAAM;IACJ;IACA;IACA;IACA;IACA;IACA;EACF,IAAI;AACJ,QAAM,CAAC,QAAQ,IAAU;IACvB,IAAI,sBAAsB;MACxB,QAAQ;MACR,aAAa;MACb,SAAS;MACT;MACA;MACA;MACA;MACA;MACA;MACA;IACF,CAAC;EACH;AAEM,EAAA,gBAAU,MAAM;AACpB,aAAS,UAAU,WAAW;EAChC,GAAG,CAAC,aAAa,QAAQ,CAAC;AAEpB,EAAA,gBAAU,MAAM;AACpB,QAAI,gBAAgB;AAClB,eAAS,kBAAkB,cAAc;IAC3C;EACF,GAAG,CAAC,gBAAgB,QAAQ,CAAC;AAEvB,EAAA,gBAAU,MAAM;AACpB,QAAI,UAAU;AACZ,eAAS,YAAY,QAAQ;IAC/B;EACF,GAAG,CAAC,UAAU,QAAQ,CAAC;AAEjB,EAAA,gBAAU,MAAM;AACpB,aAAS,iBAAiB,iBAAiB,KAAK;EAClD,GAAG,CAAC,eAAe,QAAQ,CAAC;AAEtB,EAAA,gBAAU,MAAM;AACpB,aAAS,cAAc,cAAc,CAAC,CAAC;EACzC,GAAG,CAAC,YAAY,QAAQ,CAAC;AAEnB,EAAA,gBAAU,MAAM;AACpB,QAAI,IAAI,SAAS;AACf,eAAS,MAAM,IAAI,OAAO;IAC5B;AAEA,WAAO,MAAM;AACX,eAAS,QAAQ;IACnB;EACF,GAAG,CAAC,QAAQ,CAAC;AAEb,aAAO,wBAAC,OAAA,EAAI,KAAI,OAAM,WAAU,yBAAwB,IAAA,CAAU;AACpE;;;AE5GA,IAAAc,SAAuB;AAmFnB,IAAAC,sBAAA;AA7CG,SAAS,wBACd,OAC2B;AAC3B,QAAM,cAAc,eAAe,MAAM,MAAM;AAC/C,QAAM,MAAY,cAAuB,IAAI;AAC7C,QAAM,EAAE,YAAY,YAAY,gBAAgB,IAAI;AACpD,QAAM,CAAC,QAAQ,IAAU;IACvB,IAAI,2BAA2B;MAC7B,QAAQ;MACR,aAAa;MACb,SAAS;MACT;MACA,gBAAgB;MAChB,UAAU;MACV,eAAe;MACf;MACA;MACA;MACA,SAAS,MAAM;IACjB,CAAC;EACH;AAEM,EAAA,iBAAU,MAAM;AACpB,aAAS,UAAU,WAAW;EAChC,GAAG,CAAC,aAAa,QAAQ,CAAC;AAEpB,EAAA,iBAAU,MAAM;AACpB,aAAS,WAAW,MAAM,YAAY,MAAM;IAAC,EAAE;EACjD,GAAG,CAAC,MAAM,SAAS,QAAQ,CAAC;AAEtB,EAAA,iBAAU,MAAM;AACpB,aAAS,cAAc,cAAc,CAAC,CAAC;EACzC,GAAG,CAAC,YAAY,QAAQ,CAAC;AAEnB,EAAA,iBAAU,MAAM;AACpB,QAAI,IAAI,SAAS;AACf,eAAS,MAAM,IAAI,OAAO;IAC5B;AAEA,WAAO,MAAM;AACX,eAAS,QAAQ;IACnB;EACF,GAAG,CAAC,QAAQ,CAAC;AAEb,aACE;IAAC;IAAA;MACC,OAAO,EAAE,QAAQ,SAAS,GAAG,MAAM,MAAM;MACzC,WAAU;MACV;IAAA;EACD;AAEL;;;ACrFO,IAAMC,sBACX,QACI,WAAY;AACV,SAAO;AACT,IACS;AAER,IAAMC,2BACX,QACI,WAAY;AACV,SAAO;AACT,IACc;", "names": ["onlineManager", "_a", "_client", "_onlineManager", "_queryFlavor", "_version", "_isMounted", "_styleNonce", "_shadowD<PERSON><PERSON><PERSON>get", "_buttonPosition", "_position", "_initialIsOpen", "_errorTypes", "_Component", "_dispose", "React", "import_jsx_runtime", "ReactQueryDevtools", "ReactQueryDevtoolsPanel"]}