import React from 'react';
import {
  Box,
  Heading,
  Text,
  Flex,
  Badge,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Skeleton,
  useColorModeValue,
  Tooltip,
  Icon,
  Divider,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
} from '@chakra-ui/react';
import { InfoIcon, TriangleUpIcon, TriangleDownIcon, QuestionIcon } from '@chakra-ui/icons';
import { useTradingSignals } from '../../hooks/useAI';
import { formatCurrency } from '../../utils/format';

interface TradingSignalsProps {
  cryptoId: string;
  symbol: string;
}

const TradingSignals: React.FC<TradingSignalsProps> = ({ cryptoId, symbol }) => {
  const { data, isLoading, error } = useTradingSignals(cryptoId);
  
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  
  if (isLoading) {
    return (
      <Box p={5} shadow="md" borderWidth="1px" borderRadius="lg" bg={bgColor}>
        <Skeleton height="20px" width="200px" mb={4} />
        <Skeleton height="300px" />
      </Box>
    );
  }
  
  if (error || !data) {
    return (
      <Box p={5} shadow="md" borderWidth="1px" borderRadius="lg" bg={bgColor}>
        <Heading size="md" mb={4}>Trading Signals</Heading>
        <Text color="red.500">Failed to load trading signals</Text>
      </Box>
    );
  }
  
  // Get signal color
  const getSignalColor = (signal: string) => {
    switch (signal) {
      case 'BUY':
        return 'green';
      case 'SELL':
        return 'red';
      case 'HOLD':
        return 'blue';
      case 'WATCH':
        return 'purple';
      case 'CAUTION':
        return 'orange';
      default:
        return 'gray';
    }
  };
  
  // Get strength color
  const getStrengthColor = (strength: string) => {
    switch (strength) {
      case 'Strong':
        return 'green';
      case 'Medium':
        return 'blue';
      case 'Weak':
        return 'gray';
      default:
        return 'gray';
    }
  };
  
  return (
    <Box p={5} shadow="md" borderWidth="1px" borderRadius="lg" bg={bgColor} borderColor={borderColor}>
      <Flex justify="space-between" align="center" mb={4}>
        <Heading size="md">Trading Signals ({symbol})</Heading>
        <Badge 
          colorScheme={getSignalColor(data.recommendation)} 
          fontSize="md" 
          px={3} 
          py={1}
        >
          {data.recommendation}
        </Badge>
      </Flex>
      
      <SimpleGrid columns={{ base: 2, md: 3 }} spacing={4} mb={6}>
        <Stat>
          <StatLabel>RSI</StatLabel>
          <StatNumber>{data.indicators.rsi.toFixed(2)}</StatNumber>
          <StatHelpText>
            {data.indicators.rsi < 30 ? 'Oversold' : data.indicators.rsi > 70 ? 'Overbought' : 'Neutral'}
          </StatHelpText>
        </Stat>
        
        <Stat>
          <StatLabel>MACD</StatLabel>
          <StatNumber>{data.indicators.macd.toFixed(2)}</StatNumber>
          <StatHelpText color={data.indicators.macd >= 0 ? 'green.500' : 'red.500'}>
            {data.indicators.macd >= 0 ? 'Bullish' : 'Bearish'}
          </StatHelpText>
        </Stat>
        
        <Stat>
          <StatLabel>SMA Crossover</StatLabel>
          <StatNumber>
            {data.indicators.sma20 > data.indicators.sma50 ? (
              <Flex align="center">
                <TriangleUpIcon color="green.500" mr={1} />
                <Text>Bullish</Text>
              </Flex>
            ) : (
              <Flex align="center">
                <TriangleDownIcon color="red.500" mr={1} />
                <Text>Bearish</Text>
              </Flex>
            )}
          </StatNumber>
          <StatHelpText>
            SMA20: {data.indicators.sma20.toFixed(2)}
          </StatHelpText>
        </Stat>
      </SimpleGrid>
      
      <Divider my={4} />
      
      <Heading size="sm" mb={3}>Signal Details</Heading>
      
      <Table variant="simple" size="sm">
        <Thead>
          <Tr>
            <Th>Indicator</Th>
            <Th>Signal</Th>
            <Th>Strength</Th>
            <Th display={{ base: 'none', md: 'table-cell' }}>Description</Th>
          </Tr>
        </Thead>
        <Tbody>
          {data.signals.map((signal, index) => (
            <Tr key={index}>
              <Td>
                <Flex align="center">
                  {signal.indicator}
                  <Tooltip label="Learn more about this indicator">
                    <InfoIcon ml={1} fontSize="xs" color="gray.500" />
                  </Tooltip>
                </Flex>
              </Td>
              <Td>
                <Badge colorScheme={getSignalColor(signal.signal)}>
                  {signal.signal}
                </Badge>
              </Td>
              <Td>
                <Badge colorScheme={getStrengthColor(signal.strength)} variant="outline">
                  {signal.strength}
                </Badge>
              </Td>
              <Td display={{ base: 'none', md: 'table-cell' }}>
                <Text fontSize="xs">{signal.description}</Text>
              </Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
      
      <Flex mt={4} align="center">
        <InfoIcon mr={2} color="gray.500" />
        <Text fontSize="sm" color="gray.500">
          Last updated: {new Date(data.timestamp).toLocaleString()}
        </Text>
      </Flex>
    </Box>
  );
};

export default TradingSignals;
