/**
 * Authentication Configuration
 * 
 * This file contains configuration settings for authentication.
 */

module.exports = {
  // JWT settings
  JWT_SECRET: process.env.JWT_SECRET || 'crypto-tracker-secret-key-dev',
  JWT_EXPIRES_IN: process.env.JWT_EXPIRES_IN || '30d',
  JWT_COOKIE_EXPIRES_IN: process.env.JWT_COOKIE_EXPIRES_IN || 30,
  
  // Password settings
  PASSWORD_MIN_LENGTH: 8,
  PASSWORD_SALT_ROUNDS: 10,
  
  // Social login settings
  GOOGLE_CLIENT_ID: process.env.GOOGLE_CLIENT_ID,
  GOOGLE_CLIENT_SECRET: process.env.GOOGLE_CLIENT_SECRET,
  GOOGLE_CALLBACK_URL: process.env.GOOGLE_CALLBACK_URL || 'http://localhost:3001/api/auth/google/callback',
  
  FACEBOOK_APP_ID: process.env.FACEBOOK_APP_ID,
  FACEBOOK_APP_SECRET: process.env.FACEBOOK_APP_SECRET,
  FACEBOOK_CALLBACK_URL: process.env.FACEBOOK_CALLBACK_URL || 'http://localhost:3001/api/auth/facebook/callback',
  
  GITHUB_CLIENT_ID: process.env.GITHUB_CLIENT_ID,
  GITHUB_CLIENT_SECRET: process.env.GITHUB_CLIENT_SECRET,
  GITHUB_CALLBACK_URL: process.env.GITHUB_CALLBACK_URL || 'http://localhost:3001/api/auth/github/callback',
  
  // Session settings
  SESSION_SECRET: process.env.SESSION_SECRET || 'crypto-tracker-session-secret-dev',
  SESSION_EXPIRES_IN: process.env.SESSION_EXPIRES_IN || '1d',
};
