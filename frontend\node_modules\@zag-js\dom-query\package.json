{"name": "@zag-js/dom-query", "version": "0.31.1", "description": "The dom helper library for zag.js machines", "keywords": ["js", "utils", "dom", "query"], "author": "<PERSON><PERSON> <<EMAIL>>", "homepage": "https://github.com/chakra-ui/zag#readme", "license": "MIT", "repository": "https://github.com/chakra-ui/zag/tree/main/packages/utilities/dom-helpers", "sideEffects": false, "files": ["dist", "src"], "publishConfig": {"access": "public"}, "bugs": {"url": "https://github.com/chakra-ui/zag/issues"}, "clean-package": "../../../clean-package.config.json", "main": "dist/index.js", "devDependencies": {"clean-package": "2.2.0"}, "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json"}, "scripts": {"build": "tsup", "lint": "eslint src --ext .ts,.tsx", "typecheck": "tsc --noEmit"}}