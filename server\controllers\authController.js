/**
 * Authentication Controller
 *
 * This controller handles user authentication, including registration,
 * login, social login, and token management.
 */

const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const asyncHandler = require('express-async-handler');
const User = require('../models/User');
const config = require('../config/auth');

/**
 * Generate JWT token
 * @param {string} id - User ID
 * @param {string} email - User email
 * @param {string} username - User username
 * @param {string} role - User role
 * @returns {string} - JWT token
 */
const generateToken = (id, email, username, role) => {
  return jwt.sign(
    { id, email, username, role },
    config.JWT_SECRET,
    { expiresIn: config.JWT_EXPIRES_IN }
  );
};

/**
 * Register a new user
 * @route POST /api/auth/register
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} - JSON response with user data and token
 */
const register = asyncHandler(async (req, res) => {
  const { username, email, password } = req.body;

  // Check if user already exists
  const userExists = await User.findOne({ email });
  if (userExists) {
    res.status(400);
    throw new Error('User already exists');
  }

  // Check password length
  if (password.length < config.PASSWORD_MIN_LENGTH) {
    res.status(400);
    throw new Error(`Password must be at least ${config.PASSWORD_MIN_LENGTH} characters`);
  }

  // Create new user (password will be hashed by the pre-save hook)
  const user = await User.create({
    username,
    email,
    password,
    role: 'user',
  });

  if (user) {
    // Generate token
    const token = generateToken(user._id, user.email, user.username, user.role);

    // Return user data and token
    res.status(201).json({
      success: true,
      data: {
        id: user._id,
        username: user.username,
        email: user.email,
        role: user.role,
        token,
      },
    });
  } else {
    res.status(400);
    throw new Error('Invalid user data');
  }
});

/**
 * Login user
 * @route POST /api/auth/login
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} - JSON response with user data and token
 */
const login = asyncHandler(async (req, res) => {
  const { email, password } = req.body;

  // Find user by email and include password for verification
  const user = await User.findOne({ email }).select('+password');

  // Check if user exists and password matches
  if (!user || !(await user.matchPassword(password))) {
    res.status(401);
    throw new Error('Invalid email or password');
  }

  // Generate token
  const token = generateToken(user._id, user.email, user.username, user.role);

  // Return user data and token
  res.json({
    success: true,
    data: {
      id: user._id,
      username: user.username,
      email: user.email,
      role: user.role,
      token,
    },
  });
});

/**
 * Get current user profile
 * @route GET /api/auth/me
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} - JSON response with user data
 */
const getMe = asyncHandler(async (req, res) => {
  // Find user by ID (req.user.id is set by the auth middleware)
  const user = await User.findById(req.user.id);

  if (!user) {
    res.status(404);
    throw new Error('User not found');
  }

  // Return user data
  res.json({
    success: true,
    data: {
      id: user._id,
      username: user.username,
      email: user.email,
      role: user.role,
      profilePicture: user.profilePicture,
      isEmailVerified: user.isEmailVerified,
      createdAt: user.createdAt,
    },
  });
});

/**
 * Update user profile
 * @route PUT /api/auth/me
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} - JSON response with updated user data
 */
const updateMe = asyncHandler(async (req, res) => {
  const { username, email, password, profilePicture } = req.body;

  // Find user by ID
  const user = await User.findById(req.user.id);

  if (!user) {
    res.status(404);
    throw new Error('User not found');
  }

  // Check if email is already taken by another user
  if (email && email !== user.email) {
    const emailExists = await User.findOne({ email });
    if (emailExists) {
      res.status(400);
      throw new Error('Email already in use');
    }
    user.email = email;
  }

  // Update user fields if provided
  if (username) user.username = username;
  if (profilePicture) user.profilePicture = profilePicture;

  // Update password if provided
  if (password) {
    // Check password length
    if (password.length < config.PASSWORD_MIN_LENGTH) {
      res.status(400);
      throw new Error(`Password must be at least ${config.PASSWORD_MIN_LENGTH} characters`);
    }
    user.password = password; // Will be hashed by pre-save hook
  }

  // Save updated user
  const updatedUser = await user.save();

  // Generate new token
  const token = generateToken(updatedUser._id, updatedUser.email, updatedUser.username, updatedUser.role);

  // Return updated user data and token
  res.json({
    success: true,
    data: {
      id: updatedUser._id,
      username: updatedUser.username,
      email: updatedUser.email,
      role: updatedUser.role,
      profilePicture: updatedUser.profilePicture,
      token,
    },
  });
});

/**
 * Google OAuth login callback
 * @route GET /api/auth/google/callback
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} - JSON response with user data and token
 */
const googleCallback = async (req, res) => {
  // This would be implemented with Passport.js in a real application
  // For now, we'll just return a mock response
  return res.json({
    success: true,
    message: 'Google authentication not implemented in this demo',
  });
};

/**
 * Facebook OAuth login callback
 * @route GET /api/auth/facebook/callback
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} - JSON response with user data and token
 */
const facebookCallback = async (req, res) => {
  // This would be implemented with Passport.js in a real application
  // For now, we'll just return a mock response
  return res.json({
    success: true,
    message: 'Facebook authentication not implemented in this demo',
  });
};

/**
 * GitHub OAuth login callback
 * @route GET /api/auth/github/callback
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} - JSON response with user data and token
 */
const githubCallback = async (req, res) => {
  // This would be implemented with Passport.js in a real application
  // For now, we'll just return a mock response
  return res.json({
    success: true,
    message: 'GitHub authentication not implemented in this demo',
  });
};

module.exports = {
  register,
  login,
  getMe,
  updateMe,
  googleCallback,
  facebookCallback,
  githubCallback,
};
