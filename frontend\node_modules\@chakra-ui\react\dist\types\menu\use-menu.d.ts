/// <reference types="react" />
/// <reference types="react" />
import { UseDisclosureProps } from "@chakra-ui/hooks";
import { LazyMode } from "@chakra-ui/utils";
import { UsePopperProps } from "../popper";
export declare const MenuDescendantsProvider: import("react").Provider<import("../descendant").DescendantsManager<HTMLElement, {}>>, useMenuDescendantsContext: () => import("../descendant").DescendantsManager<HTMLElement, {}>, useMenuDescendants: () => import("../descendant").DescendantsManager<HTMLElement, {}>, useMenuDescendant: (options?: {
    disabled?: boolean | undefined;
    id?: string | undefined;
} | undefined) => import("../descendant/use-descendant").UseDescendantReturn<HTMLElement, {}>;
export declare const MenuProvider: import("react").Provider<Omit<UseMenuReturn, "descendants">>, useMenuContext: () => Omit<UseMenuReturn, "descendants">;
export interface UseMenuProps extends Omit<UsePopperProps, "enabled">, UseDisclosureProps {
    /**
     * The `ref` of the element that should receive focus when the popover opens.
     */
    initialFocusRef?: React.RefObject<{
        focus(): void;
    } | null>;
    /**
     * If `true`, the menu will close when a menu item is
     * clicked
     *
     * @default true
     */
    closeOnSelect?: boolean;
    /**
     * If `true`, the menu will close when you click outside
     * the menu list
     *
     * @default true
     */
    closeOnBlur?: boolean;
    /**
     * If `true`, the first enabled menu item will receive focus and be selected
     * when the menu opens.
     *
     * @default true
     */
    autoSelect?: boolean;
    /**
     * Performance 🚀:
     * If `true`, the MenuItem rendering will be deferred
     * until the menu is open.
     *
     * @default false
     */
    isLazy?: boolean;
    /**
     * Performance 🚀:
     * The lazy behavior of menu's content when not visible.
     * Only works when `isLazy={true}`
     *
     * - "unmount": The menu's content is always unmounted when not open.
     * - "keepMounted": The menu's content initially unmounted,
     * but stays mounted when menu is open.
     *
     * @default "unmount"
     */
    lazyBehavior?: LazyMode;
    /**
     * If `rtl`, proper placement positions will be flipped i.e. 'top-right' will
     * become 'top-left' and vice-verse
     */
    direction?: "ltr" | "rtl";
    computePositionOnMount?: boolean;
}
/**
 * React Hook to manage a menu
 *
 * It provides the logic and will be used with react context
 * to propagate its return value to all children
 */
export declare function useMenu(props?: UseMenuProps): {
    openAndFocusMenu: () => void;
    openAndFocusFirstItem: () => void;
    openAndFocusLastItem: () => void;
    onTransitionEnd: () => void;
    unstable__animationState: {
        present: boolean;
        onComplete(): void;
    };
    descendants: import("../descendant").DescendantsManager<HTMLElement, {}>;
    popper: any;
    buttonId: string;
    menuId: string;
    forceUpdate: any;
    orientation: string;
    isOpen: boolean;
    onToggle: () => void;
    onOpen: () => void;
    onClose: () => void;
    menuRef: import("react").MutableRefObject<HTMLDivElement | null>;
    buttonRef: import("react").MutableRefObject<HTMLButtonElement | null>;
    focusedIndex: number;
    closeOnSelect: boolean;
    closeOnBlur: boolean;
    autoSelect: boolean;
    setFocusedIndex: import("react").Dispatch<import("react").SetStateAction<number>>;
    isLazy: boolean | undefined;
    lazyBehavior: LazyMode;
    initialFocusRef: import("react").RefObject<{
        focus(): void;
    } | null> | undefined;
    scrollIntoViewRef: import("react").MutableRefObject<boolean>;
};
export interface UseMenuReturn extends ReturnType<typeof useMenu> {
}
export interface UseMenuButtonProps extends Omit<React.HTMLAttributes<Element>, "color"> {
}
/**
 * React Hook to manage a menu button.
 *
 * The assumption here is that the `useMenu` hook is used
 * in a component higher up the tree, and its return value
 * is passed as `context` to this hook.
 */
export declare function useMenuButton(props?: UseMenuButtonProps, externalRef?: React.Ref<any>): {
    ref: (node: any) => void;
    id: string;
    "data-active": boolean | "false" | "true";
    "aria-expanded": boolean;
    "aria-haspopup": boolean | "dialog" | "menu" | "grid" | "listbox" | "false" | "true" | "tree" | undefined;
    "aria-controls": string;
    onClick: (event: import("react").MouseEvent<Element, MouseEvent>) => void;
    onKeyDown: (event: import("react").KeyboardEvent<Element>) => void;
    children?: import("react").ReactNode;
    nonce?: string | undefined;
    style?: import("react").CSSProperties | undefined;
    slot?: string | undefined;
    title?: string | undefined;
    dangerouslySetInnerHTML?: {
        __html: string | TrustedHTML;
    } | undefined;
    content?: string | undefined;
    translate?: "yes" | "no" | undefined;
    hidden?: boolean | undefined;
    defaultChecked?: boolean | undefined;
    defaultValue?: string | number | readonly string[] | undefined;
    suppressContentEditableWarning?: boolean | undefined;
    suppressHydrationWarning?: boolean | undefined;
    accessKey?: string | undefined;
    autoCapitalize?: (string & {}) | "none" | "off" | "on" | "sentences" | "words" | "characters" | undefined;
    autoFocus?: boolean | undefined;
    className?: string | undefined;
    contentEditable?: "inherit" | (boolean | "false" | "true") | "plaintext-only" | undefined;
    contextMenu?: string | undefined;
    dir?: string | undefined;
    draggable?: (boolean | "false" | "true") | undefined;
    enterKeyHint?: "search" | "done" | "enter" | "go" | "next" | "previous" | "send" | undefined;
    lang?: string | undefined;
    spellCheck?: (boolean | "false" | "true") | undefined;
    tabIndex?: number | undefined;
    radioGroup?: string | undefined;
    role?: import("react").AriaRole | undefined;
    about?: string | undefined;
    datatype?: string | undefined;
    inlist?: any;
    prefix?: string | undefined;
    property?: string | undefined;
    rel?: string | undefined;
    resource?: string | undefined;
    rev?: string | undefined;
    typeof?: string | undefined;
    vocab?: string | undefined;
    autoCorrect?: string | undefined;
    autoSave?: string | undefined;
    itemProp?: string | undefined;
    itemScope?: boolean | undefined;
    itemType?: string | undefined;
    itemID?: string | undefined;
    itemRef?: string | undefined;
    results?: number | undefined;
    security?: string | undefined;
    unselectable?: "off" | "on" | undefined;
    inputMode?: "search" | "text" | "none" | "email" | "tel" | "url" | "numeric" | "decimal" | undefined;
    is?: string | undefined;
    "aria-activedescendant"?: string | undefined;
    "aria-atomic"?: (boolean | "false" | "true") | undefined;
    "aria-autocomplete"?: "both" | "none" | "inline" | "list" | undefined;
    "aria-braillelabel"?: string | undefined;
    "aria-brailleroledescription"?: string | undefined;
    "aria-busy"?: (boolean | "false" | "true") | undefined;
    "aria-checked"?: boolean | "mixed" | "false" | "true" | undefined;
    "aria-colcount"?: number | undefined;
    "aria-colindex"?: number | undefined;
    "aria-colindextext"?: string | undefined;
    "aria-colspan"?: number | undefined;
    "aria-current"?: boolean | "time" | "page" | "false" | "true" | "step" | "date" | "location" | undefined;
    "aria-describedby"?: string | undefined;
    "aria-description"?: string | undefined;
    "aria-details"?: string | undefined;
    "aria-disabled"?: (boolean | "false" | "true") | undefined;
    "aria-dropeffect"?: "link" | "none" | "copy" | "move" | "execute" | "popup" | undefined;
    "aria-errormessage"?: string | undefined;
    "aria-flowto"?: string | undefined;
    "aria-grabbed"?: (boolean | "false" | "true") | undefined;
    "aria-hidden"?: (boolean | "false" | "true") | undefined;
    "aria-invalid"?: boolean | "false" | "true" | "grammar" | "spelling" | undefined;
    "aria-keyshortcuts"?: string | undefined;
    "aria-label"?: string | undefined;
    "aria-labelledby"?: string | undefined;
    "aria-level"?: number | undefined;
    "aria-live"?: "off" | "assertive" | "polite" | undefined;
    "aria-modal"?: (boolean | "false" | "true") | undefined;
    "aria-multiline"?: (boolean | "false" | "true") | undefined;
    "aria-multiselectable"?: (boolean | "false" | "true") | undefined;
    "aria-orientation"?: "horizontal" | "vertical" | undefined;
    "aria-owns"?: string | undefined;
    "aria-placeholder"?: string | undefined;
    "aria-posinset"?: number | undefined;
    "aria-pressed"?: boolean | "mixed" | "false" | "true" | undefined;
    "aria-readonly"?: (boolean | "false" | "true") | undefined;
    "aria-relevant"?: "all" | "text" | "additions" | "additions removals" | "additions text" | "removals" | "removals additions" | "removals text" | "text additions" | "text removals" | undefined;
    "aria-required"?: (boolean | "false" | "true") | undefined;
    "aria-roledescription"?: string | undefined;
    "aria-rowcount"?: number | undefined;
    "aria-rowindex"?: number | undefined;
    "aria-rowindextext"?: string | undefined;
    "aria-rowspan"?: number | undefined;
    "aria-selected"?: (boolean | "false" | "true") | undefined;
    "aria-setsize"?: number | undefined;
    "aria-sort"?: "none" | "ascending" | "descending" | "other" | undefined;
    "aria-valuemax"?: number | undefined;
    "aria-valuemin"?: number | undefined;
    "aria-valuenow"?: number | undefined;
    "aria-valuetext"?: string | undefined;
    onCopy?: import("react").ClipboardEventHandler<Element> | undefined;
    onCopyCapture?: import("react").ClipboardEventHandler<Element> | undefined;
    onCut?: import("react").ClipboardEventHandler<Element> | undefined;
    onCutCapture?: import("react").ClipboardEventHandler<Element> | undefined;
    onPaste?: import("react").ClipboardEventHandler<Element> | undefined;
    onPasteCapture?: import("react").ClipboardEventHandler<Element> | undefined;
    onCompositionEnd?: import("react").CompositionEventHandler<Element> | undefined;
    onCompositionEndCapture?: import("react").CompositionEventHandler<Element> | undefined;
    onCompositionStart?: import("react").CompositionEventHandler<Element> | undefined;
    onCompositionStartCapture?: import("react").CompositionEventHandler<Element> | undefined;
    onCompositionUpdate?: import("react").CompositionEventHandler<Element> | undefined;
    onCompositionUpdateCapture?: import("react").CompositionEventHandler<Element> | undefined;
    onFocus?: import("react").FocusEventHandler<Element> | undefined;
    onFocusCapture?: import("react").FocusEventHandler<Element> | undefined;
    onBlur?: import("react").FocusEventHandler<Element> | undefined;
    onBlurCapture?: import("react").FocusEventHandler<Element> | undefined;
    onChange?: import("react").FormEventHandler<Element> | undefined;
    onChangeCapture?: import("react").FormEventHandler<Element> | undefined;
    onBeforeInput?: import("react").FormEventHandler<Element> | undefined;
    onBeforeInputCapture?: import("react").FormEventHandler<Element> | undefined;
    onInput?: import("react").FormEventHandler<Element> | undefined;
    onInputCapture?: import("react").FormEventHandler<Element> | undefined;
    onReset?: import("react").FormEventHandler<Element> | undefined;
    onResetCapture?: import("react").FormEventHandler<Element> | undefined;
    onSubmit?: import("react").FormEventHandler<Element> | undefined;
    onSubmitCapture?: import("react").FormEventHandler<Element> | undefined;
    onInvalid?: import("react").FormEventHandler<Element> | undefined;
    onInvalidCapture?: import("react").FormEventHandler<Element> | undefined;
    onLoad?: import("react").ReactEventHandler<Element> | undefined;
    onLoadCapture?: import("react").ReactEventHandler<Element> | undefined;
    onError?: import("react").ReactEventHandler<Element> | undefined;
    onErrorCapture?: import("react").ReactEventHandler<Element> | undefined;
    onKeyDownCapture?: import("react").KeyboardEventHandler<Element> | undefined;
    onKeyPress?: import("react").KeyboardEventHandler<Element> | undefined;
    onKeyPressCapture?: import("react").KeyboardEventHandler<Element> | undefined;
    onKeyUp?: import("react").KeyboardEventHandler<Element> | undefined;
    onKeyUpCapture?: import("react").KeyboardEventHandler<Element> | undefined;
    onAbort?: import("react").ReactEventHandler<Element> | undefined;
    onAbortCapture?: import("react").ReactEventHandler<Element> | undefined;
    onCanPlay?: import("react").ReactEventHandler<Element> | undefined;
    onCanPlayCapture?: import("react").ReactEventHandler<Element> | undefined;
    onCanPlayThrough?: import("react").ReactEventHandler<Element> | undefined;
    onCanPlayThroughCapture?: import("react").ReactEventHandler<Element> | undefined;
    onDurationChange?: import("react").ReactEventHandler<Element> | undefined;
    onDurationChangeCapture?: import("react").ReactEventHandler<Element> | undefined;
    onEmptied?: import("react").ReactEventHandler<Element> | undefined;
    onEmptiedCapture?: import("react").ReactEventHandler<Element> | undefined;
    onEncrypted?: import("react").ReactEventHandler<Element> | undefined;
    onEncryptedCapture?: import("react").ReactEventHandler<Element> | undefined;
    onEnded?: import("react").ReactEventHandler<Element> | undefined;
    onEndedCapture?: import("react").ReactEventHandler<Element> | undefined;
    onLoadedData?: import("react").ReactEventHandler<Element> | undefined;
    onLoadedDataCapture?: import("react").ReactEventHandler<Element> | undefined;
    onLoadedMetadata?: import("react").ReactEventHandler<Element> | undefined;
    onLoadedMetadataCapture?: import("react").ReactEventHandler<Element> | undefined;
    onLoadStart?: import("react").ReactEventHandler<Element> | undefined;
    onLoadStartCapture?: import("react").ReactEventHandler<Element> | undefined;
    onPause?: import("react").ReactEventHandler<Element> | undefined;
    onPauseCapture?: import("react").ReactEventHandler<Element> | undefined;
    onPlay?: import("react").ReactEventHandler<Element> | undefined;
    onPlayCapture?: import("react").ReactEventHandler<Element> | undefined;
    onPlaying?: import("react").ReactEventHandler<Element> | undefined;
    onPlayingCapture?: import("react").ReactEventHandler<Element> | undefined;
    onProgress?: import("react").ReactEventHandler<Element> | undefined;
    onProgressCapture?: import("react").ReactEventHandler<Element> | undefined;
    onRateChange?: import("react").ReactEventHandler<Element> | undefined;
    onRateChangeCapture?: import("react").ReactEventHandler<Element> | undefined;
    onResize?: import("react").ReactEventHandler<Element> | undefined;
    onResizeCapture?: import("react").ReactEventHandler<Element> | undefined;
    onSeeked?: import("react").ReactEventHandler<Element> | undefined;
    onSeekedCapture?: import("react").ReactEventHandler<Element> | undefined;
    onSeeking?: import("react").ReactEventHandler<Element> | undefined;
    onSeekingCapture?: import("react").ReactEventHandler<Element> | undefined;
    onStalled?: import("react").ReactEventHandler<Element> | undefined;
    onStalledCapture?: import("react").ReactEventHandler<Element> | undefined;
    onSuspend?: import("react").ReactEventHandler<Element> | undefined;
    onSuspendCapture?: import("react").ReactEventHandler<Element> | undefined;
    onTimeUpdate?: import("react").ReactEventHandler<Element> | undefined;
    onTimeUpdateCapture?: import("react").ReactEventHandler<Element> | undefined;
    onVolumeChange?: import("react").ReactEventHandler<Element> | undefined;
    onVolumeChangeCapture?: import("react").ReactEventHandler<Element> | undefined;
    onWaiting?: import("react").ReactEventHandler<Element> | undefined;
    onWaitingCapture?: import("react").ReactEventHandler<Element> | undefined;
    onAuxClick?: import("react").MouseEventHandler<Element> | undefined;
    onAuxClickCapture?: import("react").MouseEventHandler<Element> | undefined;
    onClickCapture?: import("react").MouseEventHandler<Element> | undefined;
    onContextMenu?: import("react").MouseEventHandler<Element> | undefined;
    onContextMenuCapture?: import("react").MouseEventHandler<Element> | undefined;
    onDoubleClick?: import("react").MouseEventHandler<Element> | undefined;
    onDoubleClickCapture?: import("react").MouseEventHandler<Element> | undefined;
    onDrag?: import("react").DragEventHandler<Element> | undefined;
    onDragCapture?: import("react").DragEventHandler<Element> | undefined;
    onDragEnd?: import("react").DragEventHandler<Element> | undefined;
    onDragEndCapture?: import("react").DragEventHandler<Element> | undefined;
    onDragEnter?: import("react").DragEventHandler<Element> | undefined;
    onDragEnterCapture?: import("react").DragEventHandler<Element> | undefined;
    onDragExit?: import("react").DragEventHandler<Element> | undefined;
    onDragExitCapture?: import("react").DragEventHandler<Element> | undefined;
    onDragLeave?: import("react").DragEventHandler<Element> | undefined;
    onDragLeaveCapture?: import("react").DragEventHandler<Element> | undefined;
    onDragOver?: import("react").DragEventHandler<Element> | undefined;
    onDragOverCapture?: import("react").DragEventHandler<Element> | undefined;
    onDragStart?: import("react").DragEventHandler<Element> | undefined;
    onDragStartCapture?: import("react").DragEventHandler<Element> | undefined;
    onDrop?: import("react").DragEventHandler<Element> | undefined;
    onDropCapture?: import("react").DragEventHandler<Element> | undefined;
    onMouseDown?: import("react").MouseEventHandler<Element> | undefined;
    onMouseDownCapture?: import("react").MouseEventHandler<Element> | undefined;
    onMouseEnter?: import("react").MouseEventHandler<Element> | undefined;
    onMouseLeave?: import("react").MouseEventHandler<Element> | undefined;
    onMouseMove?: import("react").MouseEventHandler<Element> | undefined;
    onMouseMoveCapture?: import("react").MouseEventHandler<Element> | undefined;
    onMouseOut?: import("react").MouseEventHandler<Element> | undefined;
    onMouseOutCapture?: import("react").MouseEventHandler<Element> | undefined;
    onMouseOver?: import("react").MouseEventHandler<Element> | undefined;
    onMouseOverCapture?: import("react").MouseEventHandler<Element> | undefined;
    onMouseUp?: import("react").MouseEventHandler<Element> | undefined;
    onMouseUpCapture?: import("react").MouseEventHandler<Element> | undefined;
    onSelect?: import("react").ReactEventHandler<Element> | undefined;
    onSelectCapture?: import("react").ReactEventHandler<Element> | undefined;
    onTouchCancel?: import("react").TouchEventHandler<Element> | undefined;
    onTouchCancelCapture?: import("react").TouchEventHandler<Element> | undefined;
    onTouchEnd?: import("react").TouchEventHandler<Element> | undefined;
    onTouchEndCapture?: import("react").TouchEventHandler<Element> | undefined;
    onTouchMove?: import("react").TouchEventHandler<Element> | undefined;
    onTouchMoveCapture?: import("react").TouchEventHandler<Element> | undefined;
    onTouchStart?: import("react").TouchEventHandler<Element> | undefined;
    onTouchStartCapture?: import("react").TouchEventHandler<Element> | undefined;
    onPointerDown?: import("react").PointerEventHandler<Element> | undefined;
    onPointerDownCapture?: import("react").PointerEventHandler<Element> | undefined;
    onPointerMove?: import("react").PointerEventHandler<Element> | undefined;
    onPointerMoveCapture?: import("react").PointerEventHandler<Element> | undefined;
    onPointerUp?: import("react").PointerEventHandler<Element> | undefined;
    onPointerUpCapture?: import("react").PointerEventHandler<Element> | undefined;
    onPointerCancel?: import("react").PointerEventHandler<Element> | undefined;
    onPointerCancelCapture?: import("react").PointerEventHandler<Element> | undefined;
    onPointerEnter?: import("react").PointerEventHandler<Element> | undefined;
    onPointerLeave?: import("react").PointerEventHandler<Element> | undefined;
    onPointerOver?: import("react").PointerEventHandler<Element> | undefined;
    onPointerOverCapture?: import("react").PointerEventHandler<Element> | undefined;
    onPointerOut?: import("react").PointerEventHandler<Element> | undefined;
    onPointerOutCapture?: import("react").PointerEventHandler<Element> | undefined;
    onGotPointerCapture?: import("react").PointerEventHandler<Element> | undefined;
    onGotPointerCaptureCapture?: import("react").PointerEventHandler<Element> | undefined;
    onLostPointerCapture?: import("react").PointerEventHandler<Element> | undefined;
    onLostPointerCaptureCapture?: import("react").PointerEventHandler<Element> | undefined;
    onScroll?: import("react").UIEventHandler<Element> | undefined;
    onScrollCapture?: import("react").UIEventHandler<Element> | undefined;
    onWheel?: import("react").WheelEventHandler<Element> | undefined;
    onWheelCapture?: import("react").WheelEventHandler<Element> | undefined;
    onAnimationStart?: import("react").AnimationEventHandler<Element> | undefined;
    onAnimationStartCapture?: import("react").AnimationEventHandler<Element> | undefined;
    onAnimationEnd?: import("react").AnimationEventHandler<Element> | undefined;
    onAnimationEndCapture?: import("react").AnimationEventHandler<Element> | undefined;
    onAnimationIteration?: import("react").AnimationEventHandler<Element> | undefined;
    onAnimationIterationCapture?: import("react").AnimationEventHandler<Element> | undefined;
    onTransitionEnd?: import("react").TransitionEventHandler<Element> | undefined;
    onTransitionEndCapture?: import("react").TransitionEventHandler<Element> | undefined;
};
export interface UseMenuListProps extends Omit<React.HTMLAttributes<Element>, "color"> {
}
/**
 * React Hook to manage a menu list.
 *
 * The assumption here is that the `useMenu` hook is used
 * in a component higher up the tree, and its return value
 * is passed as `context` to this hook.
 */
export declare function useMenuList(props?: UseMenuListProps, ref?: React.Ref<any>): React.HTMLAttributes<HTMLElement> & React.RefAttributes<HTMLElement>;
export declare function useMenuPositioner(props?: any): any;
export interface UseMenuItemProps extends Omit<React.HTMLAttributes<Element>, "color" | "disabled"> {
    /**
     * If `true`, the menuitem will be disabled
     */
    isDisabled?: boolean;
    /**
     * If `true` and the menuitem is disabled, it'll
     * remain keyboard-focusable
     */
    isFocusable?: boolean;
    /**
     * Overrides the parent menu's `closeOnSelect` prop.
     */
    closeOnSelect?: boolean;
    /**
     * The type of the menuitem.
     */
    type?: React.ButtonHTMLAttributes<HTMLButtonElement>["type"];
}
export declare function useMenuItem(props?: UseMenuItemProps, externalRef?: React.Ref<any>): {
    type: any;
    id: string;
    role: string;
    tabIndex: number;
    ref: (node: any) => void;
    "aria-disabled": boolean | undefined;
    disabled: boolean | undefined;
    onClick: (event: import("react").MouseEvent<HTMLElement, MouseEvent>) => void;
    onMouseDown: import("react").MouseEventHandler<HTMLElement> | undefined;
    onMouseUp: import("react").MouseEventHandler<HTMLElement> | undefined;
    onKeyUp: import("react").KeyboardEventHandler<HTMLElement> | undefined;
    onKeyDown: import("react").KeyboardEventHandler<HTMLElement> | undefined;
    onMouseOver: import("react").MouseEventHandler<HTMLElement> | undefined;
    onMouseLeave: import("react").MouseEventHandler<HTMLElement> | undefined;
    defaultChecked?: boolean | undefined;
    defaultValue?: string | number | readonly string[] | undefined;
    suppressContentEditableWarning?: boolean | undefined;
    suppressHydrationWarning?: boolean | undefined;
    accessKey?: string | undefined;
    autoCapitalize?: (string & {}) | "none" | "off" | "on" | "sentences" | "words" | "characters" | undefined;
    autoFocus?: boolean | undefined;
    className?: string | undefined;
    contentEditable?: "inherit" | (boolean | "false" | "true") | "plaintext-only" | undefined;
    contextMenu?: string | undefined;
    dir?: string | undefined;
    draggable?: (boolean | "false" | "true") | undefined;
    enterKeyHint?: "search" | "done" | "enter" | "go" | "next" | "previous" | "send" | undefined;
    hidden?: boolean | undefined;
    lang?: string | undefined;
    nonce?: string | undefined;
    slot?: string | undefined;
    spellCheck?: (boolean | "false" | "true") | undefined;
    style?: import("react").CSSProperties | undefined;
    title?: string | undefined;
    translate?: "yes" | "no" | undefined;
    radioGroup?: string | undefined;
    about?: string | undefined;
    content?: string | undefined;
    datatype?: string | undefined;
    inlist?: any;
    prefix?: string | undefined;
    property?: string | undefined;
    rel?: string | undefined;
    resource?: string | undefined;
    rev?: string | undefined;
    typeof?: string | undefined;
    vocab?: string | undefined;
    autoCorrect?: string | undefined;
    autoSave?: string | undefined;
    color?: string | undefined;
    itemProp?: string | undefined;
    itemScope?: boolean | undefined;
    itemType?: string | undefined;
    itemID?: string | undefined;
    itemRef?: string | undefined;
    results?: number | undefined;
    security?: string | undefined;
    unselectable?: "off" | "on" | undefined;
    inputMode?: "search" | "text" | "none" | "email" | "tel" | "url" | "numeric" | "decimal" | undefined;
    is?: string | undefined;
    "aria-activedescendant"?: string | undefined;
    "aria-atomic"?: (boolean | "false" | "true") | undefined;
    "aria-autocomplete"?: "both" | "none" | "inline" | "list" | undefined;
    "aria-braillelabel"?: string | undefined;
    "aria-brailleroledescription"?: string | undefined;
    "aria-busy"?: (boolean | "false" | "true") | undefined;
    "aria-checked"?: boolean | "mixed" | "false" | "true" | undefined;
    "aria-colcount"?: number | undefined;
    "aria-colindex"?: number | undefined;
    "aria-colindextext"?: string | undefined;
    "aria-colspan"?: number | undefined;
    "aria-controls"?: string | undefined;
    "aria-current"?: boolean | "time" | "page" | "false" | "true" | "step" | "date" | "location" | undefined;
    "aria-describedby"?: string | undefined;
    "aria-description"?: string | undefined;
    "aria-details"?: string | undefined;
    "aria-dropeffect"?: "link" | "none" | "copy" | "move" | "execute" | "popup" | undefined;
    "aria-errormessage"?: string | undefined;
    "aria-expanded"?: (boolean | "false" | "true") | undefined;
    "aria-flowto"?: string | undefined;
    "aria-grabbed"?: (boolean | "false" | "true") | undefined;
    "aria-haspopup"?: boolean | "dialog" | "menu" | "grid" | "listbox" | "false" | "true" | "tree" | undefined;
    "aria-hidden"?: (boolean | "false" | "true") | undefined;
    "aria-invalid"?: boolean | "false" | "true" | "grammar" | "spelling" | undefined;
    "aria-keyshortcuts"?: string | undefined;
    "aria-label"?: string | undefined;
    "aria-labelledby"?: string | undefined;
    "aria-level"?: number | undefined;
    "aria-live"?: "off" | "assertive" | "polite" | undefined;
    "aria-modal"?: (boolean | "false" | "true") | undefined;
    "aria-multiline"?: (boolean | "false" | "true") | undefined;
    "aria-multiselectable"?: (boolean | "false" | "true") | undefined;
    "aria-orientation"?: "horizontal" | "vertical" | undefined;
    "aria-owns"?: string | undefined;
    "aria-placeholder"?: string | undefined;
    "aria-posinset"?: number | undefined;
    "aria-pressed"?: boolean | "mixed" | "false" | "true" | undefined;
    "aria-readonly"?: (boolean | "false" | "true") | undefined;
    "aria-relevant"?: "all" | "text" | "additions" | "additions removals" | "additions text" | "removals" | "removals additions" | "removals text" | "text additions" | "text removals" | undefined;
    "aria-required"?: (boolean | "false" | "true") | undefined;
    "aria-roledescription"?: string | undefined;
    "aria-rowcount"?: number | undefined;
    "aria-rowindex"?: number | undefined;
    "aria-rowindextext"?: string | undefined;
    "aria-rowspan"?: number | undefined;
    "aria-selected"?: (boolean | "false" | "true") | undefined;
    "aria-setsize"?: number | undefined;
    "aria-sort"?: "none" | "ascending" | "descending" | "other" | undefined;
    "aria-valuemax"?: number | undefined;
    "aria-valuemin"?: number | undefined;
    "aria-valuenow"?: number | undefined;
    "aria-valuetext"?: string | undefined;
    children?: import("react").ReactNode;
    dangerouslySetInnerHTML?: {
        __html: string | TrustedHTML;
    } | undefined;
    onCopy?: import("react").ClipboardEventHandler<HTMLElement> | undefined;
    onCopyCapture?: import("react").ClipboardEventHandler<HTMLElement> | undefined;
    onCut?: import("react").ClipboardEventHandler<HTMLElement> | undefined;
    onCutCapture?: import("react").ClipboardEventHandler<HTMLElement> | undefined;
    onPaste?: import("react").ClipboardEventHandler<HTMLElement> | undefined;
    onPasteCapture?: import("react").ClipboardEventHandler<HTMLElement> | undefined;
    onCompositionEnd?: import("react").CompositionEventHandler<HTMLElement> | undefined;
    onCompositionEndCapture?: import("react").CompositionEventHandler<HTMLElement> | undefined;
    onCompositionStart?: import("react").CompositionEventHandler<HTMLElement> | undefined;
    onCompositionStartCapture?: import("react").CompositionEventHandler<HTMLElement> | undefined;
    onCompositionUpdate?: import("react").CompositionEventHandler<HTMLElement> | undefined;
    onCompositionUpdateCapture?: import("react").CompositionEventHandler<HTMLElement> | undefined;
    onFocus?: import("react").FocusEventHandler<HTMLElement> | undefined;
    onFocusCapture?: import("react").FocusEventHandler<HTMLElement> | undefined;
    onBlur?: import("react").FocusEventHandler<HTMLElement> | undefined;
    onBlurCapture?: import("react").FocusEventHandler<HTMLElement> | undefined;
    onChange?: import("react").FormEventHandler<HTMLElement> | undefined;
    onChangeCapture?: import("react").FormEventHandler<HTMLElement> | undefined;
    onBeforeInput?: import("react").FormEventHandler<HTMLElement> | undefined;
    onBeforeInputCapture?: import("react").FormEventHandler<HTMLElement> | undefined;
    onInput?: import("react").FormEventHandler<HTMLElement> | undefined;
    onInputCapture?: import("react").FormEventHandler<HTMLElement> | undefined;
    onReset?: import("react").FormEventHandler<HTMLElement> | undefined;
    onResetCapture?: import("react").FormEventHandler<HTMLElement> | undefined;
    onSubmit?: import("react").FormEventHandler<HTMLElement> | undefined;
    onSubmitCapture?: import("react").FormEventHandler<HTMLElement> | undefined;
    onInvalid?: import("react").FormEventHandler<HTMLElement> | undefined;
    onInvalidCapture?: import("react").FormEventHandler<HTMLElement> | undefined;
    onLoad?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onLoadCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onError?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onErrorCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onKeyDownCapture?: import("react").KeyboardEventHandler<HTMLElement> | undefined;
    onKeyPress?: import("react").KeyboardEventHandler<HTMLElement> | undefined;
    onKeyPressCapture?: import("react").KeyboardEventHandler<HTMLElement> | undefined;
    onKeyUpCapture?: import("react").KeyboardEventHandler<HTMLElement> | undefined;
    onAbort?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onAbortCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onCanPlay?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onCanPlayCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onCanPlayThrough?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onCanPlayThroughCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onDurationChange?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onDurationChangeCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onEmptied?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onEmptiedCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onEncrypted?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onEncryptedCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onEnded?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onEndedCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onLoadedData?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onLoadedDataCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onLoadedMetadata?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onLoadedMetadataCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onLoadStart?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onLoadStartCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onPause?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onPauseCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onPlay?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onPlayCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onPlaying?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onPlayingCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onProgress?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onProgressCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onRateChange?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onRateChangeCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onResize?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onResizeCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onSeeked?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onSeekedCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onSeeking?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onSeekingCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onStalled?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onStalledCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onSuspend?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onSuspendCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onTimeUpdate?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onTimeUpdateCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onVolumeChange?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onVolumeChangeCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onWaiting?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onWaitingCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onAuxClick?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onAuxClickCapture?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onClickCapture?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onContextMenu?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onContextMenuCapture?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onDoubleClick?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onDoubleClickCapture?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onDrag?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragCapture?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragEnd?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragEndCapture?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragEnter?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragEnterCapture?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragExit?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragExitCapture?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragLeave?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragLeaveCapture?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragOver?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragOverCapture?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragStart?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragStartCapture?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDrop?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDropCapture?: import("react").DragEventHandler<HTMLElement> | undefined;
    onMouseDownCapture?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onMouseEnter?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onMouseMove?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onMouseMoveCapture?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onMouseOut?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onMouseOutCapture?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onMouseOverCapture?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onMouseUpCapture?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onSelect?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onSelectCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onTouchCancel?: import("react").TouchEventHandler<HTMLElement> | undefined;
    onTouchCancelCapture?: import("react").TouchEventHandler<HTMLElement> | undefined;
    onTouchEnd?: import("react").TouchEventHandler<HTMLElement> | undefined;
    onTouchEndCapture?: import("react").TouchEventHandler<HTMLElement> | undefined;
    onTouchMove?: import("react").TouchEventHandler<HTMLElement> | undefined;
    onTouchMoveCapture?: import("react").TouchEventHandler<HTMLElement> | undefined;
    onTouchStart?: import("react").TouchEventHandler<HTMLElement> | undefined;
    onTouchStartCapture?: import("react").TouchEventHandler<HTMLElement> | undefined;
    onPointerDown?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerDownCapture?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerMove?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerMoveCapture?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerUp?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerUpCapture?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerCancel?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerCancelCapture?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerEnter?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerLeave?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerOver?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerOverCapture?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerOut?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerOutCapture?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onGotPointerCapture?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onGotPointerCaptureCapture?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onLostPointerCapture?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onLostPointerCaptureCapture?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onScroll?: import("react").UIEventHandler<HTMLElement> | undefined;
    onScrollCapture?: import("react").UIEventHandler<HTMLElement> | undefined;
    onWheel?: import("react").WheelEventHandler<HTMLElement> | undefined;
    onWheelCapture?: import("react").WheelEventHandler<HTMLElement> | undefined;
    onAnimationStart?: import("react").AnimationEventHandler<HTMLElement> | undefined;
    onAnimationStartCapture?: import("react").AnimationEventHandler<HTMLElement> | undefined;
    onAnimationEnd?: import("react").AnimationEventHandler<HTMLElement> | undefined;
    onAnimationEndCapture?: import("react").AnimationEventHandler<HTMLElement> | undefined;
    onAnimationIteration?: import("react").AnimationEventHandler<HTMLElement> | undefined;
    onAnimationIterationCapture?: import("react").AnimationEventHandler<HTMLElement> | undefined;
    onTransitionEnd?: import("react").TransitionEventHandler<HTMLElement> | undefined;
    onTransitionEndCapture?: import("react").TransitionEventHandler<HTMLElement> | undefined;
} | {
    type: any;
    id: string;
    role: string;
    tabIndex: number;
    ref: (node: any) => void;
    "data-active": boolean | "false" | "true";
    "aria-disabled": "true" | undefined;
    onClick: (event: import("react").MouseEvent<HTMLElement, MouseEvent>) => void;
    onMouseDown: (event: import("react").MouseEvent<HTMLElement, MouseEvent>) => void;
    onMouseUp: (event: import("react").MouseEvent<HTMLElement, MouseEvent>) => void;
    onKeyUp: (event: import("react").KeyboardEvent<HTMLElement>) => void;
    onKeyDown: (event: import("react").KeyboardEvent<HTMLElement>) => void;
    onMouseOver: (event: import("react").MouseEvent<HTMLElement, MouseEvent>) => void;
    onMouseLeave: (event: import("react").MouseEvent<HTMLElement, MouseEvent>) => void;
    defaultChecked?: boolean | undefined;
    defaultValue?: string | number | readonly string[] | undefined;
    suppressContentEditableWarning?: boolean | undefined;
    suppressHydrationWarning?: boolean | undefined;
    accessKey?: string | undefined;
    autoCapitalize?: (string & {}) | "none" | "off" | "on" | "sentences" | "words" | "characters" | undefined;
    autoFocus?: boolean | undefined;
    className?: string | undefined;
    contentEditable?: "inherit" | (boolean | "false" | "true") | "plaintext-only" | undefined;
    contextMenu?: string | undefined;
    dir?: string | undefined;
    draggable?: (boolean | "false" | "true") | undefined;
    enterKeyHint?: "search" | "done" | "enter" | "go" | "next" | "previous" | "send" | undefined;
    hidden?: boolean | undefined;
    lang?: string | undefined;
    nonce?: string | undefined;
    slot?: string | undefined;
    spellCheck?: (boolean | "false" | "true") | undefined;
    style?: import("react").CSSProperties | undefined;
    title?: string | undefined;
    translate?: "yes" | "no" | undefined;
    radioGroup?: string | undefined;
    about?: string | undefined;
    content?: string | undefined;
    datatype?: string | undefined;
    inlist?: any;
    prefix?: string | undefined;
    property?: string | undefined;
    rel?: string | undefined;
    resource?: string | undefined;
    rev?: string | undefined;
    typeof?: string | undefined;
    vocab?: string | undefined;
    autoCorrect?: string | undefined;
    autoSave?: string | undefined;
    color?: string | undefined;
    itemProp?: string | undefined;
    itemScope?: boolean | undefined;
    itemType?: string | undefined;
    itemID?: string | undefined;
    itemRef?: string | undefined;
    results?: number | undefined;
    security?: string | undefined;
    unselectable?: "off" | "on" | undefined;
    inputMode?: "search" | "text" | "none" | "email" | "tel" | "url" | "numeric" | "decimal" | undefined;
    is?: string | undefined;
    "aria-activedescendant"?: string | undefined;
    "aria-atomic"?: (boolean | "false" | "true") | undefined;
    "aria-autocomplete"?: "both" | "none" | "inline" | "list" | undefined;
    "aria-braillelabel"?: string | undefined;
    "aria-brailleroledescription"?: string | undefined;
    "aria-busy"?: (boolean | "false" | "true") | undefined;
    "aria-checked"?: boolean | "mixed" | "false" | "true" | undefined;
    "aria-colcount"?: number | undefined;
    "aria-colindex"?: number | undefined;
    "aria-colindextext"?: string | undefined;
    "aria-colspan"?: number | undefined;
    "aria-controls"?: string | undefined;
    "aria-current"?: boolean | "time" | "page" | "false" | "true" | "step" | "date" | "location" | undefined;
    "aria-describedby"?: string | undefined;
    "aria-description"?: string | undefined;
    "aria-details"?: string | undefined;
    "aria-dropeffect"?: "link" | "none" | "copy" | "move" | "execute" | "popup" | undefined;
    "aria-errormessage"?: string | undefined;
    "aria-expanded"?: (boolean | "false" | "true") | undefined;
    "aria-flowto"?: string | undefined;
    "aria-grabbed"?: (boolean | "false" | "true") | undefined;
    "aria-haspopup"?: boolean | "dialog" | "menu" | "grid" | "listbox" | "false" | "true" | "tree" | undefined;
    "aria-hidden"?: (boolean | "false" | "true") | undefined;
    "aria-invalid"?: boolean | "false" | "true" | "grammar" | "spelling" | undefined;
    "aria-keyshortcuts"?: string | undefined;
    "aria-label"?: string | undefined;
    "aria-labelledby"?: string | undefined;
    "aria-level"?: number | undefined;
    "aria-live"?: "off" | "assertive" | "polite" | undefined;
    "aria-modal"?: (boolean | "false" | "true") | undefined;
    "aria-multiline"?: (boolean | "false" | "true") | undefined;
    "aria-multiselectable"?: (boolean | "false" | "true") | undefined;
    "aria-orientation"?: "horizontal" | "vertical" | undefined;
    "aria-owns"?: string | undefined;
    "aria-placeholder"?: string | undefined;
    "aria-posinset"?: number | undefined;
    "aria-pressed"?: boolean | "mixed" | "false" | "true" | undefined;
    "aria-readonly"?: (boolean | "false" | "true") | undefined;
    "aria-relevant"?: "all" | "text" | "additions" | "additions removals" | "additions text" | "removals" | "removals additions" | "removals text" | "text additions" | "text removals" | undefined;
    "aria-required"?: (boolean | "false" | "true") | undefined;
    "aria-roledescription"?: string | undefined;
    "aria-rowcount"?: number | undefined;
    "aria-rowindex"?: number | undefined;
    "aria-rowindextext"?: string | undefined;
    "aria-rowspan"?: number | undefined;
    "aria-selected"?: (boolean | "false" | "true") | undefined;
    "aria-setsize"?: number | undefined;
    "aria-sort"?: "none" | "ascending" | "descending" | "other" | undefined;
    "aria-valuemax"?: number | undefined;
    "aria-valuemin"?: number | undefined;
    "aria-valuenow"?: number | undefined;
    "aria-valuetext"?: string | undefined;
    children?: import("react").ReactNode;
    dangerouslySetInnerHTML?: {
        __html: string | TrustedHTML;
    } | undefined;
    onCopy?: import("react").ClipboardEventHandler<HTMLElement> | undefined;
    onCopyCapture?: import("react").ClipboardEventHandler<HTMLElement> | undefined;
    onCut?: import("react").ClipboardEventHandler<HTMLElement> | undefined;
    onCutCapture?: import("react").ClipboardEventHandler<HTMLElement> | undefined;
    onPaste?: import("react").ClipboardEventHandler<HTMLElement> | undefined;
    onPasteCapture?: import("react").ClipboardEventHandler<HTMLElement> | undefined;
    onCompositionEnd?: import("react").CompositionEventHandler<HTMLElement> | undefined;
    onCompositionEndCapture?: import("react").CompositionEventHandler<HTMLElement> | undefined;
    onCompositionStart?: import("react").CompositionEventHandler<HTMLElement> | undefined;
    onCompositionStartCapture?: import("react").CompositionEventHandler<HTMLElement> | undefined;
    onCompositionUpdate?: import("react").CompositionEventHandler<HTMLElement> | undefined;
    onCompositionUpdateCapture?: import("react").CompositionEventHandler<HTMLElement> | undefined;
    onFocus?: import("react").FocusEventHandler<HTMLElement> | undefined;
    onFocusCapture?: import("react").FocusEventHandler<HTMLElement> | undefined;
    onBlur?: import("react").FocusEventHandler<HTMLElement> | undefined;
    onBlurCapture?: import("react").FocusEventHandler<HTMLElement> | undefined;
    onChange?: import("react").FormEventHandler<HTMLElement> | undefined;
    onChangeCapture?: import("react").FormEventHandler<HTMLElement> | undefined;
    onBeforeInput?: import("react").FormEventHandler<HTMLElement> | undefined;
    onBeforeInputCapture?: import("react").FormEventHandler<HTMLElement> | undefined;
    onInput?: import("react").FormEventHandler<HTMLElement> | undefined;
    onInputCapture?: import("react").FormEventHandler<HTMLElement> | undefined;
    onReset?: import("react").FormEventHandler<HTMLElement> | undefined;
    onResetCapture?: import("react").FormEventHandler<HTMLElement> | undefined;
    onSubmit?: import("react").FormEventHandler<HTMLElement> | undefined;
    onSubmitCapture?: import("react").FormEventHandler<HTMLElement> | undefined;
    onInvalid?: import("react").FormEventHandler<HTMLElement> | undefined;
    onInvalidCapture?: import("react").FormEventHandler<HTMLElement> | undefined;
    onLoad?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onLoadCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onError?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onErrorCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onKeyDownCapture?: import("react").KeyboardEventHandler<HTMLElement> | undefined;
    onKeyPress?: import("react").KeyboardEventHandler<HTMLElement> | undefined;
    onKeyPressCapture?: import("react").KeyboardEventHandler<HTMLElement> | undefined;
    onKeyUpCapture?: import("react").KeyboardEventHandler<HTMLElement> | undefined;
    onAbort?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onAbortCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onCanPlay?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onCanPlayCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onCanPlayThrough?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onCanPlayThroughCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onDurationChange?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onDurationChangeCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onEmptied?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onEmptiedCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onEncrypted?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onEncryptedCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onEnded?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onEndedCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onLoadedData?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onLoadedDataCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onLoadedMetadata?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onLoadedMetadataCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onLoadStart?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onLoadStartCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onPause?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onPauseCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onPlay?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onPlayCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onPlaying?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onPlayingCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onProgress?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onProgressCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onRateChange?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onRateChangeCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onResize?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onResizeCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onSeeked?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onSeekedCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onSeeking?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onSeekingCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onStalled?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onStalledCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onSuspend?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onSuspendCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onTimeUpdate?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onTimeUpdateCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onVolumeChange?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onVolumeChangeCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onWaiting?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onWaitingCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onAuxClick?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onAuxClickCapture?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onClickCapture?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onContextMenu?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onContextMenuCapture?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onDoubleClick?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onDoubleClickCapture?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onDrag?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragCapture?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragEnd?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragEndCapture?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragEnter?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragEnterCapture?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragExit?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragExitCapture?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragLeave?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragLeaveCapture?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragOver?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragOverCapture?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragStart?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragStartCapture?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDrop?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDropCapture?: import("react").DragEventHandler<HTMLElement> | undefined;
    onMouseDownCapture?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onMouseEnter?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onMouseMove?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onMouseMoveCapture?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onMouseOut?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onMouseOutCapture?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onMouseOverCapture?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onMouseUpCapture?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onSelect?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onSelectCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onTouchCancel?: import("react").TouchEventHandler<HTMLElement> | undefined;
    onTouchCancelCapture?: import("react").TouchEventHandler<HTMLElement> | undefined;
    onTouchEnd?: import("react").TouchEventHandler<HTMLElement> | undefined;
    onTouchEndCapture?: import("react").TouchEventHandler<HTMLElement> | undefined;
    onTouchMove?: import("react").TouchEventHandler<HTMLElement> | undefined;
    onTouchMoveCapture?: import("react").TouchEventHandler<HTMLElement> | undefined;
    onTouchStart?: import("react").TouchEventHandler<HTMLElement> | undefined;
    onTouchStartCapture?: import("react").TouchEventHandler<HTMLElement> | undefined;
    onPointerDown?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerDownCapture?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerMove?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerMoveCapture?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerUp?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerUpCapture?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerCancel?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerCancelCapture?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerEnter?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerLeave?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerOver?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerOverCapture?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerOut?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerOutCapture?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onGotPointerCapture?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onGotPointerCaptureCapture?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onLostPointerCapture?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onLostPointerCaptureCapture?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onScroll?: import("react").UIEventHandler<HTMLElement> | undefined;
    onScrollCapture?: import("react").UIEventHandler<HTMLElement> | undefined;
    onWheel?: import("react").WheelEventHandler<HTMLElement> | undefined;
    onWheelCapture?: import("react").WheelEventHandler<HTMLElement> | undefined;
    onAnimationStart?: import("react").AnimationEventHandler<HTMLElement> | undefined;
    onAnimationStartCapture?: import("react").AnimationEventHandler<HTMLElement> | undefined;
    onAnimationEnd?: import("react").AnimationEventHandler<HTMLElement> | undefined;
    onAnimationEndCapture?: import("react").AnimationEventHandler<HTMLElement> | undefined;
    onAnimationIteration?: import("react").AnimationEventHandler<HTMLElement> | undefined;
    onAnimationIterationCapture?: import("react").AnimationEventHandler<HTMLElement> | undefined;
    onTransitionEnd?: import("react").TransitionEventHandler<HTMLElement> | undefined;
    onTransitionEndCapture?: import("react").TransitionEventHandler<HTMLElement> | undefined;
};
export interface UseMenuOptionOptions {
    value?: string;
    isChecked?: boolean;
    type?: "radio" | "checkbox";
    children?: React.ReactNode;
}
export interface UseMenuOptionProps extends Omit<UseMenuItemProps, "type">, UseMenuOptionOptions {
}
export declare function useMenuOption(props?: UseMenuOptionProps, ref?: React.Ref<any>): {
    role: string;
    "aria-checked": boolean | "mixed" | "false" | "true" | undefined;
    type: any;
    id: string;
    tabIndex: number;
    ref: (node: any) => void;
    "aria-disabled": boolean | undefined;
    disabled: boolean | undefined;
    onClick: (event: import("react").MouseEvent<HTMLElement, MouseEvent>) => void;
    onMouseDown: import("react").MouseEventHandler<HTMLElement> | undefined;
    onMouseUp: import("react").MouseEventHandler<HTMLElement> | undefined;
    onKeyUp: import("react").KeyboardEventHandler<HTMLElement> | undefined;
    onKeyDown: import("react").KeyboardEventHandler<HTMLElement> | undefined;
    onMouseOver: import("react").MouseEventHandler<HTMLElement> | undefined;
    onMouseLeave: import("react").MouseEventHandler<HTMLElement> | undefined;
    defaultChecked?: boolean | undefined;
    defaultValue?: string | number | readonly string[] | undefined;
    suppressContentEditableWarning?: boolean | undefined;
    suppressHydrationWarning?: boolean | undefined;
    accessKey?: string | undefined;
    autoCapitalize?: (string & {}) | "none" | "off" | "on" | "sentences" | "words" | "characters" | undefined;
    autoFocus?: boolean | undefined;
    className?: string | undefined;
    contentEditable?: "inherit" | (boolean | "false" | "true") | "plaintext-only" | undefined;
    contextMenu?: string | undefined;
    dir?: string | undefined;
    draggable?: (boolean | "false" | "true") | undefined;
    enterKeyHint?: "search" | "done" | "enter" | "go" | "next" | "previous" | "send" | undefined;
    hidden?: boolean | undefined;
    lang?: string | undefined;
    nonce?: string | undefined;
    slot?: string | undefined;
    spellCheck?: (boolean | "false" | "true") | undefined;
    style?: import("react").CSSProperties | undefined;
    title?: string | undefined;
    translate?: "yes" | "no" | undefined;
    radioGroup?: string | undefined;
    about?: string | undefined;
    content?: string | undefined;
    datatype?: string | undefined;
    inlist?: any;
    prefix?: string | undefined;
    property?: string | undefined;
    rel?: string | undefined;
    resource?: string | undefined;
    rev?: string | undefined;
    typeof?: string | undefined;
    vocab?: string | undefined;
    autoCorrect?: string | undefined;
    autoSave?: string | undefined;
    color?: string | undefined;
    itemProp?: string | undefined;
    itemScope?: boolean | undefined;
    itemType?: string | undefined;
    itemID?: string | undefined;
    itemRef?: string | undefined;
    results?: number | undefined;
    security?: string | undefined;
    unselectable?: "off" | "on" | undefined;
    inputMode?: "search" | "text" | "none" | "email" | "tel" | "url" | "numeric" | "decimal" | undefined;
    is?: string | undefined;
    "aria-activedescendant"?: string | undefined;
    "aria-atomic"?: (boolean | "false" | "true") | undefined;
    "aria-autocomplete"?: "both" | "none" | "inline" | "list" | undefined;
    "aria-braillelabel"?: string | undefined;
    "aria-brailleroledescription"?: string | undefined;
    "aria-busy"?: (boolean | "false" | "true") | undefined;
    "aria-colcount"?: number | undefined;
    "aria-colindex"?: number | undefined;
    "aria-colindextext"?: string | undefined;
    "aria-colspan"?: number | undefined;
    "aria-controls"?: string | undefined;
    "aria-current"?: boolean | "time" | "page" | "false" | "true" | "step" | "date" | "location" | undefined;
    "aria-describedby"?: string | undefined;
    "aria-description"?: string | undefined;
    "aria-details"?: string | undefined;
    "aria-dropeffect"?: "link" | "none" | "copy" | "move" | "execute" | "popup" | undefined;
    "aria-errormessage"?: string | undefined;
    "aria-expanded"?: (boolean | "false" | "true") | undefined;
    "aria-flowto"?: string | undefined;
    "aria-grabbed"?: (boolean | "false" | "true") | undefined;
    "aria-haspopup"?: boolean | "dialog" | "menu" | "grid" | "listbox" | "false" | "true" | "tree" | undefined;
    "aria-hidden"?: (boolean | "false" | "true") | undefined;
    "aria-invalid"?: boolean | "false" | "true" | "grammar" | "spelling" | undefined;
    "aria-keyshortcuts"?: string | undefined;
    "aria-label"?: string | undefined;
    "aria-labelledby"?: string | undefined;
    "aria-level"?: number | undefined;
    "aria-live"?: "off" | "assertive" | "polite" | undefined;
    "aria-modal"?: (boolean | "false" | "true") | undefined;
    "aria-multiline"?: (boolean | "false" | "true") | undefined;
    "aria-multiselectable"?: (boolean | "false" | "true") | undefined;
    "aria-orientation"?: "horizontal" | "vertical" | undefined;
    "aria-owns"?: string | undefined;
    "aria-placeholder"?: string | undefined;
    "aria-posinset"?: number | undefined;
    "aria-pressed"?: boolean | "mixed" | "false" | "true" | undefined;
    "aria-readonly"?: (boolean | "false" | "true") | undefined;
    "aria-relevant"?: "all" | "text" | "additions" | "additions removals" | "additions text" | "removals" | "removals additions" | "removals text" | "text additions" | "text removals" | undefined;
    "aria-required"?: (boolean | "false" | "true") | undefined;
    "aria-roledescription"?: string | undefined;
    "aria-rowcount"?: number | undefined;
    "aria-rowindex"?: number | undefined;
    "aria-rowindextext"?: string | undefined;
    "aria-rowspan"?: number | undefined;
    "aria-selected"?: (boolean | "false" | "true") | undefined;
    "aria-setsize"?: number | undefined;
    "aria-sort"?: "none" | "ascending" | "descending" | "other" | undefined;
    "aria-valuemax"?: number | undefined;
    "aria-valuemin"?: number | undefined;
    "aria-valuenow"?: number | undefined;
    "aria-valuetext"?: string | undefined;
    children?: import("react").ReactNode;
    dangerouslySetInnerHTML?: {
        __html: string | TrustedHTML;
    } | undefined;
    onCopy?: import("react").ClipboardEventHandler<HTMLElement> | undefined;
    onCopyCapture?: import("react").ClipboardEventHandler<HTMLElement> | undefined;
    onCut?: import("react").ClipboardEventHandler<HTMLElement> | undefined;
    onCutCapture?: import("react").ClipboardEventHandler<HTMLElement> | undefined;
    onPaste?: import("react").ClipboardEventHandler<HTMLElement> | undefined;
    onPasteCapture?: import("react").ClipboardEventHandler<HTMLElement> | undefined;
    onCompositionEnd?: import("react").CompositionEventHandler<HTMLElement> | undefined;
    onCompositionEndCapture?: import("react").CompositionEventHandler<HTMLElement> | undefined;
    onCompositionStart?: import("react").CompositionEventHandler<HTMLElement> | undefined;
    onCompositionStartCapture?: import("react").CompositionEventHandler<HTMLElement> | undefined;
    onCompositionUpdate?: import("react").CompositionEventHandler<HTMLElement> | undefined;
    onCompositionUpdateCapture?: import("react").CompositionEventHandler<HTMLElement> | undefined;
    onFocus?: import("react").FocusEventHandler<HTMLElement> | undefined;
    onFocusCapture?: import("react").FocusEventHandler<HTMLElement> | undefined;
    onBlur?: import("react").FocusEventHandler<HTMLElement> | undefined;
    onBlurCapture?: import("react").FocusEventHandler<HTMLElement> | undefined;
    onChange?: import("react").FormEventHandler<HTMLElement> | undefined;
    onChangeCapture?: import("react").FormEventHandler<HTMLElement> | undefined;
    onBeforeInput?: import("react").FormEventHandler<HTMLElement> | undefined;
    onBeforeInputCapture?: import("react").FormEventHandler<HTMLElement> | undefined;
    onInput?: import("react").FormEventHandler<HTMLElement> | undefined;
    onInputCapture?: import("react").FormEventHandler<HTMLElement> | undefined;
    onReset?: import("react").FormEventHandler<HTMLElement> | undefined;
    onResetCapture?: import("react").FormEventHandler<HTMLElement> | undefined;
    onSubmit?: import("react").FormEventHandler<HTMLElement> | undefined;
    onSubmitCapture?: import("react").FormEventHandler<HTMLElement> | undefined;
    onInvalid?: import("react").FormEventHandler<HTMLElement> | undefined;
    onInvalidCapture?: import("react").FormEventHandler<HTMLElement> | undefined;
    onLoad?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onLoadCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onError?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onErrorCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onKeyDownCapture?: import("react").KeyboardEventHandler<HTMLElement> | undefined;
    onKeyPress?: import("react").KeyboardEventHandler<HTMLElement> | undefined;
    onKeyPressCapture?: import("react").KeyboardEventHandler<HTMLElement> | undefined;
    onKeyUpCapture?: import("react").KeyboardEventHandler<HTMLElement> | undefined;
    onAbort?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onAbortCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onCanPlay?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onCanPlayCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onCanPlayThrough?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onCanPlayThroughCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onDurationChange?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onDurationChangeCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onEmptied?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onEmptiedCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onEncrypted?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onEncryptedCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onEnded?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onEndedCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onLoadedData?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onLoadedDataCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onLoadedMetadata?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onLoadedMetadataCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onLoadStart?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onLoadStartCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onPause?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onPauseCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onPlay?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onPlayCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onPlaying?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onPlayingCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onProgress?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onProgressCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onRateChange?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onRateChangeCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onResize?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onResizeCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onSeeked?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onSeekedCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onSeeking?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onSeekingCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onStalled?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onStalledCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onSuspend?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onSuspendCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onTimeUpdate?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onTimeUpdateCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onVolumeChange?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onVolumeChangeCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onWaiting?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onWaitingCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onAuxClick?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onAuxClickCapture?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onClickCapture?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onContextMenu?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onContextMenuCapture?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onDoubleClick?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onDoubleClickCapture?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onDrag?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragCapture?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragEnd?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragEndCapture?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragEnter?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragEnterCapture?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragExit?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragExitCapture?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragLeave?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragLeaveCapture?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragOver?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragOverCapture?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragStart?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragStartCapture?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDrop?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDropCapture?: import("react").DragEventHandler<HTMLElement> | undefined;
    onMouseDownCapture?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onMouseEnter?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onMouseMove?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onMouseMoveCapture?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onMouseOut?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onMouseOutCapture?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onMouseOverCapture?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onMouseUpCapture?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onSelect?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onSelectCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onTouchCancel?: import("react").TouchEventHandler<HTMLElement> | undefined;
    onTouchCancelCapture?: import("react").TouchEventHandler<HTMLElement> | undefined;
    onTouchEnd?: import("react").TouchEventHandler<HTMLElement> | undefined;
    onTouchEndCapture?: import("react").TouchEventHandler<HTMLElement> | undefined;
    onTouchMove?: import("react").TouchEventHandler<HTMLElement> | undefined;
    onTouchMoveCapture?: import("react").TouchEventHandler<HTMLElement> | undefined;
    onTouchStart?: import("react").TouchEventHandler<HTMLElement> | undefined;
    onTouchStartCapture?: import("react").TouchEventHandler<HTMLElement> | undefined;
    onPointerDown?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerDownCapture?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerMove?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerMoveCapture?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerUp?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerUpCapture?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerCancel?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerCancelCapture?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerEnter?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerLeave?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerOver?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerOverCapture?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerOut?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerOutCapture?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onGotPointerCapture?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onGotPointerCaptureCapture?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onLostPointerCapture?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onLostPointerCaptureCapture?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onScroll?: import("react").UIEventHandler<HTMLElement> | undefined;
    onScrollCapture?: import("react").UIEventHandler<HTMLElement> | undefined;
    onWheel?: import("react").WheelEventHandler<HTMLElement> | undefined;
    onWheelCapture?: import("react").WheelEventHandler<HTMLElement> | undefined;
    onAnimationStart?: import("react").AnimationEventHandler<HTMLElement> | undefined;
    onAnimationStartCapture?: import("react").AnimationEventHandler<HTMLElement> | undefined;
    onAnimationEnd?: import("react").AnimationEventHandler<HTMLElement> | undefined;
    onAnimationEndCapture?: import("react").AnimationEventHandler<HTMLElement> | undefined;
    onAnimationIteration?: import("react").AnimationEventHandler<HTMLElement> | undefined;
    onAnimationIterationCapture?: import("react").AnimationEventHandler<HTMLElement> | undefined;
    onTransitionEnd?: import("react").TransitionEventHandler<HTMLElement> | undefined;
    onTransitionEndCapture?: import("react").TransitionEventHandler<HTMLElement> | undefined;
} | {
    role: string;
    "aria-checked": boolean | "mixed" | "false" | "true" | undefined;
    type: any;
    id: string;
    tabIndex: number;
    ref: (node: any) => void;
    "data-active": boolean | "false" | "true";
    "aria-disabled": "true" | undefined;
    onClick: (event: import("react").MouseEvent<HTMLElement, MouseEvent>) => void;
    onMouseDown: (event: import("react").MouseEvent<HTMLElement, MouseEvent>) => void;
    onMouseUp: (event: import("react").MouseEvent<HTMLElement, MouseEvent>) => void;
    onKeyUp: (event: import("react").KeyboardEvent<HTMLElement>) => void;
    onKeyDown: (event: import("react").KeyboardEvent<HTMLElement>) => void;
    onMouseOver: (event: import("react").MouseEvent<HTMLElement, MouseEvent>) => void;
    onMouseLeave: (event: import("react").MouseEvent<HTMLElement, MouseEvent>) => void;
    defaultChecked?: boolean | undefined;
    defaultValue?: string | number | readonly string[] | undefined;
    suppressContentEditableWarning?: boolean | undefined;
    suppressHydrationWarning?: boolean | undefined;
    accessKey?: string | undefined;
    autoCapitalize?: (string & {}) | "none" | "off" | "on" | "sentences" | "words" | "characters" | undefined;
    autoFocus?: boolean | undefined;
    className?: string | undefined;
    contentEditable?: "inherit" | (boolean | "false" | "true") | "plaintext-only" | undefined;
    contextMenu?: string | undefined;
    dir?: string | undefined;
    draggable?: (boolean | "false" | "true") | undefined;
    enterKeyHint?: "search" | "done" | "enter" | "go" | "next" | "previous" | "send" | undefined;
    hidden?: boolean | undefined;
    lang?: string | undefined;
    nonce?: string | undefined;
    slot?: string | undefined;
    spellCheck?: (boolean | "false" | "true") | undefined;
    style?: import("react").CSSProperties | undefined;
    title?: string | undefined;
    translate?: "yes" | "no" | undefined;
    radioGroup?: string | undefined;
    about?: string | undefined;
    content?: string | undefined;
    datatype?: string | undefined;
    inlist?: any;
    prefix?: string | undefined;
    property?: string | undefined;
    rel?: string | undefined;
    resource?: string | undefined;
    rev?: string | undefined;
    typeof?: string | undefined;
    vocab?: string | undefined;
    autoCorrect?: string | undefined;
    autoSave?: string | undefined;
    color?: string | undefined;
    itemProp?: string | undefined;
    itemScope?: boolean | undefined;
    itemType?: string | undefined;
    itemID?: string | undefined;
    itemRef?: string | undefined;
    results?: number | undefined;
    security?: string | undefined;
    unselectable?: "off" | "on" | undefined;
    inputMode?: "search" | "text" | "none" | "email" | "tel" | "url" | "numeric" | "decimal" | undefined;
    is?: string | undefined;
    "aria-activedescendant"?: string | undefined;
    "aria-atomic"?: (boolean | "false" | "true") | undefined;
    "aria-autocomplete"?: "both" | "none" | "inline" | "list" | undefined;
    "aria-braillelabel"?: string | undefined;
    "aria-brailleroledescription"?: string | undefined;
    "aria-busy"?: (boolean | "false" | "true") | undefined;
    "aria-colcount"?: number | undefined;
    "aria-colindex"?: number | undefined;
    "aria-colindextext"?: string | undefined;
    "aria-colspan"?: number | undefined;
    "aria-controls"?: string | undefined;
    "aria-current"?: boolean | "time" | "page" | "false" | "true" | "step" | "date" | "location" | undefined;
    "aria-describedby"?: string | undefined;
    "aria-description"?: string | undefined;
    "aria-details"?: string | undefined;
    "aria-dropeffect"?: "link" | "none" | "copy" | "move" | "execute" | "popup" | undefined;
    "aria-errormessage"?: string | undefined;
    "aria-expanded"?: (boolean | "false" | "true") | undefined;
    "aria-flowto"?: string | undefined;
    "aria-grabbed"?: (boolean | "false" | "true") | undefined;
    "aria-haspopup"?: boolean | "dialog" | "menu" | "grid" | "listbox" | "false" | "true" | "tree" | undefined;
    "aria-hidden"?: (boolean | "false" | "true") | undefined;
    "aria-invalid"?: boolean | "false" | "true" | "grammar" | "spelling" | undefined;
    "aria-keyshortcuts"?: string | undefined;
    "aria-label"?: string | undefined;
    "aria-labelledby"?: string | undefined;
    "aria-level"?: number | undefined;
    "aria-live"?: "off" | "assertive" | "polite" | undefined;
    "aria-modal"?: (boolean | "false" | "true") | undefined;
    "aria-multiline"?: (boolean | "false" | "true") | undefined;
    "aria-multiselectable"?: (boolean | "false" | "true") | undefined;
    "aria-orientation"?: "horizontal" | "vertical" | undefined;
    "aria-owns"?: string | undefined;
    "aria-placeholder"?: string | undefined;
    "aria-posinset"?: number | undefined;
    "aria-pressed"?: boolean | "mixed" | "false" | "true" | undefined;
    "aria-readonly"?: (boolean | "false" | "true") | undefined;
    "aria-relevant"?: "all" | "text" | "additions" | "additions removals" | "additions text" | "removals" | "removals additions" | "removals text" | "text additions" | "text removals" | undefined;
    "aria-required"?: (boolean | "false" | "true") | undefined;
    "aria-roledescription"?: string | undefined;
    "aria-rowcount"?: number | undefined;
    "aria-rowindex"?: number | undefined;
    "aria-rowindextext"?: string | undefined;
    "aria-rowspan"?: number | undefined;
    "aria-selected"?: (boolean | "false" | "true") | undefined;
    "aria-setsize"?: number | undefined;
    "aria-sort"?: "none" | "ascending" | "descending" | "other" | undefined;
    "aria-valuemax"?: number | undefined;
    "aria-valuemin"?: number | undefined;
    "aria-valuenow"?: number | undefined;
    "aria-valuetext"?: string | undefined;
    children?: import("react").ReactNode;
    dangerouslySetInnerHTML?: {
        __html: string | TrustedHTML;
    } | undefined;
    onCopy?: import("react").ClipboardEventHandler<HTMLElement> | undefined;
    onCopyCapture?: import("react").ClipboardEventHandler<HTMLElement> | undefined;
    onCut?: import("react").ClipboardEventHandler<HTMLElement> | undefined;
    onCutCapture?: import("react").ClipboardEventHandler<HTMLElement> | undefined;
    onPaste?: import("react").ClipboardEventHandler<HTMLElement> | undefined;
    onPasteCapture?: import("react").ClipboardEventHandler<HTMLElement> | undefined;
    onCompositionEnd?: import("react").CompositionEventHandler<HTMLElement> | undefined;
    onCompositionEndCapture?: import("react").CompositionEventHandler<HTMLElement> | undefined;
    onCompositionStart?: import("react").CompositionEventHandler<HTMLElement> | undefined;
    onCompositionStartCapture?: import("react").CompositionEventHandler<HTMLElement> | undefined;
    onCompositionUpdate?: import("react").CompositionEventHandler<HTMLElement> | undefined;
    onCompositionUpdateCapture?: import("react").CompositionEventHandler<HTMLElement> | undefined;
    onFocus?: import("react").FocusEventHandler<HTMLElement> | undefined;
    onFocusCapture?: import("react").FocusEventHandler<HTMLElement> | undefined;
    onBlur?: import("react").FocusEventHandler<HTMLElement> | undefined;
    onBlurCapture?: import("react").FocusEventHandler<HTMLElement> | undefined;
    onChange?: import("react").FormEventHandler<HTMLElement> | undefined;
    onChangeCapture?: import("react").FormEventHandler<HTMLElement> | undefined;
    onBeforeInput?: import("react").FormEventHandler<HTMLElement> | undefined;
    onBeforeInputCapture?: import("react").FormEventHandler<HTMLElement> | undefined;
    onInput?: import("react").FormEventHandler<HTMLElement> | undefined;
    onInputCapture?: import("react").FormEventHandler<HTMLElement> | undefined;
    onReset?: import("react").FormEventHandler<HTMLElement> | undefined;
    onResetCapture?: import("react").FormEventHandler<HTMLElement> | undefined;
    onSubmit?: import("react").FormEventHandler<HTMLElement> | undefined;
    onSubmitCapture?: import("react").FormEventHandler<HTMLElement> | undefined;
    onInvalid?: import("react").FormEventHandler<HTMLElement> | undefined;
    onInvalidCapture?: import("react").FormEventHandler<HTMLElement> | undefined;
    onLoad?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onLoadCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onError?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onErrorCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onKeyDownCapture?: import("react").KeyboardEventHandler<HTMLElement> | undefined;
    onKeyPress?: import("react").KeyboardEventHandler<HTMLElement> | undefined;
    onKeyPressCapture?: import("react").KeyboardEventHandler<HTMLElement> | undefined;
    onKeyUpCapture?: import("react").KeyboardEventHandler<HTMLElement> | undefined;
    onAbort?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onAbortCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onCanPlay?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onCanPlayCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onCanPlayThrough?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onCanPlayThroughCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onDurationChange?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onDurationChangeCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onEmptied?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onEmptiedCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onEncrypted?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onEncryptedCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onEnded?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onEndedCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onLoadedData?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onLoadedDataCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onLoadedMetadata?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onLoadedMetadataCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onLoadStart?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onLoadStartCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onPause?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onPauseCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onPlay?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onPlayCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onPlaying?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onPlayingCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onProgress?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onProgressCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onRateChange?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onRateChangeCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onResize?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onResizeCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onSeeked?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onSeekedCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onSeeking?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onSeekingCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onStalled?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onStalledCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onSuspend?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onSuspendCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onTimeUpdate?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onTimeUpdateCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onVolumeChange?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onVolumeChangeCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onWaiting?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onWaitingCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onAuxClick?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onAuxClickCapture?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onClickCapture?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onContextMenu?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onContextMenuCapture?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onDoubleClick?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onDoubleClickCapture?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onDrag?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragCapture?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragEnd?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragEndCapture?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragEnter?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragEnterCapture?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragExit?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragExitCapture?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragLeave?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragLeaveCapture?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragOver?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragOverCapture?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragStart?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDragStartCapture?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDrop?: import("react").DragEventHandler<HTMLElement> | undefined;
    onDropCapture?: import("react").DragEventHandler<HTMLElement> | undefined;
    onMouseDownCapture?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onMouseEnter?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onMouseMove?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onMouseMoveCapture?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onMouseOut?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onMouseOutCapture?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onMouseOverCapture?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onMouseUpCapture?: import("react").MouseEventHandler<HTMLElement> | undefined;
    onSelect?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onSelectCapture?: import("react").ReactEventHandler<HTMLElement> | undefined;
    onTouchCancel?: import("react").TouchEventHandler<HTMLElement> | undefined;
    onTouchCancelCapture?: import("react").TouchEventHandler<HTMLElement> | undefined;
    onTouchEnd?: import("react").TouchEventHandler<HTMLElement> | undefined;
    onTouchEndCapture?: import("react").TouchEventHandler<HTMLElement> | undefined;
    onTouchMove?: import("react").TouchEventHandler<HTMLElement> | undefined;
    onTouchMoveCapture?: import("react").TouchEventHandler<HTMLElement> | undefined;
    onTouchStart?: import("react").TouchEventHandler<HTMLElement> | undefined;
    onTouchStartCapture?: import("react").TouchEventHandler<HTMLElement> | undefined;
    onPointerDown?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerDownCapture?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerMove?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerMoveCapture?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerUp?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerUpCapture?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerCancel?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerCancelCapture?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerEnter?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerLeave?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerOver?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerOverCapture?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerOut?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onPointerOutCapture?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onGotPointerCapture?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onGotPointerCaptureCapture?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onLostPointerCapture?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onLostPointerCaptureCapture?: import("react").PointerEventHandler<HTMLElement> | undefined;
    onScroll?: import("react").UIEventHandler<HTMLElement> | undefined;
    onScrollCapture?: import("react").UIEventHandler<HTMLElement> | undefined;
    onWheel?: import("react").WheelEventHandler<HTMLElement> | undefined;
    onWheelCapture?: import("react").WheelEventHandler<HTMLElement> | undefined;
    onAnimationStart?: import("react").AnimationEventHandler<HTMLElement> | undefined;
    onAnimationStartCapture?: import("react").AnimationEventHandler<HTMLElement> | undefined;
    onAnimationEnd?: import("react").AnimationEventHandler<HTMLElement> | undefined;
    onAnimationEndCapture?: import("react").AnimationEventHandler<HTMLElement> | undefined;
    onAnimationIteration?: import("react").AnimationEventHandler<HTMLElement> | undefined;
    onAnimationIterationCapture?: import("react").AnimationEventHandler<HTMLElement> | undefined;
    onTransitionEnd?: import("react").TransitionEventHandler<HTMLElement> | undefined;
    onTransitionEndCapture?: import("react").TransitionEventHandler<HTMLElement> | undefined;
};
export interface UseMenuOptionGroupProps {
    value?: string | string[];
    defaultValue?: string | string[];
    type?: "radio" | "checkbox";
    onChange?: (value: string | string[]) => void;
    children?: React.ReactNode;
}
export declare function useMenuOptionGroup(props?: UseMenuOptionGroupProps): {
    children: import("react").ReactElement<any, string | import("react").JSXElementConstructor<any>>[];
};
export declare function useMenuState(): {
    isOpen: boolean;
    onClose: () => void;
};
