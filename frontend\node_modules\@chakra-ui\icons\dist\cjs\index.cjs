'use strict';

var Copy = require('./Copy.cjs');
var Search = require('./Search.cjs');
var Search2 = require('./Search2.cjs');
var Moon = require('./Moon.cjs');
var Sun = require('./Sun.cjs');
var Add = require('./Add.cjs');
var SmallAdd = require('./SmallAdd.cjs');
var Settings = require('./Settings.cjs');
var CheckCircle = require('./CheckCircle.cjs');
var Lock = require('./Lock.cjs');
var Unlock = require('./Unlock.cjs');
var View = require('./View.cjs');
var ViewOff = require('./ViewOff.cjs');
var Download = require('./Download.cjs');
var Delete = require('./Delete.cjs');
var Repeat = require('./Repeat.cjs');
var React = require('./React.cjs');
var RepeatClock = require('./RepeatClock.cjs');
var Edit = require('./Edit.cjs');
var ChevronLeft = require('./ChevronLeft.cjs');
var ChevronRight = require('./ChevronRight.cjs');
var ChevronDown = require('./ChevronDown.cjs');
var ChevronUp = require('./ChevronUp.cjs');
var ArrowBack = require('./ArrowBack.cjs');
var ArrowForward = require('./ArrowForward.cjs');
var ArrowUp = require('./ArrowUp.cjs');
var ArrowUpDown = require('./ArrowUpDown.cjs');
var ArrowDown = require('./ArrowDown.cjs');
var ExternalLink = require('./ExternalLink.cjs');
var Link = require('./Link.cjs');
var PlusSquare = require('./PlusSquare.cjs');
var Calendar = require('./Calendar.cjs');
var Chat = require('./Chat.cjs');
var Time = require('./Time.cjs');
var ArrowRight = require('./ArrowRight.cjs');
var ArrowLeft = require('./ArrowLeft.cjs');
var AtSign = require('./AtSign.cjs');
var Attachment = require('./Attachment.cjs');
var UpDown = require('./UpDown.cjs');
var Star = require('./Star.cjs');
var Email = require('./Email.cjs');
var Phone = require('./Phone.cjs');
var DragHandle = require('./DragHandle.cjs');
var Spinner = require('./Spinner.cjs');
var Close = require('./Close.cjs');
var SmallClose = require('./SmallClose.cjs');
var NotAllowed = require('./NotAllowed.cjs');
var TriangleDown = require('./TriangleDown.cjs');
var TriangleUp = require('./TriangleUp.cjs');
var InfoOutline = require('./InfoOutline.cjs');
var Bell = require('./Bell.cjs');
var Info = require('./Info.cjs');
var Question = require('./Question.cjs');
var QuestionOutline = require('./QuestionOutline.cjs');
var Warning = require('./Warning.cjs');
var WarningTwo = require('./WarningTwo.cjs');
var Check = require('./Check.cjs');
var Minus = require('./Minus.cjs');
var Hamburger = require('./Hamburger.cjs');
var react = require('@chakra-ui/react');



exports.CopyIcon = Copy.CopyIcon;
exports.SearchIcon = Search.SearchIcon;
exports.Search2Icon = Search2.Search2Icon;
exports.MoonIcon = Moon.MoonIcon;
exports.SunIcon = Sun.SunIcon;
exports.AddIcon = Add.AddIcon;
exports.SmallAddIcon = SmallAdd.SmallAddIcon;
exports.SettingsIcon = Settings.SettingsIcon;
exports.CheckCircleIcon = CheckCircle.CheckCircleIcon;
exports.LockIcon = Lock.LockIcon;
exports.UnlockIcon = Unlock.UnlockIcon;
exports.ViewIcon = View.ViewIcon;
exports.ViewOffIcon = ViewOff.ViewOffIcon;
exports.DownloadIcon = Download.DownloadIcon;
exports.DeleteIcon = Delete.DeleteIcon;
exports.RepeatIcon = Repeat.RepeatIcon;
exports.ReactIcon = React.ReactIcon;
exports.RepeatClockIcon = RepeatClock.RepeatClockIcon;
exports.EditIcon = Edit.EditIcon;
exports.ChevronLeftIcon = ChevronLeft.ChevronLeftIcon;
exports.ChevronRightIcon = ChevronRight.ChevronRightIcon;
exports.ChevronDownIcon = ChevronDown.ChevronDownIcon;
exports.ChevronUpIcon = ChevronUp.ChevronUpIcon;
exports.ArrowBackIcon = ArrowBack.ArrowBackIcon;
exports.ArrowForwardIcon = ArrowForward.ArrowForwardIcon;
exports.ArrowUpIcon = ArrowUp.ArrowUpIcon;
exports.ArrowUpDownIcon = ArrowUpDown.ArrowUpDownIcon;
exports.ArrowDownIcon = ArrowDown.ArrowDownIcon;
exports.ExternalLinkIcon = ExternalLink.ExternalLinkIcon;
exports.LinkIcon = Link.LinkIcon;
exports.PlusSquareIcon = PlusSquare.PlusSquareIcon;
exports.CalendarIcon = Calendar.CalendarIcon;
exports.ChatIcon = Chat.ChatIcon;
exports.TimeIcon = Time.TimeIcon;
exports.ArrowRightIcon = ArrowRight.ArrowRightIcon;
exports.ArrowLeftIcon = ArrowLeft.ArrowLeftIcon;
exports.AtSignIcon = AtSign.AtSignIcon;
exports.AttachmentIcon = Attachment.AttachmentIcon;
exports.UpDownIcon = UpDown.UpDownIcon;
exports.StarIcon = Star.StarIcon;
exports.EmailIcon = Email.EmailIcon;
exports.PhoneIcon = Phone.PhoneIcon;
exports.DragHandleIcon = DragHandle.DragHandleIcon;
exports.SpinnerIcon = Spinner.SpinnerIcon;
exports.CloseIcon = Close.CloseIcon;
exports.SmallCloseIcon = SmallClose.SmallCloseIcon;
exports.NotAllowedIcon = NotAllowed.NotAllowedIcon;
exports.TriangleDownIcon = TriangleDown.TriangleDownIcon;
exports.TriangleUpIcon = TriangleUp.TriangleUpIcon;
exports.InfoOutlineIcon = InfoOutline.InfoOutlineIcon;
exports.BellIcon = Bell.BellIcon;
exports.InfoIcon = Info.InfoIcon;
exports.QuestionIcon = Question.QuestionIcon;
exports.QuestionOutlineIcon = QuestionOutline.QuestionOutlineIcon;
exports.WarningIcon = Warning.WarningIcon;
exports.WarningTwoIcon = WarningTwo.WarningTwoIcon;
exports.CheckIcon = Check.CheckIcon;
exports.MinusIcon = Minus.MinusIcon;
exports.HamburgerIcon = Hamburger.HamburgerIcon;
Object.keys(react).forEach(function (k) {
	if (k !== 'default' && !Object.prototype.hasOwnProperty.call(exports, k)) Object.defineProperty(exports, k, {
		enumerable: true,
		get: function () { return react[k]; }
	});
});
