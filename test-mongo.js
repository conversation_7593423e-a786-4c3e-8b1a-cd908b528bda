const mongoose = require('mongoose');

const uri = 'mongodb+srv://ziqra:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0';

console.log('Connecting to MongoDB...');

mongoose.connect(uri)
  .then(() => {
    console.log('Connected to MongoDB successfully!');
    console.log('Connection state:', mongoose.connection.readyState);
    console.log('Database name:', mongoose.connection.name);
    console.log('Host:', mongoose.connection.host);
    process.exit(0);
  })
  .catch(err => {
    console.error('Failed to connect to MongoDB:', err.message);
    process.exit(1);
  });
