import React, { useRef, useState, useEffect } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import {
  Box,
  Flex,
  Text,
  Image,
  HStack,
  VStack,
  IconButton,
  useColorModeValue,
  Skeleton,
  Badge,
  Tooltip,
  Heading,
  Button,
} from '@chakra-ui/react';
import { ChevronLeftIcon, ChevronRightIcon, StarIcon } from '@chakra-ui/icons';
import { formatCurrency, formatPercentage } from '../../utils/format';
import websocketService, { WebSocketEventType } from '../../services/websocketService';

// Helper function to get a consistent color for a symbol
const getColorForSymbol = (symbol: string) => {
  const colors = ['4a90e2', '50e3c2', 'b8e986', 'f8e71c', 'f5a623', 'e84118', '8e44ad'];
  const colorIndex = symbol.charCodeAt(0) % colors.length;
  return colors[colorIndex];
};

interface Coin {
  _id: string;
  name: string;
  symbol: string;
  currentPrice: number;
  priceChangePercentage24h: number;
  image?: string;
  rank?: number;
}

interface CoinCarouselProps {
  coins: Coin[];
  isLoading: boolean;
  title?: string;
  onAddToWatchlist?: (coinId: string) => void;
}

const CoinCarousel: React.FC<CoinCarouselProps> = ({
  coins,
  isLoading,
  title = 'Top Cryptocurrencies',
  onAddToWatchlist,
}) => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(true);
  const [liveData, setLiveData] = useState<Record<string, any>>({});

  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const hoverBg = useColorModeValue('gray.50', 'gray.700');

  // Subscribe to WebSocket price updates
  useEffect(() => {
    if (coins.length > 0) {
      // Get coin IDs
      const coinIds = coins.map(coin => coin._id);

      // Subscribe to price updates
      websocketService.subscribeToCoins(coinIds);

      // Listen for price updates
      const handlePriceUpdate = (data: Record<string, any>) => {
        setLiveData(prevData => ({
          ...prevData,
          ...data,
        }));
      };

      websocketService.addEventListener(WebSocketEventType.PRICE_UPDATE, handlePriceUpdate);

      // Connect if not already connected
      if (!websocketService.isSocketConnected()) {
        websocketService.connect();
      }

      // Cleanup
      return () => {
        websocketService.removeEventListener(WebSocketEventType.PRICE_UPDATE, handlePriceUpdate);
      };
    }
  }, [coins]);

  // Check if can scroll left/right
  const checkScrollability = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft + clientWidth < scrollWidth);
    }
  };

  // Scroll left
  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      const scrollAmount = scrollContainerRef.current.clientWidth * 0.8;
      scrollContainerRef.current.scrollBy({ left: -scrollAmount, behavior: 'smooth' });
    }
  };

  // Scroll right
  const scrollRight = () => {
    if (scrollContainerRef.current) {
      const scrollAmount = scrollContainerRef.current.clientWidth * 0.8;
      scrollContainerRef.current.scrollBy({ left: scrollAmount, behavior: 'smooth' });
    }
  };

  // Add scroll event listener
  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', checkScrollability);
      // Initial check
      checkScrollability();

      // Check on window resize
      window.addEventListener('resize', checkScrollability);

      return () => {
        scrollContainer.removeEventListener('scroll', checkScrollability);
        window.removeEventListener('resize', checkScrollability);
      };
    }
  }, []);

  // Get live price for a coin
  const getLivePrice = (coin: Coin) => {
    if (liveData[coin._id]?.price) {
      return liveData[coin._id].price;
    }
    return coin.currentPrice;
  };

  // Get live price change for a coin
  const getLivePriceChange = (coin: Coin) => {
    if (liveData[coin._id]?.change24h) {
      return liveData[coin._id].change24h;
    }
    return coin.priceChangePercentage24h;
  };

  // Check if price has changed
  const hasPriceChanged = (coin: Coin) => {
    return liveData[coin._id]?.price !== undefined;
  };

  return (
    <Box
      position="relative"
      mb={8}
      pb={2}
    >
      <Flex justify="space-between" align="center" mb={4}>
        <Heading size="md">{title}</Heading>
        <Button
          as={RouterLink}
          to="/market"
          size="sm"
          variant="ghost"
          colorScheme="brand"
        >
          View Details
        </Button>
      </Flex>

      {/* Scroll buttons */}
      {canScrollLeft && (
        <IconButton
          aria-label="Scroll left"
          icon={<ChevronLeftIcon />}
          onClick={scrollLeft}
          position="absolute"
          left={-4}
          top="50%"
          transform="translateY(-50%)"
          zIndex={2}
          rounded="full"
          shadow="md"
          colorScheme="brand"
        />
      )}

      {canScrollRight && (
        <IconButton
          aria-label="Scroll right"
          icon={<ChevronRightIcon />}
          onClick={scrollRight}
          position="absolute"
          right={-4}
          top="50%"
          transform="translateY(-50%)"
          zIndex={2}
          rounded="full"
          shadow="md"
          colorScheme="brand"
        />
      )}

      {/* Scrollable container */}
      <Box
        ref={scrollContainerRef}
        overflowX="auto"
        css={{
          '&::-webkit-scrollbar': {
            display: 'none',
          },
          scrollbarWidth: 'none',
        }}
        px={1}
      >
        <HStack spacing={4} pb={2} width="100%">
          {isLoading
            ? Array.from({ length: 10 }).map((_, index) => (
                <Box
                  key={index}
                  minW="240px"
                  maxW="280px"
                  height="180px"
                  p={5}
                  borderWidth="1px"
                  borderRadius="lg"
                  bg={bgColor}
                  borderColor={borderColor}
                >
                  <Skeleton height="24px" width="100px" mb={3} />
                  <Skeleton height="32px" mb={4} />
                  <Skeleton height="28px" width="120px" mb={3} />
                  <Skeleton height="20px" width="100%" />
                </Box>
              ))
            : coins.map((coin) => (
                <Box
                  key={coin._id}
                  as={RouterLink}
                  to={`/crypto/${coin._id}`}
                  minW="180px"
                  maxW="200px"
                  p={4}
                  borderWidth="1px"
                  borderRadius="lg"
                  bg={bgColor}
                  borderColor={borderColor}
                  _hover={{
                    bg: hoverBg,
                    transform: 'translateY(-2px)',
                    shadow: 'md',
                    borderColor: 'brand.300',
                  }}
                  transition="all 0.2s"
                  position="relative"
                  height="140px"
                >
                  {/* Watchlist button */}
                  {onAddToWatchlist && (
                    <IconButton
                      aria-label="Add to watchlist"
                      icon={<StarIcon />}
                      size="xs"
                      position="absolute"
                      top={2}
                      right={2}
                      variant="ghost"
                      colorScheme="yellow"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        onAddToWatchlist(coin._id);
                      }}
                    />
                  )}

                  <Flex align="center" mb={3}>
                    <Box position="relative" mr={2}>
                      <Image
                        src={coin.image || `https://cryptologos.cc/logos/${coin.name.toLowerCase()}-${coin.symbol.toLowerCase()}-logo.png`}
                        alt={coin.name}
                        boxSize="32px"
                        fallbackSrc={`https://via.placeholder.com/32/${getColorForSymbol(coin.symbol)}/ffffff?text=${coin.symbol.charAt(0)}`}
                        borderRadius="full"
                      />
                      {coin.rank && coin.rank <= 10 && (
                        <Badge
                          position="absolute"
                          top="-8px"
                          right="-8px"
                          borderRadius="full"
                          bg="yellow.500"
                          fontSize="xs"
                          p="1px"
                          minW="18px"
                          textAlign="center"
                        >
                          #{coin.rank}
                        </Badge>
                      )}
                    </Box>
                    <Box>
                      <Text fontWeight="bold" fontSize="md">{coin.symbol}</Text>
                      <Text fontSize="sm" color="gray.500" mt="0">{coin.name}</Text>
                    </Box>
                  </Flex>

                  <Flex direction="column">
                    <Text
                      fontWeight="bold"
                      fontSize="xl"
                      color={hasPriceChanged(coin) ? 'brand.500' : undefined}
                      transition="color 0.5s"
                    >
                      {formatCurrency(getLivePrice(coin))}
                    </Text>

                    <Flex align="center" mt={1}>
                      <Text
                        fontSize="sm"
                        fontWeight="bold"
                        color={getLivePriceChange(coin) >= 0 ? 'green.500' : 'red.500'}
                      >
                        {getLivePriceChange(coin) >= 0 ? '+' : ''}{formatPercentage(getLivePriceChange(coin))}
                      </Text>
                      <Text fontSize="xs" color="gray.500" ml={1}>
                        24h Change
                      </Text>
                    </Flex>
                  </Flex>
                </Box>
              ))}
        </HStack>
      </Box>
    </Box>
  );
};

export default CoinCarousel;
