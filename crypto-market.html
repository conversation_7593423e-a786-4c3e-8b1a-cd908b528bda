<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cryptocurrency Market</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h1, h2 {
            color: #333;
            text-align: center;
        }
        .section {
            margin-bottom: 40px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        .crypto-container {
            display: flex;
            overflow-x: auto;
            gap: 20px;
            padding: 10px 0;
        }
        .crypto-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            padding: 20px;
            min-width: 250px;
            transition: transform 0.3s ease;
        }
        .crypto-card:hover {
            transform: translateY(-5px);
        }
        .crypto-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .crypto-image {
            width: 40px;
            height: 40px;
            margin-right: 10px;
            border-radius: 50%;
        }
        .crypto-name {
            font-weight: bold;
            font-size: 18px;
            margin-bottom: 5px;
        }
        .crypto-symbol {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
            background-color: #f0f0f0;
            padding: 3px 8px;
            border-radius: 10px;
            display: inline-block;
        }
        .crypto-price {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .crypto-change {
            padding: 3px 8px;
            border-radius: 5px;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 15px;
        }
        .positive {
            background-color: #e6f7e6;
            color: #2e7d32;
        }
        .negative {
            background-color: #fde8e8;
            color: #c62828;
        }
        .crypto-details {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
            font-size: 14px;
        }
        .crypto-detail {
            text-align: center;
        }
        .detail-label {
            color: #666;
            margin-bottom: 5px;
        }
        .detail-value {
            font-weight: bold;
        }
        .crypto-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .crypto-table th, .crypto-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .crypto-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .crypto-table tr:hover {
            background-color: #f5f5f5;
        }
        .error-message {
            color: red;
            text-align: center;
            padding: 20px;
            background-color: #ffebee;
            border-radius: 5px;
        }
        .loading {
            text-align: center;
            padding: 20px;
        }
        button {
            display: block;
            margin: 20px auto;
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <h1>Cryptocurrency Market</h1>
    
    <div class="section">
        <h2>Trending Cryptocurrencies</h2>
        <button id="fetchTrendingButton">Refresh Trending</button>
        <div id="trendingLoading" class="loading">Loading trending cryptocurrencies...</div>
        <div id="trendingError" class="error-message" style="display: none;"></div>
        <div id="trendingContainer" class="crypto-container" style="display: none;"></div>
    </div>
    
    <div class="section">
        <h2>All Cryptocurrencies</h2>
        <button id="fetchAllButton">Refresh All</button>
        <div id="allLoading" class="loading">Loading all cryptocurrencies...</div>
        <div id="allError" class="error-message" style="display: none;"></div>
        <table id="cryptoTable" class="crypto-table" style="display: none;">
            <thead>
                <tr>
                    <th>#</th>
                    <th>Name</th>
                    <th>Price</th>
                    <th>24h %</th>
                    <th>Market Cap</th>
                    <th>Volume (24h)</th>
                </tr>
            </thead>
            <tbody id="cryptoTableBody"></tbody>
        </table>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Elements for trending section
            const fetchTrendingButton = document.getElementById('fetchTrendingButton');
            const trendingLoadingElement = document.getElementById('trendingLoading');
            const trendingErrorElement = document.getElementById('trendingError');
            const trendingContainer = document.getElementById('trendingContainer');
            
            // Elements for all cryptos section
            const fetchAllButton = document.getElementById('fetchAllButton');
            const allLoadingElement = document.getElementById('allLoading');
            const allErrorElement = document.getElementById('allError');
            const cryptoTable = document.getElementById('cryptoTable');
            const cryptoTableBody = document.getElementById('cryptoTableBody');
            
            // Add event listeners
            fetchTrendingButton.addEventListener('click', fetchTrendingCryptos);
            fetchAllButton.addEventListener('click', fetchAllCryptos);
            
            // Format currency
            function formatCurrency(value) {
                return new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: 'USD',
                    minimumFractionDigits: value < 1 ? 4 : 2,
                    maximumFractionDigits: value < 1 ? 6 : 2
                }).format(value);
            }
            
            // Format large numbers
            function formatLargeNumber(value) {
                if (value >= 1e12) return `$${(value / 1e12).toFixed(2)}T`;
                if (value >= 1e9) return `$${(value / 1e9).toFixed(2)}B`;
                if (value >= 1e6) return `$${(value / 1e6).toFixed(2)}M`;
                return formatCurrency(value);
            }
            
            // Format percentage
            function formatPercentage(value) {
                return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
            }
            
            // Function to fetch trending cryptocurrencies
            async function fetchTrendingCryptos() {
                // Show loading, hide error and container
                trendingLoadingElement.style.display = 'block';
                trendingErrorElement.style.display = 'none';
                trendingContainer.style.display = 'none';
                
                try {
                    const response = await fetch('http://localhost:3001/api/cryptos/trending');
                    
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    
                    const data = await response.json();
                    console.log('Trending data:', data);
                    
                    if (data.success && data.data) {
                        displayTrendingCryptos(data.data);
                    } else {
                        throw new Error('Invalid response format');
                    }
                } catch (error) {
                    console.error('Error fetching trending cryptos:', error);
                    trendingErrorElement.textContent = `Error: ${error.message}`;
                    trendingErrorElement.style.display = 'block';
                } finally {
                    trendingLoadingElement.style.display = 'none';
                }
            }
            
            // Function to display trending cryptocurrencies
            function displayTrendingCryptos(cryptos) {
                // Clear previous content
                trendingContainer.innerHTML = '';
                
                if (!Array.isArray(cryptos) || cryptos.length === 0) {
                    trendingErrorElement.textContent = 'No trending cryptocurrencies found';
                    trendingErrorElement.style.display = 'block';
                    return;
                }
                
                // Create a card for each cryptocurrency
                cryptos.forEach(crypto => {
                    const card = document.createElement('div');
                    card.className = 'crypto-card';
                    
                    const isPositive = crypto.priceChangePercentage24h >= 0;
                    
                    card.innerHTML = `
                        <div class="crypto-header">
                            <img src="${crypto.image || 'https://via.placeholder.com/40'}" alt="${crypto.name}" class="crypto-image">
                            <div>
                                <div class="crypto-name">${crypto.name}</div>
                                <div class="crypto-symbol">${crypto.symbol}</div>
                            </div>
                        </div>
                        <div class="crypto-price">${formatCurrency(crypto.currentPrice)}</div>
                        <div class="crypto-change ${isPositive ? 'positive' : 'negative'}">
                            ${formatPercentage(crypto.priceChangePercentage24h)}
                        </div>
                        <div class="crypto-details">
                            <div class="crypto-detail">
                                <div class="detail-label">Market Cap</div>
                                <div class="detail-value">${formatLargeNumber(crypto.marketCap)}</div>
                            </div>
                            <div class="crypto-detail">
                                <div class="detail-label">Volume (24h)</div>
                                <div class="detail-value">${formatLargeNumber(crypto.volume24h)}</div>
                            </div>
                        </div>
                    `;
                    
                    trendingContainer.appendChild(card);
                });
                
                // Show the container
                trendingContainer.style.display = 'flex';
            }
            
            // Function to fetch all cryptocurrencies
            async function fetchAllCryptos() {
                // Show loading, hide error and table
                allLoadingElement.style.display = 'block';
                allErrorElement.style.display = 'none';
                cryptoTable.style.display = 'none';
                
                try {
                    const response = await fetch('http://localhost:3001/api/cryptos');
                    
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    
                    const data = await response.json();
                    console.log('All cryptos data:', data);
                    
                    if (data.success && data.data && data.data.cryptos) {
                        displayAllCryptos(data.data.cryptos);
                    } else {
                        throw new Error('Invalid response format');
                    }
                } catch (error) {
                    console.error('Error fetching all cryptos:', error);
                    allErrorElement.textContent = `Error: ${error.message}`;
                    allErrorElement.style.display = 'block';
                } finally {
                    allLoadingElement.style.display = 'none';
                }
            }
            
            // Function to display all cryptocurrencies
            function displayAllCryptos(cryptos) {
                // Clear previous content
                cryptoTableBody.innerHTML = '';
                
                if (!Array.isArray(cryptos) || cryptos.length === 0) {
                    allErrorElement.textContent = 'No cryptocurrencies found';
                    allErrorElement.style.display = 'block';
                    return;
                }
                
                // Create a row for each cryptocurrency
                cryptos.forEach((crypto, index) => {
                    const row = document.createElement('tr');
                    
                    const isPositive = crypto.priceChangePercentage24h >= 0;
                    
                    row.innerHTML = `
                        <td>${index + 1}</td>
                        <td>
                            <div style="display: flex; align-items: center;">
                                <img src="${crypto.image || 'https://via.placeholder.com/24'}" alt="${crypto.name}" style="width: 24px; height: 24px; margin-right: 10px; border-radius: 50%;">
                                <div>
                                    <div style="font-weight: bold;">${crypto.name}</div>
                                    <div style="color: #666; font-size: 12px;">${crypto.symbol}</div>
                                </div>
                            </div>
                        </td>
                        <td>${formatCurrency(crypto.currentPrice)}</td>
                        <td style="color: ${isPositive ? '#2e7d32' : '#c62828'};">
                            ${formatPercentage(crypto.priceChangePercentage24h)}
                        </td>
                        <td>${formatLargeNumber(crypto.marketCap)}</td>
                        <td>${formatLargeNumber(crypto.volume24h)}</td>
                    `;
                    
                    cryptoTableBody.appendChild(row);
                });
                
                // Show the table
                cryptoTable.style.display = 'table';
            }
            
            // Fetch data on page load
            fetchTrendingCryptos();
            fetchAllCryptos();
        });
    </script>
</body>
</html>
