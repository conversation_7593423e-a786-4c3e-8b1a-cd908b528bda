const http = require('http');

const checkFrontend = () => {
  console.log('🔍 Checking frontend status...\n');

  const options = {
    hostname: 'localhost',
    port: 5173,
    path: '/',
    method: 'GET',
    headers: {
      'User-Agent': 'Frontend-Checker/1.0'
    }
  };

  const req = http.request(options, (res) => {
    console.log(`📊 Status Code: ${res.statusCode}`);
    console.log(`📋 Headers:`, res.headers);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });

    res.on('end', () => {
      console.log('\n📄 Response Body:');
      console.log(data.substring(0, 500) + (data.length > 500 ? '...' : ''));
      
      if (res.statusCode === 200) {
        console.log('\n✅ Frontend is responding correctly!');
        
        // Check if it contains React root
        if (data.includes('id="root"')) {
          console.log('✅ HTML contains React root element');
        } else {
          console.log('❌ HTML missing React root element');
        }
        
        // Check if it contains the main script
        if (data.includes('main.tsx')) {
          console.log('✅ HTML includes main.tsx script');
        } else {
          console.log('❌ HTML missing main.tsx script');
        }
      } else {
        console.log('❌ Frontend returned error status');
      }
    });
  });

  req.on('error', (e) => {
    console.error('❌ Request failed:', e.message);
  });

  req.end();
};

checkFrontend();
