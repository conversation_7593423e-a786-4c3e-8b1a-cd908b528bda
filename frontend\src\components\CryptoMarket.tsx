import React, { useState } from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  Button,
  VStack,
  HStack,
  Grid,
  GridItem,
  Card,
  CardBody,
  Badge,
  useColorModeValue,
  Flex,
  Spacer,
  Spinner,
  Image,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
  Input,
  InputGroup,
  InputLeftElement,
  Select,
  NumberInput,
  NumberInputField,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
} from '@chakra-ui/react';
import { FaSearch, FaSync, FaChartLine, FaCoins, FaDollarSign, FaTrendingUp } from 'react-icons/fa';
import { useLiveCryptoData } from '../hooks/useLiveCryptoData';
import GlobalMarketStats from './GlobalMarketStats';

const CryptoMarket: React.FC = () => {
  const bgColor = useColorModeValue('gray.50', 'gray.900');
  const cardBg = useColorModeValue('white', 'gray.800');
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('market_cap_desc');
  const [perPage, setPerPage] = useState(100);
  const [showOnlyGainers, setShowOnlyGainers] = useState(false);
  const [showOnlyLosers, setShowOnlyLosers] = useState(false);

  // Use live crypto data hook for comprehensive market data
  const { data: marketData, loading, error, refetch, lastUpdated } = useLiveCryptoData(undefined, perPage);

  // Filter data based on search query and filters
  const filteredData = marketData
    .filter(crypto => {
      const matchesSearch = crypto.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           crypto.symbol.toLowerCase().includes(searchQuery.toLowerCase());

      if (showOnlyGainers) return matchesSearch && crypto.positive;
      if (showOnlyLosers) return matchesSearch && !crypto.positive;

      return matchesSearch;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'market_cap_desc':
          return b.marketCap - a.marketCap;
        case 'market_cap_asc':
          return a.marketCap - b.marketCap;
        case 'price_desc':
          return parseFloat(b.price.replace(/[$,]/g, '')) - parseFloat(a.price.replace(/[$,]/g, ''));
        case 'price_asc':
          return parseFloat(a.price.replace(/[$,]/g, '')) - parseFloat(b.price.replace(/[$,]/g, ''));
        case 'change_desc':
          return b.changePercent - a.changePercent;
        case 'change_asc':
          return a.changePercent - b.changePercent;
        case 'name_asc':
          return a.name.localeCompare(b.name);
        case 'name_desc':
          return b.name.localeCompare(a.name);
        case 'volume_desc':
          return b.volume - a.volume;
        case 'volume_asc':
          return a.volume - b.volume;
        default:
          return b.marketCap - a.marketCap;
      }
    });

  const formatMarketCap = (marketCap: number): string => {
    if (marketCap >= 1e12) return `$${(marketCap / 1e12).toFixed(2)}T`;
    if (marketCap >= 1e9) return `$${(marketCap / 1e9).toFixed(2)}B`;
    if (marketCap >= 1e6) return `$${(marketCap / 1e6).toFixed(2)}M`;
    return `$${marketCap.toLocaleString()}`;
  };

  const formatVolume = (volume: number): string => {
    if (volume >= 1e9) return `$${(volume / 1e9).toFixed(2)}B`;
    if (volume >= 1e6) return `$${(volume / 1e6).toFixed(2)}M`;
    if (volume >= 1e3) return `$${(volume / 1e3).toFixed(2)}K`;
    return `$${volume.toLocaleString()}`;
  };

  // Calculate market stats
  const totalMarketCap = marketData.reduce((sum, crypto) => sum + crypto.marketCap, 0);
  const totalVolume = marketData.reduce((sum, crypto) => sum + crypto.volume, 0);
  const gainers = marketData.filter(crypto => crypto.positive).length;
  const losers = marketData.filter(crypto => !crypto.positive).length;

  return (
    <Box bg={bgColor} minH="100vh" py={8}>
      <Container maxW="7xl">
        {/* Header */}
        <VStack spacing={6} mb={8}>
          <Heading size="xl" textAlign="center">
            🌐 Cryptocurrency Market
          </Heading>
          <Text fontSize="lg" color="gray.600" textAlign="center" maxW="2xl">
            Real-time cryptocurrency prices, market capitalizations, and trading volumes
          </Text>
        </VStack>

        {/* Global Market Stats */}
        <Box mb={8}>
          <Heading size="md" mb={4}>Global Market Overview</Heading>
          <GlobalMarketStats />
        </Box>

        {/* Market Stats */}
        <Grid templateColumns={{ base: '1fr', md: 'repeat(2, 1fr)', lg: 'repeat(4, 1fr)' }} gap={6} mb={8}>
          <Card bg={cardBg}>
            <CardBody>
              <Stat>
                <StatLabel>Total Market Cap</StatLabel>
                <StatNumber fontSize="lg">{formatMarketCap(totalMarketCap)}</StatNumber>
                <StatHelpText>
                  <FaDollarSign style={{ display: 'inline', marginRight: '4px' }} />
                  All cryptocurrencies
                </StatHelpText>
              </Stat>
            </CardBody>
          </Card>

          <Card bg={cardBg}>
            <CardBody>
              <Stat>
                <StatLabel>24h Volume</StatLabel>
                <StatNumber fontSize="lg">{formatVolume(totalVolume)}</StatNumber>
                <StatHelpText>
                  <FaChartLine style={{ display: 'inline', marginRight: '4px' }} />
                  Trading volume
                </StatHelpText>
              </Stat>
            </CardBody>
          </Card>

          <Card bg={cardBg}>
            <CardBody>
              <Stat>
                <StatLabel>Gainers</StatLabel>
                <StatNumber fontSize="lg" color="green.500">{gainers}</StatNumber>
                <StatHelpText>
                  <StatArrow type="increase" />
                  24h positive
                </StatHelpText>
              </Stat>
            </CardBody>
          </Card>

          <Card bg={cardBg}>
            <CardBody>
              <Stat>
                <StatLabel>Losers</StatLabel>
                <StatNumber fontSize="lg" color="red.500">{losers}</StatNumber>
                <StatHelpText>
                  <StatArrow type="decrease" />
                  24h negative
                </StatHelpText>
              </Stat>
            </CardBody>
          </Card>
        </Grid>

        {/* Controls */}
        <Card bg={cardBg} mb={6}>
          <CardBody>
            <Grid templateColumns={{ base: '1fr', md: 'repeat(3, 1fr)' }} gap={4} alignItems="end">
              <Box>
                <Text mb={2} fontWeight="medium">Search</Text>
                <InputGroup>
                  <InputLeftElement pointerEvents="none">
                    <FaSearch color="gray.300" />
                  </InputLeftElement>
                  <Input
                    placeholder="Search cryptocurrencies..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </InputGroup>
              </Box>

              <Box>
                <Text mb={2} fontWeight="medium">Sort By</Text>
                <Select value={sortBy} onChange={(e) => setSortBy(e.target.value)}>
                  <option value="market_cap_desc">Market Cap (High to Low)</option>
                  <option value="market_cap_asc">Market Cap (Low to High)</option>
                  <option value="price_desc">Price (High to Low)</option>
                  <option value="price_asc">Price (Low to High)</option>
                  <option value="change_desc">24h Change (High to Low)</option>
                  <option value="change_asc">24h Change (Low to High)</option>
                </Select>
              </Box>

              <HStack spacing={2}>
                {lastUpdated && (
                  <Text fontSize="sm" color="gray.500">
                    Updated: {lastUpdated.toLocaleTimeString()}
                  </Text>
                )}
                <Button
                  leftIcon={<FaSync />}
                  onClick={refetch}
                  isLoading={loading}
                  loadingText="Updating"
                  colorScheme="blue"
                >
                  Refresh
                </Button>
              </HStack>
            </Grid>
          </CardBody>
        </Card>

        {/* Error Alert */}
        {error && (
          <Alert status="warning" mb={6} borderRadius="md">
            <AlertIcon />
            <Box>
              <AlertTitle>API Connection Issue</AlertTitle>
              <AlertDescription>
                Using cached data. {error}
              </AlertDescription>
            </Box>
          </Alert>
        )}

        {/* Market Table */}
        <Card bg={cardBg}>
          <CardBody p={0}>
            {loading && marketData.length === 0 ? (
              <Flex justify="center" py={12}>
                <VStack spacing={4}>
                  <Spinner size="xl" color="blue.500" />
                  <Text>Loading cryptocurrency market data...</Text>
                </VStack>
              </Flex>
            ) : (
              <TableContainer>
                <Table variant="simple">
                  <Thead>
                    <Tr>
                      <Th>Rank</Th>
                      <Th>Name</Th>
                      <Th isNumeric>Price</Th>
                      <Th isNumeric>24h Change</Th>
                      <Th isNumeric>Market Cap</Th>
                      <Th isNumeric>Volume (24h)</Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {filteredData.map((crypto, index) => (
                      <Tr key={crypto.id || index} _hover={{ bg: useColorModeValue('gray.50', 'gray.700') }}>
                        <Td>
                          <Badge colorScheme="gray">
                            #{crypto.rank || index + 1}
                          </Badge>
                        </Td>
                        <Td>
                          <HStack spacing={3}>
                            {crypto.image && (
                              <Image
                                src={crypto.image}
                                alt={crypto.name}
                                boxSize={6}
                                borderRadius="full"
                              />
                            )}
                            <VStack align="start" spacing={0}>
                              <Text fontWeight="bold">{crypto.name}</Text>
                              <Text fontSize="sm" color="gray.500">{crypto.symbol}</Text>
                            </VStack>
                          </HStack>
                        </Td>
                        <Td isNumeric fontWeight="bold">
                          {crypto.price}
                        </Td>
                        <Td isNumeric>
                          <Badge colorScheme={crypto.positive ? 'green' : 'red'}>
                            {crypto.change}
                          </Badge>
                        </Td>
                        <Td isNumeric>
                          {formatMarketCap(crypto.marketCap)}
                        </Td>
                        <Td isNumeric>
                          {formatVolume(crypto.volume)}
                        </Td>
                      </Tr>
                    ))}
                  </Tbody>
                </Table>
              </TableContainer>
            )}
          </CardBody>
        </Card>

        {/* Footer Info */}
        <Box mt={8} textAlign="center">
          <Text fontSize="sm" color="gray.500">
            Data provided by CoinGecko API • Updates every 30 seconds • {filteredData.length} cryptocurrencies shown
          </Text>
        </Box>
      </Container>
    </Box>
  );
};

export default CryptoMarket;
