export { I as AnyDataTag, b1 as CancelOptions, J as DataTag, z as DefaultError, b0 as DefaultOptions, af as DefaultedInfiniteQueryObserverOptions, ad as DefaultedQueryObserverOptions, aK as DefinedInfiniteQueryObserverResult, aB as DefinedQueryObserverResult, V as Enabled, ai as EnsureInfiniteQueryDataOptions, ah as EnsureQueryDataOptions, aj as FetchInfiniteQueryOptions, aq as FetchNextPageOptions, ar as FetchPreviousPageOptions, ag as FetchQueryOptions, at as FetchStatus, a1 as GetNextPageParamFunction, a0 as GetPreviousPageParamFunction, K as InferDataFromTag, L as InferErrorFromTag, a2 as InfiniteData, aD as InfiniteQueryObserverBaseResult, aG as InfiniteQueryObserverLoadingErrorResult, aF as InfiniteQueryObserverLoadingResult, ae as InfiniteQueryObserverOptions, aE as InfiniteQueryObserverPendingResult, aJ as InfiniteQueryObserverPlaceholderResult, aH as InfiniteQueryObserverRefetchErrorResult, aL as InfiniteQueryObserverResult, aI as InfiniteQueryObserverSuccessResult, a8 as InfiniteQueryPageParamsOptions, Y as InitialDataFunction, a7 as InitialPageParam, ao as InvalidateOptions, am as InvalidateQueryFilters, aU as MutateFunction, aT as MutateOptions, aQ as MutationFunction, aM as MutationKey, aP as MutationMeta, aV as MutationObserverBaseResult, aY as MutationObserverErrorResult, aW as MutationObserverIdleResult, aX as MutationObserverLoadingResult, aS as MutationObserverOptions, a_ as MutationObserverResult, aZ as MutationObserverSuccessResult, aR as MutationOptions, aO as MutationScope, aN as MutationStatus, a4 as NetworkMode, N as NoInfer, b4 as NotifyEvent, b3 as NotifyEventType, a5 as NotifyOnChangeProps, O as OmitKeyof, ac as Optional, y as Override, Z as PlaceholderDataFunction, _ as QueriesPlaceholderDataFunction, a$ as QueryClientConfig, P as QueryFunction, X as QueryFunctionContext, A as QueryKey, $ as QueryKeyHashFunction, a3 as QueryMeta, au as QueryObserverBaseResult, ax as QueryObserverLoadingErrorResult, aw as QueryObserverLoadingResult, aa as QueryObserverOptions, av as QueryObserverPendingResult, aA as QueryObserverPlaceholderResult, ay as QueryObserverRefetchErrorResult, aC as QueryObserverResult, az as QueryObserverSuccessResult, a6 as QueryOptions, W as QueryPersister, as as QueryStatus, al as RefetchOptions, an as RefetchQueryFilters, R as Register, ap as ResetOptions, ak as ResultOptions, b2 as SetDataOptions, T as StaleTime, a9 as ThrowOnError, G as UnsetMarker, ab as WithRequired, E as dataTagErrorSymbol, B as dataTagSymbol, F as unsetMarker } from './hydration-Dmdl5wo-.cjs';
import './removable.cjs';
import './subscribable.cjs';
