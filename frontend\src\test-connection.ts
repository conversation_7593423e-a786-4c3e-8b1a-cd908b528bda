/**
 * Test Connection Script
 *
 * This script tests the connection to the backend API and MongoDB
 * when the application starts.
 */

import api from './services/api';
import mongodbService from './services/mongodbService';

// Test API connection
const testApiConnection = async () => {
  try {
    const response = await api.get('/health');
    console.log('API Connection Test:', response.data.success ? 'Success' : 'Failed');
    console.log('API Version:', response.data.version);
    console.log('Environment:', response.data.environment);

    // Check available features
    if (response.data.features) {
      console.log('Available Features:');
      Object.entries(response.data.features).forEach(([feature, available]) => {
        console.log(`- ${feature}: ${available ? 'Available' : 'Not Available'}`);
      });
    }

    return response.data.success;
  } catch (error) {
    console.error('API Connection Test Failed:', error);
    return false;
  }
};

// Test MongoDB connection
const testMongoDBConnection = async () => {
  try {
    const status = await mongodbService.checkConnection();
    console.log('MongoDB Connection Test:', status.connected ? 'Success' : 'Failed');

    if (status.connected) {
      console.log('MongoDB Host:', status.host);
      console.log('MongoDB Database:', status.name);
    } else {
      console.warn('MongoDB Connection Error:', status.error);
      console.log('Using Fallback Mode:', status.fallbackMode);
    }

    return status.connected;
  } catch (error) {
    console.error('MongoDB Connection Test Failed:', error);
    return false;
  }
};

// Run tests
const runConnectionTests = async () => {
  console.log('=== Connection Tests ===');

  const apiConnected = await testApiConnection();
  const mongoDBConnected = await testMongoDBConnection();

  console.log('=== Connection Test Results ===');
  console.log('API Connected:', apiConnected);
  console.log('MongoDB Connected:', mongoDBConnected);
  console.log('===========================');
};

// Run tests when the application starts
runConnectionTests();

export { testApiConnection, testMongoDBConnection, runConnectionTests };
