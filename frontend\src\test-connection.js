/**
 * Crypto Web Application - Frontend-Backend Connection Test
 * Run this in the browser console to test the connection
 */

// Test the API connection
async function testApiConnection() {
  console.log('%c Testing API connection...', 'color: #0066cc; font-weight: bold;');

  try {
    // Use the full URL to ensure we're testing the correct endpoint
    const response = await fetch('http://localhost:3001/api');

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const data = await response.json();
    console.log('API Response:', data);

    return {
      success: true,
      status: response.status,
      data
    };
  } catch (error) {
    console.error('API Connection Error:', error.message);

    return {
      success: false,
      error: error.message
    };
  }
}

// Test the health endpoint
async function testHealthEndpoint() {
  console.log('%c Testing health endpoint...', 'color: #0066cc; font-weight: bold;');

  try {
    const response = await fetch('http://localhost:3001/api/health');

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const data = await response.json();
    console.log('Health Response:', data);

    return {
      success: true,
      status: response.status,
      data
    };
  } catch (error) {
    console.error('Health Endpoint Error:', error.message);

    return {
      success: false,
      error: error.message
    };
  }
}

// Test the cryptos endpoint
async function testCryptosEndpoint() {
  console.log('%c Testing cryptos endpoint...', 'color: #0066cc; font-weight: bold;');

  try {
    const response = await fetch('http://localhost:3001/api/cryptos');

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const data = await response.json();
    console.log('Cryptos Response:', data);

    return {
      success: true,
      status: response.status,
      data
    };
  } catch (error) {
    console.error('Cryptos Endpoint Error:', error.message);

    return {
      success: false,
      error: error.message
    };
  }
}

// Test the AI insights endpoint
async function testAiInsightsEndpoint() {
  console.log('%c Testing AI insights endpoint...', 'color: #0066cc; font-weight: bold;');

  try {
    const response = await fetch('http://localhost:3001/api/ai/insights');

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const data = await response.json();
    console.log('AI Insights Response:', data);

    return {
      success: true,
      status: response.status,
      data
    };
  } catch (error) {
    console.error('AI Insights Endpoint Error:', error.message);

    return {
      success: false,
      error: error.message
    };
  }
}

// Test WebSocket connection
function testWebSocketConnection() {
  console.log('%c Testing WebSocket connection...', 'color: #0066cc; font-weight: bold;');

  return new Promise((resolve) => {
    try {
      const ws = new WebSocket('ws://localhost:3001');

      ws.onopen = () => {
        console.log('WebSocket connection established');

        // Send a test message
        ws.send(JSON.stringify({
          type: 'subscribe',
          channel: 'prices',
          coins: ['1', '2', '3'] // Bitcoin, Ethereum, Cardano
        }));

        // Close the connection after 3 seconds
        setTimeout(() => {
          ws.close();
          resolve({
            success: true,
            message: 'WebSocket connection successful'
          });
        }, 3000);
      };

      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          console.log('WebSocket message received:', data);
        } catch (e) {
          console.log('WebSocket message received (raw):', event.data);
        }
      };

      ws.onerror = (error) => {
        console.error('WebSocket Error:', error);
        resolve({
          success: false,
          error: 'WebSocket connection failed'
        });
      };

      ws.onclose = () => {
        console.log('WebSocket connection closed');
      };

      // Timeout after 5 seconds
      setTimeout(() => {
        if (ws.readyState !== WebSocket.OPEN) {
          ws.close();
          resolve({
            success: false,
            error: 'WebSocket connection timed out'
          });
        }
      }, 5000);
    } catch (error) {
      console.error('WebSocket Setup Error:', error.message);
      resolve({
        success: false,
        error: error.message
      });
    }
  });
}

// Run all tests
async function runAllTests() {
  console.log('%c =================================', 'color: #4CAF50; font-weight: bold;');
  console.log('%c CRYPTO WEB APP CONNECTION TESTS', 'color: #4CAF50; font-weight: bold;');
  console.log('%c =================================', 'color: #4CAF50; font-weight: bold;');

  // Test API connection
  const apiResult = await testApiConnection();
  console.log(
    '%c API Test: ' + (apiResult.success ? 'SUCCESS ✅' : 'FAILED ❌'),
    `color: ${apiResult.success ? '#4CAF50' : '#F44336'}; font-weight: bold;`
  );
  console.log('---------------------------------');

  // Test health endpoint
  const healthResult = await testHealthEndpoint();
  console.log(
    '%c Health Test: ' + (healthResult.success ? 'SUCCESS ✅' : 'FAILED ❌'),
    `color: ${healthResult.success ? '#4CAF50' : '#F44336'}; font-weight: bold;`
  );
  console.log('---------------------------------');

  // Test cryptos endpoint
  const cryptosResult = await testCryptosEndpoint();
  console.log(
    '%c Cryptos Test: ' + (cryptosResult.success ? 'SUCCESS ✅' : 'FAILED ❌'),
    `color: ${cryptosResult.success ? '#4CAF50' : '#F44336'}; font-weight: bold;`
  );
  console.log('---------------------------------');

  // Test AI insights endpoint
  const aiResult = await testAiInsightsEndpoint();
  console.log(
    '%c AI Insights Test: ' + (aiResult.success ? 'SUCCESS ✅' : 'FAILED ❌'),
    `color: ${aiResult.success ? '#4CAF50' : '#F44336'}; font-weight: bold;`
  );
  console.log('---------------------------------');

  // Test WebSocket connection
  const wsResult = await testWebSocketConnection();
  console.log(
    '%c WebSocket Test: ' + (wsResult.success ? 'SUCCESS ✅' : 'FAILED ❌'),
    `color: ${wsResult.success ? '#4CAF50' : '#F44336'}; font-weight: bold;`
  );
  console.log('---------------------------------');

  // Summary
  console.log('%c TEST SUMMARY:', 'color: #0066cc; font-weight: bold;');
  console.log(
    '%c - API Connection: ' + (apiResult.success ? 'OK ✅' : 'FAILED ❌'),
    `color: ${apiResult.success ? '#4CAF50' : '#F44336'};`
  );
  console.log(
    '%c - Health Endpoint: ' + (healthResult.success ? 'OK ✅' : 'FAILED ❌'),
    `color: ${healthResult.success ? '#4CAF50' : '#F44336'};`
  );
  console.log(
    '%c - Cryptos Endpoint: ' + (cryptosResult.success ? 'OK ✅' : 'FAILED ❌'),
    `color: ${cryptosResult.success ? '#4CAF50' : '#F44336'};`
  );
  console.log(
    '%c - AI Insights Endpoint: ' + (aiResult.success ? 'OK ✅' : 'FAILED ❌'),
    `color: ${aiResult.success ? '#4CAF50' : '#F44336'};`
  );
  console.log(
    '%c - WebSocket Connection: ' + (wsResult.success ? 'OK ✅' : 'FAILED ❌'),
    `color: ${wsResult.success ? '#4CAF50' : '#F44336'};`
  );

  const overallSuccess = apiResult.success && healthResult.success && cryptosResult.success && aiResult.success && wsResult.success;

  console.log('%c =================================', 'color: #4CAF50; font-weight: bold;');
  console.log(
    overallSuccess
      ? '%c ✅ ALL TESTS PASSED - Connection is working properly! '
      : '%c ❌ SOME TESTS FAILED - Check the logs for details ',
    overallSuccess
      ? 'background: #4CAF50; color: white; padding: 5px; border-radius: 5px;'
      : 'background: #F44336; color: white; padding: 5px; border-radius: 5px;'
  );
  console.log('%c =================================', 'color: #4CAF50; font-weight: bold;');

  // If using mock data, show a message
  if (!apiResult.success) {
    console.log(
      '%c NOTE: The frontend will use mock data since the backend is not available.',
      'background: #FFC107; color: black; padding: 5px; border-radius: 5px;'
    );
  }

  return {
    success: overallSuccess,
    api: apiResult,
    health: healthResult,
    cryptos: cryptosResult,
    ai: aiResult,
    websocket: wsResult
  };
}

// Export the test functions
window.testConnection = {
  testApi: testApiConnection,
  testHealth: testHealthEndpoint,
  testCryptos: testCryptosEndpoint,
  testAi: testAiInsightsEndpoint,
  testWebSocket: testWebSocketConnection,
  runAll: runAllTests
};

// Run the test automatically after a short delay
setTimeout(() => {
  console.log('%c Running connection tests...', 'color: #0066cc; font-weight: bold;');
  runAllTests();
}, 2000);
