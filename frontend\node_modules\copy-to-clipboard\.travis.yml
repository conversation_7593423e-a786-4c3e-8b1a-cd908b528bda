language: node_js
node_js:
  - "4.2"

env:
  - E2E_BROWSER_VENDOR="internet explorer" E2E_PLATFORM="Windows 8.1" E2E_BROWSER_VERSION="latest"
  - E2E_BROWSER_VENDOR="internet explorer" E2E_PLATFORM="Windows 10" E2E_BROWSER_VERSION="latest"
  - E2E_BROWSER_VENDOR="MicrosoftEdge" E2E_PLATFORM="Windows 10" E2E_BROWSER_VERSION="latest"
  - E2E_BROWSER_VENDOR="chrome" E2E_PLATFORM="Windows 8.1" E2E_BROWSER_VERSION="latest"
  - E2E_BROWSER_VENDOR="chrome" E2E_PLATFORM="Windows 8.1" E2E_BROWSER_VERSION="latest-1"
  - E2E_BROWSER_VENDOR="chrome" E2E_PLATFORM="Windows 10" E2E_BROWSER_VERSION="latest"
  - E2E_BROWSER_VENDOR="chrome" E2E_PLATFORM="Windows 10" E2E_BROWSER_VERSION="latest-1"
  - E2E_BROWSER_VENDOR="firefox" E2E_PLATFORM="Windows 8.1" E2E_BROWSER_VERSION="latest"
  - E2E_BROWSER_VENDOR="firefox" E2E_PLATFORM="Windows 8.1" E2E_BROWSER_VERSION="latest-1"
  - E2E_BROWSER_VENDOR="firefox" E2E_PLATFORM="Windows 10" E2E_BROWSER_VERSION="latest"
  - E2E_BROWSER_VENDOR="firefox" E2E_PLATFORM="Windows 10" E2E_BROWSER_VERSION="latest-1"
  # TODO: add workaround for chromedriver issue
  # - E2E_BROWSER_VENDOR="chrome" E2E_PLATFORM="OS X 10.11" E2E_BROWSER_VERSION="latest"
  # - E2E_BROWSER_VENDOR="chrome" E2E_PLATFORM="OS X 10.11" E2E_BROWSER_VERSION="latest-1"
  # TODO: look into execCommand(“copy”) support in Safari
  # window.document.queryCommandSupported('copy') - false in 9.1.1
  # - E2E_BROWSER_VENDOR="safari" E2E_PLATFORM="OS X 10.11" E2E_BROWSER_VERSION="latest"
  - E2E_BROWSER_VENDOR="firefox" E2E_PLATFORM="OS X 10.11" E2E_BROWSER_VERSION="latest"
  - E2E_BROWSER_VENDOR="firefox" E2E_PLATFORM="OS X 10.11" E2E_BROWSER_VERSION="latest-1"

addons:
  sauce_connect:
    username: "sudodoki"
    access_key:
      secure: "MBHn7+lxuJ0uNuFC8HjIZtosuOHICNW52fEhvtnJhXmaGOUXGLrQFUCvECJOoGrdOWmkiFaGgYINdF57HbYpyAICmv51UBlzHYftkTspYooH5+vni0ezPPpvxqNpUM/dlwfrdzC/ag97VYDeIxdYRKREwhRsNd5npE0Zrx1xmvzmnpdqdVhQ7Z/wQUDjv7talKC2fkaIYP+wEhVblnr18u0phFLqyADNsCV7D9QmILQWC4ieSz6ylTW9fb69B4rZElLj+D8qGvyvgmLnu+dK4Vlh0WuTdmVZ8TZS4OeBRIbjq/Mi2vMVuQLXz3DTiGWRZeDOBB5PYm/orgjgsnNg5hPx6t7yc4ypcBYf2gHxc31lV9VZRb10py33D8qPtcmPptwIWlsCQ4ANDyOwxck0FejMpjT+Ktkyme4nAvt9op289x8KKI0w8W7nHIB930lEookJCeMHWjyycPLJDTwYe60zBS24vNRF46ixhfpeO1zGw8nEj5qqSoEltunCOQ3Uvl25tNEuk9R649TyWbegjTuS4txqAsSjUnVMGvRBcZef4YyOQsggos1YbJMNnFSvDH9VaPYiJbg/qR7tb0YJ8gtAC9/6iqba6EZadkJQe9DHs6qu5zuoiwfSgBZTON2oWL6AGwySADm44SL7ikKJN8Zb8bLjQ1o15KinBVKLuOk="

script:
  - REMOTE_SELENIUM=true npm test -- -e saucelabs
