import React, { useState, useEffect } from 'react';
import {
  Box, Text, Badge, Tooltip, Flex, Icon, Popover,
  <PERSON>overTrigger, PopoverContent, PopoverHeader, PopoverBody,
  PopoverArrow, PopoverCloseButton, List, ListItem, ListIcon,
  Button, useDisclosure, Modal, ModalOverlay, ModalContent,
  ModalHeader, ModalFooter, ModalBody, ModalCloseButton,
  Code, VStack, Divider
} from '@chakra-ui/react';
import { FiDatabase, FiAlertCircle, FiCheckCircle, FiInfo, FiServer } from 'react-icons/fi';
import api from '../../services/api';

interface MongoDBInfo {
  connected: boolean;
  host: string | null;
  name: string | null;
  error: string | null;
  fallbackMode: boolean;
}

const MongoDBStatus: React.FC = () => {
  const [status, setStatus] = useState<'connected' | 'disconnected' | 'checking'>('checking');
  const [message, setMessage] = useState<string>('Checking MongoDB connection...');
  const [dbInfo, setDbInfo] = useState<MongoDBInfo | null>(null);
  const [lastChecked, setLastChecked] = useState<string>('');
  const { isOpen, onOpen, onClose } = useDisclosure();

  const checkMongoDBStatus = async () => {
    try {
      setStatus('checking');
      const response = await api.get('/health');
      setLastChecked(new Date().toLocaleTimeString());

      if (response.data.success && response.data.mongodb) {
        setDbInfo(response.data.mongodb);

        if (response.data.mongodb.connected) {
          setStatus('connected');
          setMessage(`Connected to MongoDB: ${response.data.mongodb.host || 'MongoDB Atlas'}`);
        } else {
          setStatus('disconnected');
          setMessage(response.data.mongodb.error || 'MongoDB connection failed');
        }
      } else {
        setStatus('disconnected');
        setMessage('Could not retrieve MongoDB status');
      }
    } catch (error) {
      console.error('Error checking MongoDB status:', error);
      setStatus('disconnected');
      setMessage('Could not check MongoDB connection status');
    }
  };

  useEffect(() => {
    // Check status immediately
    checkMongoDBStatus();

    // Set up interval to check status periodically
    const interval = setInterval(checkMongoDBStatus, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, []);

  return (
    <>
      <Popover placement="bottom" closeOnBlur={true}>
        <PopoverTrigger>
          <Flex align="center" px={2} cursor="pointer">
            <Icon
              as={FiDatabase}
              mr={1}
              color={status === 'connected' ? 'green.500' : status === 'checking' ? 'yellow.500' : 'red.500'}
              boxSize={4}
            />
            <Badge colorScheme={status === 'connected' ? 'green' : status === 'checking' ? 'yellow' : 'red'} variant="subtle">
              <Text fontSize="xs">MongoDB: {status}</Text>
            </Badge>
          </Flex>
        </PopoverTrigger>
        <PopoverContent width="300px">
          <PopoverArrow />
          <PopoverCloseButton />
          <PopoverHeader fontWeight="bold">MongoDB Connection Status</PopoverHeader>
          <PopoverBody>
            <VStack align="start" spacing={2}>
              <Flex justify="space-between" width="100%">
                <Text fontSize="sm">Status:</Text>
                <Badge colorScheme={status === 'connected' ? 'green' : 'red'}>
                  {status === 'connected' ? 'Connected' : 'Disconnected'}
                </Badge>
              </Flex>

              {dbInfo && (
                <>
                  {dbInfo.connected && (
                    <>
                      <Flex justify="space-between" width="100%">
                        <Text fontSize="sm">Host:</Text>
                        <Text fontSize="sm" fontWeight="medium">{dbInfo.host}</Text>
                      </Flex>
                      <Flex justify="space-between" width="100%">
                        <Text fontSize="sm">Database:</Text>
                        <Text fontSize="sm" fontWeight="medium">{dbInfo.name}</Text>
                      </Flex>
                    </>
                  )}

                  {!dbInfo.connected && (
                    <>
                      <Flex justify="space-between" width="100%">
                        <Text fontSize="sm">Error:</Text>
                        <Text fontSize="sm" color="red.500">{dbInfo.error}</Text>
                      </Flex>
                      <Flex justify="space-between" width="100%">
                        <Text fontSize="sm">Fallback Mode:</Text>
                        <Badge colorScheme={dbInfo.fallbackMode ? 'yellow' : 'green'}>
                          {dbInfo.fallbackMode ? 'Active' : 'Inactive'}
                        </Badge>
                      </Flex>
                    </>
                  )}
                </>
              )}

              <Divider />

              <Flex justify="space-between" width="100%">
                <Text fontSize="xs" color="gray.500">Last checked: {lastChecked}</Text>
                <Button size="xs" onClick={checkMongoDBStatus} colorScheme="blue" variant="outline">
                  Refresh
                </Button>
              </Flex>

              <Button size="sm" width="100%" onClick={onOpen} leftIcon={<FiInfo />}>
                Connection Details
              </Button>
            </VStack>
          </PopoverBody>
        </PopoverContent>
      </Popover>

      <Modal isOpen={isOpen} onClose={onClose} size="lg">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>MongoDB Connection Details</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <VStack align="start" spacing={4}>
              <Box>
                <Text fontWeight="bold" mb={2}>Current Status</Text>
                <Flex align="center">
                  <Icon
                    as={status === 'connected' ? FiCheckCircle : FiAlertCircle}
                    color={status === 'connected' ? 'green.500' : 'red.500'}
                    mr={2}
                    boxSize={5}
                  />
                  <Text>{message}</Text>
                </Flex>
              </Box>

              {dbInfo && dbInfo.connected && (
                <Box width="100%">
                  <Text fontWeight="bold" mb={2}>Connection Information</Text>
                  <List spacing={2}>
                    <ListItem>
                      <ListIcon as={FiServer} color="blue.500" />
                      <Text as="span" fontWeight="medium">Host:</Text> {dbInfo.host}
                    </ListItem>
                    <ListItem>
                      <ListIcon as={FiDatabase} color="blue.500" />
                      <Text as="span" fontWeight="medium">Database:</Text> {dbInfo.name}
                    </ListItem>
                  </List>
                </Box>
              )}

              {dbInfo && !dbInfo.connected && (
                <Box width="100%">
                  <Text fontWeight="bold" mb={2}>Troubleshooting</Text>
                  <List spacing={3}>
                    <ListItem>
                      <ListIcon as={FiAlertCircle} color="red.500" />
                      <Text as="span" fontWeight="medium">Error:</Text> {dbInfo.error}
                    </ListItem>
                    <ListItem>
                      <ListIcon as={FiInfo} color="blue.500" />
                      <Text fontWeight="medium">Connection Troubleshooting Steps:</Text>
                      <List ml={6} mt={2} spacing={1}>
                        <ListItem>1. Verify the username and password in your connection string</ListItem>
                        <ListItem>2. Check if your IP address is whitelisted in MongoDB Atlas</ListItem>
                        <ListItem>3. Ensure the cluster name and database name are correct</ListItem>
                        <ListItem>4. Try installing and running MongoDB locally as a fallback</ListItem>
                      </List>
                    </ListItem>
                    <ListItem>
                      <ListIcon as={FiInfo} color="yellow.500" />
                      <Text as="span" fontWeight="medium">Fallback Mode:</Text> {dbInfo.fallbackMode ? 'Active (using mock data)' : 'Inactive'}
                    </ListItem>
                  </List>
                </Box>
              )}

              <Box width="100%">
                <Text fontWeight="bold" mb={2}>Connection String Format</Text>
                <Code p={2} borderRadius="md" width="100%" fontSize="sm">
                  mongodb+srv://&lt;username&gt;:&lt;password&gt;@&lt;cluster-url&gt;/&lt;database&gt;?retryWrites=true&w=majority
                </Code>
                <Text mt={2} fontSize="sm" color="gray.600">
                  Update this in your .env file to connect to your MongoDB Atlas database.
                </Text>
              </Box>
            </VStack>
          </ModalBody>
          <ModalFooter>
            <Button colorScheme="blue" mr={3} onClick={checkMongoDBStatus}>
              Refresh Status
            </Button>
            <Button variant="ghost" onClick={onClose}>Close</Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
};

export default MongoDBStatus;
