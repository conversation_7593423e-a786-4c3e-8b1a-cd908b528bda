'use strict';

var background = require('./background.cjs');
var border = require('./border.cjs');
var color = require('./color.cjs');
var effect = require('./effect.cjs');
var filter = require('./filter.cjs');
var flexbox = require('./flexbox.cjs');
var grid = require('./grid.cjs');
var interactivity = require('./interactivity.cjs');
var layout = require('./layout.cjs');
var list = require('./list.cjs');
var others = require('./others.cjs');
var position = require('./position.cjs');
var ring = require('./ring.cjs');
var space = require('./space.cjs');
var textDecoration = require('./text-decoration.cjs');
var transform = require('./transform.cjs');
var transition = require('./transition.cjs');
var typography = require('./typography.cjs');
var scroll = require('./scroll.cjs');



exports.background = background.background;
exports.border = border.border;
exports.color = color.color;
exports.effect = effect.effect;
exports.filter = filter.filter;
exports.flexbox = flexbox.flexbox;
exports.grid = grid.grid;
exports.interactivity = interactivity.interactivity;
exports.layout = layout.layout;
exports.list = list.list;
exports.others = others.others;
exports.position = position.position;
exports.ring = ring.ring;
exports.space = space.space;
exports.textDecoration = textDecoration.textDecoration;
exports.transform = transform.transform;
exports.transition = transition.transition;
exports.typography = typography.typography;
exports.scroll = scroll.scroll;
