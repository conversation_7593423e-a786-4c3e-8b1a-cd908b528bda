'use strict';

var createAnatomy = require('./create-anatomy.cjs');
var components = require('./components.cjs');



exports.anatomy = createAnatomy.anatomy;
exports.accordionAnatomy = components.accordionAnatomy;
exports.alertAnatomy = components.alertAnatomy;
exports.avatarAnatomy = components.avatarAnatomy;
exports.breadcrumbAnatomy = components.breadcrumbAnatomy;
exports.buttonAnatomy = components.buttonAnatomy;
exports.cardAnatomy = components.cardAnatomy;
exports.checkboxAnatomy = components.checkboxAnatomy;
exports.circularProgressAnatomy = components.circularProgressAnatomy;
exports.drawerAnatomy = components.drawerAnatomy;
exports.editableAnatomy = components.editableAnatomy;
exports.formAnatomy = components.formAnatomy;
exports.formErrorAnatomy = components.formErrorAnatomy;
exports.inputAnatomy = components.inputAnatomy;
exports.listAnatomy = components.listAnatomy;
exports.menuAnatomy = components.menuAnatomy;
exports.modalAnatomy = components.modalAnatomy;
exports.numberInputAnatomy = components.numberInputAnatomy;
exports.pinInputAnatomy = components.pinInputAnatomy;
exports.popoverAnatomy = components.popoverAnatomy;
exports.progressAnatomy = components.progressAnatomy;
exports.radioAnatomy = components.radioAnatomy;
exports.selectAnatomy = components.selectAnatomy;
exports.sliderAnatomy = components.sliderAnatomy;
exports.statAnatomy = components.statAnatomy;
exports.stepperAnatomy = components.stepperAnatomy;
exports.switchAnatomy = components.switchAnatomy;
exports.tableAnatomy = components.tableAnatomy;
exports.tabsAnatomy = components.tabsAnatomy;
exports.tagAnatomy = components.tagAnatomy;
