<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Console - Crypto Tracker</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .console { background: #000; color: #0f0; padding: 20px; border-radius: 8px; font-family: monospace; }
        .error { color: #f00; }
        .warning { color: #ff0; }
        .info { color: #0ff; }
        .success { color: #0f0; }
        .section { margin: 20px 0; padding: 15px; background: white; border-radius: 8px; }
    </style>
</head>
<body>
    <h1>🔧 Crypto Tracker Debug Console</h1>
    
    <div class="section">
        <h2>Application Status</h2>
        <div id="status"></div>
    </div>
    
    <div class="section">
        <h2>Console Output</h2>
        <div class="console" id="console"></div>
    </div>
    
    <div class="section">
        <h2>Network Requests</h2>
        <div id="network"></div>
    </div>
    
    <script>
        const statusDiv = document.getElementById('status');
        const consoleDiv = document.getElementById('console');
        const networkDiv = document.getElementById('network');
        
        function addStatus(message, type = 'info') {
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            statusDiv.appendChild(div);
        }
        
        function addConsole(message, type = 'info') {
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            consoleDiv.appendChild(div);
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        }
        
        function addNetwork(message) {
            const div = document.createElement('div');
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            networkDiv.appendChild(div);
        }
        
        // Override console methods to capture output
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addConsole(args.join(' '), 'info');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addConsole(args.join(' '), 'warning');
        };
        
        // Capture unhandled errors
        window.addEventListener('error', (e) => {
            addConsole(`ERROR: ${e.message} at ${e.filename}:${e.lineno}`, 'error');
        });
        
        window.addEventListener('unhandledrejection', (e) => {
            addConsole(`UNHANDLED PROMISE REJECTION: ${e.reason}`, 'error');
        });
        
        addStatus('Debug console initialized', 'success');
        addConsole('Starting diagnostic checks...');
        
        // Test basic functionality
        addConsole('Testing React loading...');
        
        // Try to load the main app
        setTimeout(() => {
            addConsole('Attempting to load main application...');
            
            const iframe = document.createElement('iframe');
            iframe.src = '/';
            iframe.style.width = '100%';
            iframe.style.height = '400px';
            iframe.style.border = '1px solid #ccc';
            iframe.style.marginTop = '20px';
            
            iframe.onload = () => {
                addConsole('Main application iframe loaded successfully', 'success');
            };
            
            iframe.onerror = () => {
                addConsole('Failed to load main application iframe', 'error');
            };
            
            document.body.appendChild(iframe);
        }, 2000);
        
        // Test API connectivity
        addConsole('Testing CoinGecko API connectivity...');
        fetch('https://api.coingecko.com/api/v3/ping')
            .then(response => response.json())
            .then(data => {
                addConsole(`CoinGecko API Status: ${JSON.stringify(data)}`, 'success');
                addNetwork('CoinGecko API: Connected successfully');
            })
            .catch(error => {
                addConsole(`CoinGecko API Error: ${error.message}`, 'error');
                addNetwork('CoinGecko API: Connection failed');
            });
    </script>
</body>
</html>
