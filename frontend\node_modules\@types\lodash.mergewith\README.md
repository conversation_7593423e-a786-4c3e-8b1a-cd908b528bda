# Installation
> `npm install --save @types/lodash.mergewith`

# Summary
This package contains type definitions for lodash.mergewith (https://lodash.com).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/lodash.mergewith.
## [index.d.ts](https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/lodash.mergewith/index.d.ts)
````ts
// Generated from https://github.com/DefinitelyTyped/DefinitelyTyped/blob/master/types/lodash/scripts/generate-modules.ts

import { mergeWith } from "lodash";
export = mergeWith;

````

### Additional Details
 * Last updated: Tue, 07 Nov 2023 09:09:38 GMT
 * Dependencies: [@types/lodash](https://npmjs.com/package/@types/lodash)

# Credits
These definitions were written by [<PERSON>](https://github.com/bczengel), and [<PERSON><PERSON>](https://github.com/chrootsu).
