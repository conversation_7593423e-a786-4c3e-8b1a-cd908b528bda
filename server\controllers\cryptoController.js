/**
 * Cryptocurrency Controller
 * 
 * This controller handles all cryptocurrency-related API endpoints.
 * It uses the enhanced CoinGecko service to fetch data and provides
 * consistent error handling and response formatting.
 */

const coinGeckoService = require('../services/enhancedCoinGeckoService');
const { formatCoinData, formatMarketData } = require('../utils/dataFormatters');

/**
 * Get all cryptocurrencies with pagination and filters
 * @route GET /api/cryptos
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} - JSON response with cryptocurrency data
 */
const getCryptos = async (req, res) => {
  try {
    // Parse query parameters
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const vsCurrency = req.query.vsCurrency || 'usd';
    const order = req.query.order || 'market_cap_desc';
    const sparkline = req.query.sparkline === 'true';
    
    // Get data from CoinGecko
    const coinsData = await coinGeckoService.getCoinsMarkets({
      page,
      perPage: limit,
      vsCurrency,
      order,
      sparkline,
    });
    
    // Get global data for total count
    const globalData = await coinGeckoService.getGlobalData();
    
    // Format data for frontend
    const formattedCoins = coinsData.map(coin => formatCoinData(coin));
    
    // Calculate total pages
    const totalCoins = globalData.data?.active_cryptocurrencies || formattedCoins.length;
    const totalPages = Math.ceil(totalCoins / limit);
    
    // Return formatted response
    return res.json({
      success: true,
      data: {
        cryptos: formattedCoins,
        page,
        limit,
        pages: totalPages,
        total: totalCoins,
      },
    });
  } catch (error) {
    console.error('Error in getCryptos controller:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch cryptocurrency data',
      error: error.message,
    });
  }
};

/**
 * Get detailed data for a specific cryptocurrency
 * @route GET /api/cryptos/:id
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} - JSON response with detailed cryptocurrency data
 */
const getCryptoById = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Get detailed coin data
    const coinData = await coinGeckoService.getCoinDetails(id);
    
    // Get market chart data
    const marketChart = await coinGeckoService.getCoinMarketChart(id, {
      days: req.query.days || '30',
      interval: req.query.interval || 'daily',
    });
    
    // Format data for frontend
    const formattedCoin = formatCoinData(coinData, true);
    formattedCoin.marketData = formatMarketData(marketChart);
    
    return res.json({
      success: true,
      data: formattedCoin,
    });
  } catch (error) {
    console.error(`Error in getCryptoById controller for ${req.params.id}:`, error);
    return res.status(500).json({
      success: false,
      message: `Failed to fetch data for cryptocurrency ${req.params.id}`,
      error: error.message,
    });
  }
};

/**
 * Get trending cryptocurrencies
 * @route GET /api/cryptos/trending
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} - JSON response with trending cryptocurrency data
 */
const getTrendingCryptos = async (req, res) => {
  try {
    // Get trending coins
    const trendingData = await coinGeckoService.getTrendingCoins();
    
    // Format data for frontend
    const formattedTrending = trendingData.coins.map(item => ({
      _id: item.item.id,
      name: item.item.name,
      symbol: item.item.symbol,
      image: item.item.large,
      rank: item.item.market_cap_rank,
      priceBtc: item.item.price_btc,
      score: 7 - item.item.score, // Reverse score (0 is best in API, we want highest to be best)
    }));
    
    return res.json({
      success: true,
      data: formattedTrending,
    });
  } catch (error) {
    console.error('Error in getTrendingCryptos controller:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch trending cryptocurrencies',
      error: error.message,
    });
  }
};

/**
 * Search for cryptocurrencies
 * @route GET /api/cryptos/search/:query
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} - JSON response with search results
 */
const searchCryptos = async (req, res) => {
  try {
    const { query } = req.params;
    
    // Search for coins
    const searchResults = await coinGeckoService.searchCoins(query);
    
    // Format data for frontend
    const formattedResults = {
      coins: searchResults.coins.map(coin => ({
        _id: coin.id,
        name: coin.name,
        symbol: coin.symbol,
        image: coin.large,
        rank: coin.market_cap_rank,
      })),
      categories: searchResults.categories,
      exchanges: searchResults.exchanges,
    };
    
    return res.json({
      success: true,
      data: formattedResults,
    });
  } catch (error) {
    console.error(`Error in searchCryptos controller for "${req.params.query}":`, error);
    return res.status(500).json({
      success: false,
      message: 'Failed to search cryptocurrencies',
      error: error.message,
    });
  }
};

/**
 * Get global cryptocurrency market data
 * @route GET /api/cryptos/global
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} - JSON response with global market data
 */
const getGlobalMarketData = async (req, res) => {
  try {
    // Get global data
    const globalData = await coinGeckoService.getGlobalData();
    
    // Format data for frontend
    const formattedGlobal = {
      totalMarketCap: globalData.data.total_market_cap.usd,
      total24hVolume: globalData.data.total_volume.usd,
      btcDominance: globalData.data.market_cap_percentage.btc,
      ethDominance: globalData.data.market_cap_percentage.eth,
      activeCryptocurrencies: globalData.data.active_cryptocurrencies,
      markets: globalData.data.markets,
      marketCapChangePercentage24h: globalData.data.market_cap_change_percentage_24h_usd,
      lastUpdated: globalData.data.updated_at,
    };
    
    return res.json({
      success: true,
      data: formattedGlobal,
    });
  } catch (error) {
    console.error('Error in getGlobalMarketData controller:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch global market data',
      error: error.message,
    });
  }
};

/**
 * Clear cache for CoinGecko API
 * @route POST /api/cryptos/clear-cache
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} - JSON response with success message
 */
const clearCache = async (req, res) => {
  try {
    const { cacheType } = req.body;
    
    // Clear specific cache or all caches
    coinGeckoService.clearCache(cacheType);
    
    return res.json({
      success: true,
      message: cacheType ? `Cache for ${cacheType} cleared successfully` : 'All caches cleared successfully',
    });
  } catch (error) {
    console.error('Error in clearCache controller:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to clear cache',
      error: error.message,
    });
  }
};

/**
 * Get cache statistics
 * @route GET /api/cryptos/cache-stats
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} - JSON response with cache statistics
 */
const getCacheStats = async (req, res) => {
  try {
    // Get cache statistics
    const cacheStats = coinGeckoService.getCacheStats();
    
    return res.json({
      success: true,
      data: cacheStats,
    });
  } catch (error) {
    console.error('Error in getCacheStats controller:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to get cache statistics',
      error: error.message,
    });
  }
};

module.exports = {
  getCryptos,
  getCryptoById,
  getTrendingCryptos,
  searchCryptos,
  getGlobalMarketData,
  clearCache,
  getCacheStats,
};
