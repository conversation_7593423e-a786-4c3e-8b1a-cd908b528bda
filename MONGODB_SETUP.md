# MongoDB Setup Guide for Crypto Tracker

This guide will help you set up MongoDB for the Crypto Tracker application.

## Prerequisites

- Node.js and npm installed
- MongoDB Atlas account (free tier is sufficient)

## Option 1: Using MongoDB Atlas (Recommended)

MongoDB Atlas is a cloud-hosted MongoDB service that eliminates the need to install and manage MongoDB locally.

### Step 1: Create a MongoDB Atlas Account

1. Go to [MongoDB Atlas](https://www.mongodb.com/cloud/atlas/register) and sign up for a free account.
2. Complete the registration process with your email and password.
3. Select the free tier option (M0) when prompted.

### Step 2: Create a New Cluster

1. After logging in, click "Build a Database".
2. Select the "FREE" option (Shared Clusters).
3. Choose a cloud provider (AWS, Google Cloud, or Azure) and a region closest to you.
4. Click "Create Cluster" and wait for the cluster to be created (this may take a few minutes).

### Step 3: Set Up Database Access

1. In the left sidebar, click on "Database Access" under the Security section.
2. Click "Add New Database User".
3. Create a new user with a username and password. Make sure to use a strong password and remember it.
   ```
   Username: crypto_user
   Password: [your-secure-password]
   ```
4. Set the user privileges to "Read and Write to Any Database" and click "Add User".

### Step 4: Set Up Network Access

1. In the left sidebar, click on "Network Access" under the Security section.
2. Click "Add IP Address".
3. For development, you can click "Allow Access from Anywhere" (not recommended for production).
   Alternatively, add your current IP address.
4. Click "Confirm" to save the IP address.

### Step 5: Get Your Connection String

1. Go back to the "Database" section and click "Connect" on your cluster.
2. Select "Connect your application".
3. Choose "Node.js" as your driver and the latest version.
4. Copy the connection string. It will look something like this:
   ```
   mongodb+srv://crypto_user:<password>@cluster0.xxxxx.mongodb.net/myFirstDatabase?retryWrites=true&w=majority
   ```
5. Replace `<password>` with your actual password and `myFirstDatabase` with `crypto_tracker`.

### Step 6: Update Your .env File

1. Open the `.env` file in your project root directory.
2. Update the MONGO_URI variable with your connection string:
   ```
   MONGO_URI=mongodb+srv://crypto_user:<EMAIL>/crypto_tracker?retryWrites=true&w=majority
   ```
3. Save the file.

## Option 2: Using Local MongoDB

If you prefer to use a local MongoDB instance, follow these steps:

### Step 1: Install MongoDB Community Edition

#### Windows
1. Download the MongoDB Community Edition installer from the [MongoDB Download Center](https://www.mongodb.com/try/download/community).
2. Run the installer and follow the installation wizard.
3. Choose "Complete" installation.
4. Install MongoDB as a service.

#### macOS
1. Install MongoDB using Homebrew:
   ```
   brew tap mongodb/brew
   brew install mongodb-community
   ```

#### Linux (Ubuntu)
1. Import the MongoDB public GPG key:
   ```
   wget -qO - https://www.mongodb.org/static/pgp/server-5.0.asc | sudo apt-key add -
   ```
2. Create a list file for MongoDB:
   ```
   echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/5.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-5.0.list
   ```
3. Update the package list and install MongoDB:
   ```
   sudo apt-get update
   sudo apt-get install -y mongodb-org
   ```

### Step 2: Start MongoDB Service

#### Windows
MongoDB should be running as a service. If not, you can start it from the Services console.

#### macOS
```
brew services start mongodb-community
```

#### Linux (Ubuntu)
```
sudo systemctl start mongod
```

### Step 3: Update Your .env File

1. Open the `.env` file in your project root directory.
2. Update the MONGO_URI variable with your local MongoDB connection string:
   ```
   MONGO_URI=mongodb://localhost:27017/crypto_tracker
   ```
3. Save the file.

## Setting Up the Database

After configuring your MongoDB connection, run the setup script to create the necessary collections and indexes:

```
npm run setup-mongodb
```

This script will:
1. Connect to your MongoDB database
2. Create the required collections (users, portfolios, transactions, watchlists)
3. Set up indexes for better performance
4. Optionally create a test user

## Testing the Connection

To test your MongoDB connection, run:

```
npm run test-mongodb
```

This will attempt to connect to your MongoDB database and display the connection status.

## Troubleshooting

### Authentication Failed
- Double-check your username and password in the connection string.
- Make sure you've replaced the `<password>` placeholder with your actual password.
- Ensure the password doesn't contain special characters that need to be URL-encoded.

### Connection Timeout
- Check your network access settings in MongoDB Atlas.
- Make sure your current IP address is whitelisted or that you've allowed access from anywhere.

### DNS Resolution Issues
- This might be a temporary issue with MongoDB Atlas.
- Try again later or check the MongoDB Atlas status page.

### Local MongoDB Not Running
- Make sure the MongoDB service is running.
- Check the MongoDB logs for any errors.

## Next Steps

Once your MongoDB connection is set up, you can start the application:

```
npm run dev
```

This will start both the backend server and the frontend development server.
