import * as CSS from "csstype";
import { Config } from "../utils/prop-config";
import { ResponsiveValue } from "../utils";
export declare const list: Config;
export interface ListProps {
    listStyleType?: ResponsiveValue<CSS.Property.ListStyleType>;
    /**
     * The CSS `list-style-position` property
     */
    listStylePosition?: ResponsiveValue<CSS.Property.ListStylePosition>;
    /**
     * The CSS `list-style-position` property
     */
    listStylePos?: ResponsiveValue<CSS.Property.ListStylePosition>;
    /**
     * The CSS `list-style-image` property
     */
    listStyleImage?: ResponsiveValue<CSS.Property.ListStyleImage>;
    /**
     * The CSS `list-style-image` property
     */
    listStyleImg?: ResponsiveValue<CSS.Property.ListStyleImage>;
}
