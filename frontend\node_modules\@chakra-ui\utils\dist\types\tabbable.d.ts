export declare const hasDisplayNone: (element: HTMLElement) => boolean;
export declare const hasTabIndex: (element: HTMLElement) => boolean;
export declare const hasNegativeTabIndex: (element: HTMLElement) => boolean;
export declare function hasFocusWithin(element: HTMLElement): boolean;
export declare function isFocusable(element: HTMLElement): boolean;
export declare function isTabbable(element?: HTMLElement | null): boolean;
