import React, { Component, ErrorInfo, ReactNode } from 'react';
import {
  <PERSON>ert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Box,
  Button,
  VStack,
  Text,
  Code,
  Collapse,
  useDisclosure,
} from '@chakra-ui/react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

// Error details component with expandable stack trace
const ErrorDetails = ({ error, errorInfo }: { error: Error; errorInfo: ErrorInfo }) => {
  const { isOpen, onToggle } = useDisclosure();

  return (
    <Box mt={4}>
      <Alert status="error" variant="left-accent" flexDirection="column" alignItems="start" p={4} borderRadius="md">
        <AlertIcon />
        <AlertTitle mt={0} mb={2} fontSize="lg">
          {error.name}: {error.message}
        </AlertTitle>
        <AlertDescription>
          <Button size="sm" onClick={onToggle} variant="outline" colorScheme="red" mt={2} mb={isOpen ? 4 : 0}>
            {isOpen ? 'Hide' : 'Show'} Stack Trace
          </Button>
          <Collapse in={isOpen} animateOpacity>
            <Box
              mt={2}
              p={3}
              bg="gray.900"
              color="white"
              borderRadius="md"
              fontSize="sm"
              fontFamily="monospace"
              whiteSpace="pre-wrap"
              overflowX="auto"
              maxH="300px"
              overflowY="auto"
            >
              {errorInfo.componentStack}
            </Box>
          </Collapse>
        </AlertDescription>
      </Alert>
    </Box>
  );
};

class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
    error: null,
    errorInfo: null,
  };

  public static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error, errorInfo: null };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Log the error to console
    console.error('Error caught by ErrorBoundary:', error, errorInfo);
    this.setState({ errorInfo });
  }

  private handleReload = (): void => {
    window.location.reload();
  };

  private handleGoHome = (): void => {
    window.location.href = '/';
  };

  public render(): ReactNode {
    const { hasError, error, errorInfo } = this.state;
    const { children, fallback } = this.props;

    if (hasError && error) {
      // Custom fallback UI
      if (fallback) {
        return fallback;
      }

      // Default fallback UI
      return (
        <Box p={4} borderRadius="md" bg="white" boxShadow="md" my={8} mx="auto" maxW="container.md">
          <VStack spacing={4} align="stretch">
            <Alert status="error" variant="subtle" flexDirection="column" alignItems="center" justifyContent="center" textAlign="center" height="200px" borderRadius="md">
              <AlertIcon boxSize="40px" mr={0} />
              <AlertTitle mt={4} mb={1} fontSize="lg">
                Something went wrong
              </AlertTitle>
              <AlertDescription maxWidth="sm">
                We're sorry, but an error occurred while rendering this component.
              </AlertDescription>
            </Alert>

            {errorInfo && <ErrorDetails error={error} errorInfo={errorInfo} />}

            <Box textAlign="center" mt={4}>
              <Button colorScheme="brand" mr={3} onClick={this.handleReload}>
                Reload Page
              </Button>
              <Button variant="outline" onClick={this.handleGoHome}>
                Go to Home
              </Button>
            </Box>
          </VStack>
        </Box>
      );
    }

    // When there's no error, render children normally
    return children;
  }
}

export default ErrorBoundary;
