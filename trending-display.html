<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trending Cryptocurrencies</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .crypto-container {
            display: flex;
            overflow-x: auto;
            gap: 20px;
            padding: 10px 0;
        }
        .crypto-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            padding: 20px;
            min-width: 200px;
            transition: transform 0.3s ease;
        }
        .crypto-card:hover {
            transform: translateY(-5px);
        }
        .crypto-name {
            font-weight: bold;
            font-size: 18px;
            margin-bottom: 5px;
        }
        .crypto-symbol {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
        }
        .crypto-rank {
            display: inline-block;
            background-color: #f0f0f0;
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 12px;
            margin-bottom: 10px;
        }
        .crypto-score {
            color: #4CAF50;
            font-weight: bold;
        }
        .error-message {
            color: red;
            text-align: center;
            padding: 20px;
            background-color: #ffebee;
            border-radius: 5px;
        }
        .loading {
            text-align: center;
            padding: 20px;
        }
        button {
            display: block;
            margin: 20px auto;
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <h1>Trending Cryptocurrencies</h1>
    
    <button id="fetchButton">Fetch Trending Cryptos</button>
    
    <div id="content">
        <div id="loading" class="loading">Loading...</div>
        <div id="error" class="error-message" style="display: none;"></div>
        <div id="cryptoContainer" class="crypto-container" style="display: none;"></div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const fetchButton = document.getElementById('fetchButton');
            const loadingElement = document.getElementById('loading');
            const errorElement = document.getElementById('error');
            const cryptoContainer = document.getElementById('cryptoContainer');
            
            // Hide loading initially
            loadingElement.style.display = 'none';
            
            fetchButton.addEventListener('click', fetchTrendingCryptos);
            
            // Function to fetch trending cryptocurrencies
            async function fetchTrendingCryptos() {
                // Show loading, hide error and container
                loadingElement.style.display = 'block';
                errorElement.style.display = 'none';
                cryptoContainer.style.display = 'none';
                
                try {
                    const response = await fetch('http://localhost:3001/api/cryptos/trending');
                    
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    
                    const data = await response.json();
                    console.log('Trending data:', data);
                    
                    if (data.success && data.data) {
                        displayCryptos(data.data);
                    } else {
                        throw new Error('Invalid response format');
                    }
                } catch (error) {
                    console.error('Error fetching trending cryptos:', error);
                    errorElement.textContent = `Error: ${error.message}`;
                    errorElement.style.display = 'block';
                } finally {
                    loadingElement.style.display = 'none';
                }
            }
            
            // Function to display cryptocurrencies
            function displayCryptos(cryptos) {
                // Clear previous content
                cryptoContainer.innerHTML = '';
                
                if (!Array.isArray(cryptos) || cryptos.length === 0) {
                    errorElement.textContent = 'No trending cryptocurrencies found';
                    errorElement.style.display = 'block';
                    return;
                }
                
                // Create a card for each cryptocurrency
                cryptos.forEach(crypto => {
                    const card = document.createElement('div');
                    card.className = 'crypto-card';
                    
                    card.innerHTML = `
                        <div class="crypto-name">${crypto.name}</div>
                        <div class="crypto-symbol">${crypto.symbol}</div>
                        <div class="crypto-rank">Rank #${crypto.rank || 'N/A'}</div>
                        <div>Score: <span class="crypto-score">${crypto.score || 'N/A'}</span></div>
                    `;
                    
                    cryptoContainer.appendChild(card);
                });
                
                // Show the container
                cryptoContainer.style.display = 'flex';
            }
            
            // Fetch trending cryptos on page load
            fetchTrendingCryptos();
        });
    </script>
</body>
</html>
