/**
 * Authentication Routes
 * 
 * This file defines all routes related to authentication.
 */

const express = require('express');
const router = express.Router();
const authController = require('../controllers/authController');
const { protect } = require('../middleware/authMiddleware');

/**
 * @route   POST /api/auth/register
 * @desc    Register a new user
 * @access  Public
 */
router.post('/register', authController.register);

/**
 * @route   POST /api/auth/login
 * @desc    Login user
 * @access  Public
 */
router.post('/login', authController.login);

/**
 * @route   GET /api/auth/me
 * @desc    Get current user profile
 * @access  Private
 */
router.get('/me', protect, authController.getMe);

/**
 * @route   PUT /api/auth/me
 * @desc    Update user profile
 * @access  Private
 */
router.put('/me', protect, authController.updateMe);

/**
 * @route   GET /api/auth/google
 * @desc    Google OAuth login
 * @access  Public
 */
router.get('/google', (req, res) => {
  res.json({
    success: true,
    message: 'Google authentication not implemented in this demo',
    redirectUrl: '/api/auth/google/callback',
  });
});

/**
 * @route   GET /api/auth/google/callback
 * @desc    Google OAuth callback
 * @access  Public
 */
router.get('/google/callback', authController.googleCallback);

/**
 * @route   GET /api/auth/facebook
 * @desc    Facebook OAuth login
 * @access  Public
 */
router.get('/facebook', (req, res) => {
  res.json({
    success: true,
    message: 'Facebook authentication not implemented in this demo',
    redirectUrl: '/api/auth/facebook/callback',
  });
});

/**
 * @route   GET /api/auth/facebook/callback
 * @desc    Facebook OAuth callback
 * @access  Public
 */
router.get('/facebook/callback', authController.facebookCallback);

/**
 * @route   GET /api/auth/github
 * @desc    GitHub OAuth login
 * @access  Public
 */
router.get('/github', (req, res) => {
  res.json({
    success: true,
    message: 'GitHub authentication not implemented in this demo',
    redirectUrl: '/api/auth/github/callback',
  });
});

/**
 * @route   GET /api/auth/github/callback
 * @desc    GitHub OAuth callback
 * @access  Public
 */
router.get('/github/callback', authController.githubCallback);

module.exports = router;
