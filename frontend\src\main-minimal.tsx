import React from 'react';
import ReactDOM from 'react-dom/client';

function MinimalApp() {
  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>🚀 Crypto Tracker - Minimal Test</h1>
      <p>✅ React is working!</p>
      <p>✅ TypeScript is working!</p>
      <p>✅ Vite is working!</p>
      <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#e8f5e9', borderRadius: '5px' }}>
        <strong>Status:</strong> Frontend is successfully loading!
      </div>
    </div>
  );
}

console.log('🔧 Loading minimal app...');

const root = document.getElementById('root');
if (root) {
  ReactDOM.createRoot(root).render(<MinimalApp />);
  console.log('✅ Minimal app rendered successfully!');
} else {
  console.error('❌ Root element not found!');
}
