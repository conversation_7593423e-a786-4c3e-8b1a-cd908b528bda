/**
 * Watchlist Model
 * 
 * This model defines the schema for user watchlists in MongoDB.
 */

const mongoose = require('mongoose');

const watchlistSchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    name: {
      type: String,
      required: [true, 'Watchlist name is required'],
      trim: true,
      maxlength: [50, 'Watchlist name cannot exceed 50 characters'],
    },
    cryptos: [
      {
        cryptoId: {
          type: String,
          required: true,
        },
        name: {
          type: String,
          required: true,
        },
        symbol: {
          type: String,
          required: true,
        },
        addedAt: {
          type: Date,
          default: Date.now,
        },
      },
    ],
    isDefault: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true,
  }
);

// Create index for faster queries
watchlistSchema.index({ user: 1 });

const Watchlist = mongoose.model('Watchlist', watchlistSchema);

module.exports = Watchlist;
