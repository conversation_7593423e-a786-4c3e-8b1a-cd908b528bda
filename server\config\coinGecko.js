/**
 * CoinGecko API Configuration
 * 
 * This file contains configuration settings for the CoinGecko API integration.
 * It includes rate limiting parameters, cache settings, and API endpoints.
 */

module.exports = {
  // API Base URL
  API_URL: 'https://api.coingecko.com/api/v3',
  
  // API Key (if you have a CoinGecko Pro subscription)
  // API_KEY: process.env.COINGECKO_API_KEY || '',
  
  // Request timeout in milliseconds
  TIMEOUT: 10000,
  
  // Rate limiting settings
  RATE_LIMIT: {
    // Maximum requests per minute (free tier: 10-50/minute)
    MAX_REQUESTS_PER_MINUTE: 30,
    
    // Minimum time between requests in milliseconds
    MIN_REQUEST_INTERVAL: 2000,
    
    // Retry settings
    MAX_RETRIES: 3,
    RETRY_DELAY: 5000,
    
    // Exponential backoff settings
    USE_EXPONENTIAL_BACKOFF: true,
    BACKOFF_MULTIPLIER: 2,
  },
  
  // Cache settings
  CACHE: {
    // Default cache expiration time in milliseconds (5 minutes)
    DEFAULT_EXPIRATION: 5 * 60 * 1000,
    
    // Cache expiration times for specific endpoints (in milliseconds)
    EXPIRATION_TIMES: {
      COINS_LIST: 24 * 60 * 60 * 1000, // 24 hours
      COINS_MARKETS: 5 * 60 * 1000,    // 5 minutes
      COIN_DETAILS: 10 * 60 * 1000,    // 10 minutes
      MARKET_CHART: 15 * 60 * 1000,    // 15 minutes
      GLOBAL: 10 * 60 * 1000,          // 10 minutes
      TRENDING: 5 * 60 * 1000,         // 5 minutes
      SEARCH: 60 * 60 * 1000,          // 1 hour
    },
    
    // Maximum number of items to cache per endpoint
    MAX_ITEMS: {
      COINS_LIST: 1,
      COINS_MARKETS: 10,
      COIN_DETAILS: 50,
      MARKET_CHART: 20,
      GLOBAL: 1,
      TRENDING: 1,
      SEARCH: 20,
    },
  },
  
  // Endpoints
  ENDPOINTS: {
    PING: '/ping',
    COINS_LIST: '/coins/list',
    COINS_MARKETS: '/coins/markets',
    COIN_DETAILS: '/coins/{id}',
    MARKET_CHART: '/coins/{id}/market_chart',
    GLOBAL: '/global',
    TRENDING: '/search/trending',
    SEARCH: '/search',
  },
  
  // Default parameters for API requests
  DEFAULT_PARAMS: {
    COINS_MARKETS: {
      vs_currency: 'usd',
      order: 'market_cap_desc',
      per_page: 100,
      page: 1,
      sparkline: false,
      price_change_percentage: '1h,24h,7d',
    },
    MARKET_CHART: {
      vs_currency: 'usd',
      days: '30',
      interval: 'daily',
    },
  },
};
