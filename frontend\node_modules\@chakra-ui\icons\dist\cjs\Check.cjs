'use strict';

var jsxRuntime = require('react/jsx-runtime');
var react = require('@chakra-ui/react');

const CheckIcon = react.createIcon({
  viewBox: "0 0 14 14",
  path: /* @__PURE__ */ jsxRuntime.jsx("g", { fill: "currentColor", children: /* @__PURE__ */ jsxRuntime.jsx("polygon", { points: "5.5 11.9993304 14 3.49933039 12.5 2 5.5 8.99933039 1.5 4.9968652 0 6.49933039" }) })
});

exports.CheckIcon = CheckIcon;
