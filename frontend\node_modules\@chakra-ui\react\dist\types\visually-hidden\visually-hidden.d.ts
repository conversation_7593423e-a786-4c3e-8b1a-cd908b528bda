/**
 * Visually hidden component used to hide
 * elements on screen
 *
 * @see Docs https://v2.chakra-ui.com/docs/components/visually-hidden
 */
export declare const VisuallyHidden: import("../system").ChakraComponent<"span", {}>;
/**
 * Visually hidden input component for designing
 * custom input components using the html `input`
 * as a proxy
 *
 * @see Docs https://v2.chakra-ui.com/docs/components/visually-hidden#visually-hidden-input
 */
export declare const VisuallyHiddenInput: import("../system").ChakraComponent<"input", {}>;
