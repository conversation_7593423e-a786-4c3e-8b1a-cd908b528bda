import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>ooter,
  ModalBody,
  ModalCloseButton,
  Button,
  Text,
  useDisclosure,
  Box,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Code,
  VStack,
  HStack,
  Badge,
  Divider,
} from '@chakra-ui/react';
import { FiAlertCircle, FiInfo } from 'react-icons/fi';
import MongoDBSetupGuide from './MongoDBSetupGuide';
import api from '../../services/api';

interface MongoDBConnectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialTab?: number;
}

const MongoDBConnectionModal: React.FC<MongoDBConnectionModalProps> = ({ 
  isOpen, 
  onClose,
  initialTab = 0
}) => {
  const [activeTab, setActiveTab] = useState(initialTab);
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [testResult, setTestResult] = useState<{
    success: boolean;
    message: string;
    details?: any;
  } | null>(null);

  const testConnection = async () => {
    setIsTestingConnection(true);
    setTestResult(null);
    
    try {
      const response = await api.get('/health');
      
      if (response.data.success && response.data.mongodb?.connected) {
        setTestResult({
          success: true,
          message: 'Successfully connected to MongoDB!',
          details: response.data.mongodb
        });
      } else {
        setTestResult({
          success: false,
          message: response.data.mongodb?.error || 'Failed to connect to MongoDB',
          details: response.data.mongodb
        });
      }
    } catch (error) {
      console.error('Error testing MongoDB connection:', error);
      setTestResult({
        success: false,
        message: 'Error testing connection',
        details: { error: String(error) }
      });
    } finally {
      setIsTestingConnection(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl" scrollBehavior="inside">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>MongoDB Connection</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <Tabs isFitted variant="enclosed" index={activeTab} onChange={setActiveTab}>
            <TabList mb="1em">
              <Tab>Connection Status</Tab>
              <Tab>Setup Guide</Tab>
              <Tab>Troubleshooting</Tab>
            </TabList>
            <TabPanels>
              <TabPanel>
                <VStack spacing={4} align="stretch">
                  <Box>
                    <Text fontSize="lg" fontWeight="bold" mb={2}>
                      MongoDB Connection Status
                    </Text>
                    <Text mb={4}>
                      Test your MongoDB connection to ensure your application can access the database.
                    </Text>
                    
                    <Button 
                      colorScheme="blue" 
                      onClick={testConnection} 
                      isLoading={isTestingConnection}
                      loadingText="Testing Connection"
                      mb={4}
                    >
                      Test Connection
                    </Button>
                    
                    {testResult && (
                      <Alert
                        status={testResult.success ? 'success' : 'error'}
                        variant="subtle"
                        flexDirection="column"
                        alignItems="flex-start"
                        borderRadius="md"
                        p={4}
                      >
                        <HStack spacing={2} mb={2}>
                          <AlertIcon />
                          <AlertTitle>{testResult.success ? 'Connection Successful' : 'Connection Failed'}</AlertTitle>
                        </HStack>
                        <AlertDescription>
                          <Text>{testResult.message}</Text>
                          
                          {testResult.details && (
                            <Box mt={3}>
                              {testResult.success ? (
                                <VStack align="start" spacing={2}>
                                  <HStack>
                                    <Text fontWeight="medium">Host:</Text>
                                    <Text>{testResult.details.host}</Text>
                                  </HStack>
                                  <HStack>
                                    <Text fontWeight="medium">Database:</Text>
                                    <Text>{testResult.details.name}</Text>
                                  </HStack>
                                </VStack>
                              ) : (
                                <VStack align="start" spacing={2}>
                                  <Text fontWeight="medium">Error Details:</Text>
                                  <Code p={2} borderRadius="md" width="100%">
                                    {testResult.details.error || 'Unknown error'}
                                  </Code>
                                  <HStack>
                                    <Text fontWeight="medium">Fallback Mode:</Text>
                                    <Badge colorScheme={testResult.details.fallbackMode ? 'yellow' : 'green'}>
                                      {testResult.details.fallbackMode ? 'Active (using mock data)' : 'Inactive'}
                                    </Badge>
                                  </HStack>
                                </VStack>
                              )}
                            </Box>
                          )}
                          
                          {!testResult.success && (
                            <Button 
                              mt={4} 
                              colorScheme="blue" 
                              size="sm" 
                              onClick={() => setActiveTab(1)}
                            >
                              View Setup Guide
                            </Button>
                          )}
                        </AlertDescription>
                      </Alert>
                    )}
                  </Box>
                  
                  <Divider my={4} />
                  
                  <Box>
                    <Text fontSize="lg" fontWeight="bold" mb={2}>
                      Connection Information
                    </Text>
                    <Text mb={2}>
                      Your MongoDB connection string should be set in the <Code>.env</Code> file in your project root:
                    </Text>
                    <Code p={2} borderRadius="md" width="100%" fontSize="sm" mb={4}>
                      MONGO_URI=mongodb+srv://&lt;username&gt;:&lt;password&gt;@&lt;cluster&gt;/&lt;database&gt;?retryWrites=true&w=majority
                    </Code>
                    <Alert status="info" borderRadius="md">
                      <AlertIcon as={FiInfo} />
                      <Box>
                        <AlertTitle>Need to set up MongoDB?</AlertTitle>
                        <AlertDescription>
                          If you haven't set up MongoDB Atlas yet, check out the Setup Guide tab for step-by-step instructions.
                        </AlertDescription>
                      </Box>
                    </Alert>
                  </Box>
                </VStack>
              </TabPanel>
              
              <TabPanel>
                <MongoDBSetupGuide />
              </TabPanel>
              
              <TabPanel>
                <VStack spacing={4} align="stretch">
                  <Text fontSize="lg" fontWeight="bold">
                    Common MongoDB Connection Issues
                  </Text>
                  
                  <Box p={4} borderWidth="1px" borderRadius="md">
                    <Text fontWeight="bold" mb={2}>Authentication Failed</Text>
                    <Text mb={2}>
                      This usually means your username or password is incorrect in the connection string.
                    </Text>
                    <Alert status="info" borderRadius="md" size="sm">
                      <AlertIcon />
                      <Box>
                        <Text fontWeight="medium">Solution:</Text>
                        <Text>Double-check your username and password. Make sure special characters in the password are URL-encoded.</Text>
                      </Box>
                    </Alert>
                  </Box>
                  
                  <Box p={4} borderWidth="1px" borderRadius="md">
                    <Text fontWeight="bold" mb={2}>Connection Timeout</Text>
                    <Text mb={2}>
                      This can happen if your IP address is not whitelisted in MongoDB Atlas.
                    </Text>
                    <Alert status="info" borderRadius="md" size="sm">
                      <AlertIcon />
                      <Box>
                        <Text fontWeight="medium">Solution:</Text>
                        <Text>Go to Network Access in MongoDB Atlas and add your current IP address or allow access from anywhere (for development only).</Text>
                      </Box>
                    </Alert>
                  </Box>
                  
                  <Box p={4} borderWidth="1px" borderRadius="md">
                    <Text fontWeight="bold" mb={2}>DNS Resolution Issues</Text>
                    <Text mb={2}>
                      This can happen if there's an issue with your network or with MongoDB Atlas.
                    </Text>
                    <Alert status="info" borderRadius="md" size="sm">
                      <AlertIcon />
                      <Box>
                        <Text fontWeight="medium">Solution:</Text>
                        <Text>Check your internet connection and try again later. You can also check the MongoDB Atlas status page.</Text>
                      </Box>
                    </Alert>
                  </Box>
                  
                  <Box p={4} borderWidth="1px" borderRadius="md">
                    <Text fontWeight="bold" mb={2}>Invalid Connection String</Text>
                    <Text mb={2}>
                      This happens if your connection string format is incorrect.
                    </Text>
                    <Alert status="info" borderRadius="md" size="sm">
                      <AlertIcon />
                      <Box>
                        <Text fontWeight="medium">Solution:</Text>
                        <Text>Make sure your connection string follows the correct format and includes all required components.</Text>
                        <Code mt={2} p={2} borderRadius="md" width="100%" fontSize="sm">
                          mongodb+srv://username:<EMAIL>/database?retryWrites=true&w=majority
                        </Code>
                      </Box>
                    </Alert>
                  </Box>
                </VStack>
              </TabPanel>
            </TabPanels>
          </Tabs>
        </ModalBody>
        <ModalFooter>
          {activeTab === 0 && (
            <Button colorScheme="blue" mr={3} onClick={testConnection} isLoading={isTestingConnection}>
              Test Connection
            </Button>
          )}
          <Button variant="ghost" onClick={onClose}>Close</Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default MongoDBConnectionModal;
