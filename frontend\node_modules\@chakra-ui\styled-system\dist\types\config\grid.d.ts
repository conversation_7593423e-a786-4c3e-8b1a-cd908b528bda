import * as CSS from "csstype";
import { Config } from "../utils/prop-config";
import { Token } from "../utils";
export declare const grid: Config;
export interface GridProps {
    /**
     * The CSS `grid-gap` property.
     *
     * It defines the gaps (gutters) between rows and columns
     *
     * @see [Mozilla Docs](https://developer.mozilla.org/en-US/docs/Web/CSS/grid-gap)
     */
    gridGap?: Token<CSS.Property.GridGap | number, "space">;
    /**
     * The CSS `grid-column-gap` property.
     *
     * It defines the size of the gap (gutter) between an element's columns.
     *
     * @see [Mozilla Docs](https://developer.mozilla.org/en-US/docs/Web/CSS/column-gap)
     */
    gridColumnGap?: Token<CSS.Property.GridColumnGap | number, "space">;
    /**
     * The CSS `grid-row-gap` property.
     *
     * It defines the size of the gap (gutter) between an element's grid rows.
     *
     * @see [Mozilla Docs](https://developer.mozilla.org/en-US/docs/Web/CSS/row-gap)
     */
    gridRowGap?: Token<CSS.Property.GridRowGap | number, "space">;
    /**
     * The CSS `grid-column` property.
     *
     * It specifies a grid item’s start position within the grid column by
     * contributing a line, a span, or nothing (automatic) to its grid placement
     *
     * @see [Mozilla Docs](https://developer.mozilla.org/en-US/docs/Web/CSS/grid-column-start)
     */
    gridColumnStart?: Token<CSS.Property.GridColumnStart>;
    /**
     * The CSS `grid-row-start` property
     *
     * It specifies a grid item’s start position within the grid row by
     * contributing a line, a span, or nothing (automatic) to its grid placement,
     * thereby specifying the `inline-start` edge of its grid area.
     *
     * @see [Mozilla Docs](https://developer.mozilla.org/en-US/docs/Web/CSS/grid-row-start)
     */
    gridRowStart?: Token<CSS.Property.GridRowStart>;
    /**
     * The CSS `grid-row-end` property
     *
     * It specifies a grid item’s end position within the grid row by
     * contributing a line, a span, or nothing (automatic) to its grid placement,
     * thereby specifying the `inline-end` edge of its grid area.
     *
     * @see [Mozilla Docs](https://developer.mozilla.org/en-US/docs/Web/CSS/grid-row-end)
     */
    gridRowEnd?: Token<CSS.Property.GridRowEnd>;
    /**
     * The CSS `grid-template` property.
     *
     * It is a shorthand property for defining grid columns, rows, and areas.
     *
     * @see [Mozilla Docs](https://developer.mozilla.org/en-US/docs/Web/CSS/grid-template)
     */
    gridTemplate?: Token<CSS.Property.GridTemplate>;
    /**
     * The CSS `grid-column` property
     *
     * It specifies a grid item’s end position within the grid column by
     * contributing a line, a span, or nothing (automatic) to its grid placement,
     * thereby specifying the block-end edge of its grid area.
     *
     * @see [Mozilla Docs](https://developer.mozilla.org/en-US/docs/Web/CSS/grid-column-end)
     */
    gridColumnEnd?: Token<CSS.Property.GridColumnEnd>;
    /**
     * The CSS `grid-column` property.
     *
     * It specifies a grid item's size and location within a grid column
     * by contributing a line, a span, or nothing (automatic) to its grid placement,
     * thereby specifying the `inline-start` and `inline-end` edge of its grid area.
     *
     * @see [Mozilla Docs](https://developer.mozilla.org/en-US/docs/Web/CSS/grid-column)
     */
    gridColumn?: Token<CSS.Property.GridColumn>;
    /**
     * The CSS `grid-row` property
     *
     * It specifies a grid item’s size and location within the grid row
     * by contributing a line, a span, or nothing (automatic) to its grid placement,
     * thereby specifying the `inline-start` and `inline-end` edge of its grid area.
     *
     * @see [Mozilla Docs](https://developer.mozilla.org/en-US/docs/Web/CSS/grid-row)
     */
    gridRow?: Token<CSS.Property.GridRow>;
    /**
     * The CSS `grid-auto-flow` property
     *
     * It controls how the auto-placement algorithm works,
     * specifying exactly how auto-placed items get flowed into the grid.
     *
     * @see [Mozilla Docs](https://developer.mozilla.org/en-US/docs/Web/CSS/grid-auto-flow)
     */
    gridAutoFlow?: Token<CSS.Property.GridAutoFlow>;
    /**
     * The CSS `grid-auto-columns` property.
     *
     * It specifies the size of an implicitly-created grid column track or pattern of tracks.
     *
     * @see [Mozilla Docs](https://developer.mozilla.org/en-US/docs/Web/CSS/grid-auto-columns)
     */
    gridAutoColumns?: Token<CSS.Property.GridAutoColumns>;
    /**
     * The CSS `grid-auto-rows` property.
     *
     * It specifies the size of an implicitly-created grid row track or pattern of tracks.
     *
     * @see [Mozilla Docs](https://developer.mozilla.org/en-US/docs/Web/CSS/grid-auto-rows)
     */
    gridAutoRows?: Token<CSS.Property.GridAutoRows>;
    /**
     * The CSS `grid-template-columns` property
     *
     * It defines the line names and track sizing functions of the grid columns.
     *
     * @see [Mozilla Docs](https://developer.mozilla.org/en-US/docs/Web/CSS/grid-template-columns)
     */
    gridTemplateColumns?: Token<CSS.Property.GridTemplateColumns>;
    /**
     * The CSS `grid-template-rows` property.
     *
     * It defines the line names and track sizing functions of the grid rows.
     *
     * @see [Mozilla Docs](https://developer.mozilla.org/en-US/docs/Web/CSS/grid-template-rows)
     */
    gridTemplateRows?: Token<CSS.Property.GridTemplateRows>;
    /**
     * The CSS `grid-template-areas` property.
     *
     * @see [Mozilla Docs](https://developer.mozilla.org/en-US/docs/Web/CSS/grid-template-areas)
     */
    gridTemplateAreas?: Token<CSS.Property.GridTemplateAreas>;
    /**
     * The CSS `grid-areas` property.
     *
     * It specifies a grid item’s size and location within a grid by
     * contributing a line, a span, or nothing (automatic)
     * to its grid placement, thereby specifying the edges of its grid area.
     *
     * @see [Mozilla Docs](https://developer.mozilla.org/en-US/docs/Web/CSS/grid-area)
     */
    gridArea?: Token<CSS.Property.GridArea>;
}
