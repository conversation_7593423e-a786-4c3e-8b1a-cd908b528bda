const express = require('express');
const cors = require('cors');
const app = express();

// Enable CORS for all routes
app.use(cors({
  origin: '*', // Allow all origins
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}));

// Add CORS preflight
app.options('*', cors());

// Parse JSON bodies
app.use(express.json());

// Logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Root endpoint
app.get('/', (req, res) => {
  console.log('Root endpoint accessed');
  res.json({
    message: 'API is running',
    status: 'ok'
  });
});

// API root endpoint
app.get('/api', (req, res) => {
  console.log('API root endpoint accessed');
  res.json({
    success: true,
    message: 'API is running',
    version: '1.0.0'
  });
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  console.log('Health check endpoint accessed');
  res.json({
    success: true,
    status: 'healthy',
    timestamp: new Date().toISOString(),
    message: 'Server is running properly'
  });
});

// Mock data
const mockCryptos = [
  {
    _id: '1',
    name: 'Bitcoin',
    symbol: 'BTC',
    currentPrice: 50000,
    marketCap: 1000000000000,
    volume24h: 50000000000,
    priceChange24h: 1000,
    priceChangePercentage24h: 2,
    circulatingSupply: 19000000,
    totalSupply: 21000000,
    maxSupply: 21000000,
    lastUpdated: new Date(),
    image: 'https://assets.coingecko.com/coins/images/1/large/bitcoin.png'
  },
  {
    _id: '2',
    name: 'Ethereum',
    symbol: 'ETH',
    currentPrice: 3000,
    marketCap: 400000000000,
    volume24h: 20000000000,
    priceChange24h: 100,
    priceChangePercentage24h: 3.5,
    circulatingSupply: 120000000,
    totalSupply: 120000000,
    maxSupply: null,
    lastUpdated: new Date(),
    image: 'https://assets.coingecko.com/coins/images/279/large/ethereum.png'
  },
  {
    _id: '3',
    name: 'Cardano',
    symbol: 'ADA',
    currentPrice: 1.5,
    marketCap: 50000000000,
    volume24h: 2000000000,
    priceChange24h: 0.05,
    priceChangePercentage24h: 3.5,
    circulatingSupply: 33000000000,
    totalSupply: 45000000000,
    maxSupply: 45000000000,
    lastUpdated: new Date(),
    image: 'https://assets.coingecko.com/coins/images/975/large/cardano.png'
  },
  {
    _id: '4',
    name: 'Solana',
    symbol: 'SOL',
    currentPrice: 100,
    marketCap: 30000000000,
    volume24h: 1500000000,
    priceChange24h: 5,
    priceChangePercentage24h: 5,
    circulatingSupply: 300000000,
    totalSupply: 500000000,
    maxSupply: null,
    lastUpdated: new Date(),
    image: 'https://assets.coingecko.com/coins/images/4128/large/solana.png'
  },
  {
    _id: '5',
    name: 'Ripple',
    symbol: 'XRP',
    currentPrice: 0.75,
    marketCap: 35000000000,
    volume24h: 1800000000,
    priceChange24h: -0.02,
    priceChangePercentage24h: -2.5,
    circulatingSupply: 46000000000,
    totalSupply: 100000000000,
    maxSupply: 100000000000,
    lastUpdated: new Date(),
    image: 'https://assets.coingecko.com/coins/images/44/large/xrp-symbol-white-128.png'
  }
];

// Get all cryptos
app.get('/api/cryptos', (req, res) => {
  console.log('Cryptos endpoint accessed');
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.perPage) || 20;
    
    // Calculate pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedCryptos = mockCryptos.slice(startIndex, endIndex);
    
    const response = {
      success: true,
      data: {
        cryptos: paginatedCryptos,
        page,
        pages: Math.ceil(mockCryptos.length / limit),
        total: mockCryptos.length,
      },
    };
    
    console.log('Sending cryptos response');
    res.json(response);
  } catch (error) {
    console.error('Error fetching cryptos:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching cryptocurrencies',
      error: error.message
    });
  }
});

// Get trending cryptos
app.get('/api/cryptos/trending', (req, res) => {
  console.log('Trending endpoint accessed');
  try {
    // Use top cryptos as trending
    const trendingCryptos = mockCryptos.slice(0, 5).map(crypto => ({
      _id: crypto._id,
      name: crypto.name,
      symbol: crypto.symbol,
      image: crypto.image,
      rank: parseInt(crypto._id),
      priceBtc: crypto.currentPrice / 50000, // Approximate BTC value
      score: 6 - parseInt(crypto._id), // Higher score for lower ID
    }));
    
    const response = {
      success: true,
      data: trendingCryptos,
    };
    
    console.log('Sending trending response');
    res.json(response);
  } catch (error) {
    console.error('Error in trending cryptos endpoint:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching trending cryptocurrencies',
      error: error.message
    });
  }
});

// Get crypto by ID
app.get('/api/cryptos/:id', (req, res) => {
  console.log(`Crypto details endpoint accessed for ID: ${req.params.id}`);
  try {
    const coinId = req.params.id;
    
    // Find matching crypto or return 404
    const crypto = mockCryptos.find(c => c._id === coinId || c.symbol.toLowerCase() === coinId.toLowerCase());
    
    if (!crypto) {
      console.log(`Crypto with ID ${coinId} not found`);
      return res.status(404).json({
        success: false,
        message: `Cryptocurrency with ID ${coinId} not found`
      });
    }
    
    // Generate mock historical data
    const now = Date.now();
    const mockPrices = [];
    const mockMarketCaps = [];
    const mockVolumes = [];
    
    // Generate 30 days of mock data
    for (let i = 30; i >= 0; i--) {
      const timestamp = now - (i * 24 * 60 * 60 * 1000);
      const randomFactor = 0.9 + (Math.random() * 0.2); // Random between 0.9 and 1.1
      
      mockPrices.push([timestamp, crypto.currentPrice * randomFactor]);
      mockMarketCaps.push([timestamp, crypto.marketCap * randomFactor]);
      mockVolumes.push([timestamp, crypto.volume24h * randomFactor]);
    }
    
    // Create detailed response
    const detailedCrypto = {
      _id: crypto._id,
      name: crypto.name,
      symbol: crypto.symbol,
      description: `${crypto.name} is a cryptocurrency used for digital transactions.`,
      currentPrice: crypto.currentPrice,
      marketCap: crypto.marketCap,
      volume24h: crypto.volume24h,
      priceChange24h: crypto.priceChange24h,
      priceChangePercentage24h: crypto.priceChangePercentage24h,
      priceChangePercentage7d: crypto.priceChangePercentage24h * 2,
      priceChangePercentage30d: crypto.priceChangePercentage24h * 5,
      circulatingSupply: crypto.circulatingSupply,
      totalSupply: crypto.totalSupply,
      maxSupply: crypto.maxSupply,
      allTimeHigh: crypto.currentPrice * 2,
      allTimeHighDate: new Date(now - (90 * 24 * 60 * 60 * 1000)).toISOString(),
      allTimeLow: crypto.currentPrice * 0.3,
      allTimeLowDate: new Date(now - (300 * 24 * 60 * 60 * 1000)).toISOString(),
      lastUpdated: new Date().toISOString(),
      image: crypto.image,
      marketCapRank: parseInt(crypto._id),
      genesisDate: '2015-07-30',
      categories: ['Currency', 'Smart Contract Platform'],
      links: {
        homepage: `https://${crypto.name.toLowerCase()}.org`,
        blockchain_site: `https://explorer.${crypto.name.toLowerCase()}.org`,
        official_forum_url: `https://forum.${crypto.name.toLowerCase()}.org`,
        chat_url: `https://chat.${crypto.name.toLowerCase()}.org`,
        announcement_url: `https://blog.${crypto.name.toLowerCase()}.org`,
        twitter_screen_name: crypto.name.toLowerCase(),
        facebook_username: crypto.name.toLowerCase(),
        telegram_channel_identifier: crypto.name.toLowerCase(),
        subreddit_url: `https://reddit.com/r/${crypto.name.toLowerCase()}`,
        repos_url: { github: [`https://github.com/${crypto.name.toLowerCase()}`] },
      },
      marketData: {
        prices: mockPrices,
        market_caps: mockMarketCaps,
        total_volumes: mockVolumes,
      },
      communityData: {
        twitter_followers: 100000,
        reddit_subscribers: 50000,
        telegram_channel_user_count: 25000,
      },
      developerData: {
        forks: 100,
        stars: 500,
        subscribers: 200,
        total_issues: 50,
        closed_issues: 40,
        pull_requests_merged: 300,
        pull_request_contributors: 30,
        commit_count_4_weeks: 100,
      },
    };
    
    console.log(`Sending detailed response for ${crypto.name}`);
    res.json({
      success: true,
      data: detailedCrypto,
    });
  } catch (error) {
    console.error(`Error fetching crypto details:`, error);
    res.status(500).json({
      success: false,
      message: 'Error fetching cryptocurrency details',
      error: error.message
    });
  }
});

// AI insights endpoint
app.get('/api/ai/insights', (req, res) => {
  console.log('AI insights endpoint accessed');
  try {
    const insights = [
      {
        id: 1,
        title: "Bitcoin Price Prediction",
        content: "Based on current market trends and historical data, our AI model predicts Bitcoin may reach $75,000 by the end of the year. Key factors include institutional adoption and regulatory clarity.",
        confidence: 0.85,
        timestamp: new Date().toISOString(),
        tags: ["Bitcoin", "Price Prediction", "Institutional Adoption"]
      },
      {
        id: 2,
        title: "Ethereum Technical Analysis",
        content: "Ethereum is showing strong support at $2,800 with resistance at $3,200. The completion of the ETH 2.0 upgrade could be a significant catalyst for price movement in the coming months.",
        confidence: 0.78,
        timestamp: new Date().toISOString(),
        tags: ["Ethereum", "Technical Analysis", "ETH 2.0"]
      },
      {
        id: 3,
        title: "Market Sentiment Analysis",
        content: "Overall crypto market sentiment is currently neutral with a slight bullish bias. Social media analysis shows increasing interest in DeFi and NFT projects.",
        confidence: 0.72,
        timestamp: new Date().toISOString(),
        tags: ["Market Sentiment", "Social Media", "DeFi", "NFT"]
      }
    ];
    
    console.log('Sending AI insights response');
    res.json({
      success: true,
      data: insights
    });
  } catch (error) {
    console.error('Error in AI insights endpoint:', error);
    res.status(500).json({
      success: false,
      message: 'Error generating AI insights',
      error: error.message
    });
  }
});

// Start server
const PORT = 3001;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`API available at http://localhost:${PORT}/api`);
});
