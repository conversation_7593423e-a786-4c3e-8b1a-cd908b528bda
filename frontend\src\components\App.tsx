import React, { useState } from 'react';
import { Box } from '@chakra-ui/react';
import Header from './Header';
import HomePage from './HomePage';
import CryptoMarket from './CryptoMarket';

type Page = 'home' | 'market' | 'portfolio' | 'watchlist' | 'ai-insights';

const App: React.FC = () => {
  const [currentPage, setCurrentPage] = useState<Page>('home');

  const renderPage = () => {
    switch (currentPage) {
      case 'home':
        return <HomePage />;
      case 'market':
        return <CryptoMarket />;
      case 'portfolio':
        return <Box p={8}><h1>Portfolio (Coming Soon)</h1></Box>;
      case 'watchlist':
        return <Box p={8}><h1>Watchlist (Coming Soon)</h1></Box>;
      case 'ai-insights':
        return <Box p={8}><h1>AI Insights (Coming Soon)</h1></Box>;
      default:
        return <HomePage />;
    }
  };

  return (
    <Box>
      <Header currentPage={currentPage} onPageChange={setCurrentPage} />
      {renderPage()}
    </Box>
  );
};

export default App;
