const express = require('express');
const cors = require('cors');
const mongoose = require('mongoose');
const aiRoutes = require('./routes/aiRoutes');
const authRoutes = require('./routes/authRoutes');
const portfolioRoutes = require('./routes/portfolioRoutes');
const watchlistRoutes = require('./routes/watchlistRoutes');
const coinGeckoService = require('./services/coinGeckoService');
const WebSocket = require('ws');
const http = require('http');
const { helmetMiddleware, xssMiddleware, noCache } = require('./middleware/securityMiddleware');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// MongoDB connection string
const MONGO_URI = process.env.MONGO_URI || 'mongodb+srv://ziqra:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0';

// Connect to MongoDB
mongoose.connect(MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(() => console.log('MongoDB connected successfully'))
.catch(err => console.error('MongoDB connection error:', err));

// Create Express app
const app = express();

// Enable CORS for all routes
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3002', 'http://localhost:5173', 'http://127.0.0.1:3000', 'http://127.0.0.1:3002', 'http://127.0.0.1:5173'],
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  credentials: true,
  maxAge: 86400 // 24 hours
}));

// Add CORS preflight
app.options('*', cors());

// Security middleware
app.use(helmetMiddleware);
app.use(xssMiddleware);
app.use(noCache);

// Parse JSON bodies
app.use(express.json({ limit: '10kb' }));
app.use(express.urlencoded({ extended: true, limit: '10kb' }));

// Mock data
const mockCryptos = [
  {
    _id: '1',
    name: 'Bitcoin',
    symbol: 'BTC',
    currentPrice: 50000,
    marketCap: 1000000000000,
    volume24h: 50000000000,
    priceChange24h: 1000,
    priceChangePercentage24h: 2,
    circulatingSupply: 19000000,
    totalSupply: 21000000,
    maxSupply: 21000000,
    lastUpdated: new Date(),
  },
  {
    _id: '2',
    name: 'Ethereum',
    symbol: 'ETH',
    currentPrice: 3000,
    marketCap: 400000000000,
    volume24h: 20000000000,
    priceChange24h: 100,
    priceChangePercentage24h: 3.5,
    circulatingSupply: 120000000,
    totalSupply: 120000000,
    maxSupply: null,
    lastUpdated: new Date(),
  },
  {
    _id: '3',
    name: 'Cardano',
    symbol: 'ADA',
    currentPrice: 1.5,
    marketCap: 50000000000,
    volume24h: 2000000000,
    priceChange24h: 0.05,
    priceChangePercentage24h: 3.5,
    circulatingSupply: 33000000000,
    totalSupply: 45000000000,
    maxSupply: 45000000000,
    lastUpdated: new Date(),
  },
  {
    _id: '4',
    name: 'Solana',
    symbol: 'SOL',
    currentPrice: 100,
    marketCap: 30000000000,
    volume24h: 1500000000,
    priceChange24h: 5,
    priceChangePercentage24h: 5,
    circulatingSupply: 300000000,
    totalSupply: 500000000,
    maxSupply: null,
    lastUpdated: new Date(),
  },
  {
    _id: '5',
    name: 'Ripple',
    symbol: 'XRP',
    currentPrice: 0.75,
    marketCap: 35000000000,
    volume24h: 1800000000,
    priceChange24h: -0.02,
    priceChangePercentage24h: -2.5,
    circulatingSupply: 46000000000,
    totalSupply: 100000000000,
    maxSupply: 100000000000,
    lastUpdated: new Date(),
  },
];

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'API is running',
    status: 'ok'
  });
});

// API root endpoint
app.get('/api', (req, res) => {
  res.json({
    success: true,
    message: 'API is running',
    version: '1.0.0'
  });
});

// Get all cryptos from CoinGecko
app.get('/api/cryptos', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.perPage) || 20;
    const vsCurrency = req.query.vsCurrency || 'usd';
    const order = req.query.order || 'market_cap_desc';
    const sparkline = req.query.sparkline === 'true';

    let coins;
    let globalData;
    let useMockData = false;

    try {
      // Try to get data from CoinGecko
      coins = await coinGeckoService.getCoins({
        page,
        perPage: limit,
        vsCurrency,
        order,
        sparkline,
      });

      // Get global market data for total count
      globalData = await coinGeckoService.getGlobalData();
    } catch (apiError) {
      console.error('Error fetching from CoinGecko API, using mock data:', apiError);
      useMockData = true;
    }

    if (useMockData) {
      // Use mock data if API call failed
      console.log('Using mock cryptocurrency data');

      // Calculate pagination for mock data
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedMockCryptos = mockCryptos.slice(startIndex, endIndex);

      res.json({
        success: true,
        data: {
          cryptos: paginatedMockCryptos,
          page,
          pages: Math.ceil(mockCryptos.length / limit),
          total: mockCryptos.length,
        },
      });
      return;
    }

    // Transform CoinGecko data to match our API format
    const cryptos = coins.map(coin => ({
      _id: coin.id,
      name: coin.name,
      symbol: coin.symbol.toUpperCase(),
      currentPrice: coin.current_price,
      marketCap: coin.market_cap,
      volume24h: coin.total_volume,
      priceChange24h: coin.price_change_24h,
      priceChangePercentage24h: coin.price_change_percentage_24h,
      priceChangePercentage1h: coin.price_change_percentage_1h_in_currency,
      priceChangePercentage7d: coin.price_change_percentage_7d_in_currency,
      circulatingSupply: coin.circulating_supply,
      totalSupply: coin.total_supply,
      maxSupply: coin.max_supply,
      lastUpdated: coin.last_updated,
      image: coin.image,
      sparklineData: coin.sparkline_in_7d?.price || [],
      rank: coin.market_cap_rank,
    }));

    const total = globalData.data.active_cryptocurrencies;

    res.json({
      success: true,
      data: {
        cryptos,
        page,
        pages: Math.ceil(total / limit),
        total,
      },
    });
  } catch (error) {
    console.error('Error fetching cryptos:', error);

    // Return mock data as a fallback
    console.log('Using mock cryptocurrency data due to error');
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.perPage) || 20;

    // Calculate pagination for mock data
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedMockCryptos = mockCryptos.slice(startIndex, endIndex);

    res.json({
      success: true,
      data: {
        cryptos: paginatedMockCryptos,
        page,
        pages: Math.ceil(mockCryptos.length / limit),
        total: mockCryptos.length,
      },
    });
  }
});

// Get trending cryptos
app.get('/api/cryptos/trending', async (req, res) => {
  try {
    let trendingCoins;

    try {
      const trendingData = await coinGeckoService.getTrendingCoins();

      // Transform CoinGecko data to match our API format
      trendingCoins = trendingData.coins.map(item => ({
        _id: item.item.id,
        name: item.item.name,
        symbol: item.item.symbol.toUpperCase(),
        image: item.item.large,
        rank: item.item.market_cap_rank,
        priceBtc: item.item.price_btc,
        score: item.item.score,
      }));
    } catch (apiError) {
      console.error('Error fetching from CoinGecko API, using mock trending data:', apiError);

      // Use top 5 mock cryptos as trending
      trendingCoins = mockCryptos.slice(0, 5).map(crypto => ({
        _id: crypto._id,
        name: crypto.name,
        symbol: crypto.symbol,
        image: `https://cryptoicons.org/api/${crypto.symbol.toLowerCase()}/200`,
        rank: parseInt(crypto._id),
        priceBtc: crypto.currentPrice / 50000, // Approximate BTC value
        score: 6 - parseInt(crypto._id), // Higher score for lower ID
      }));
    }

    res.json({
      success: true,
      data: trendingCoins,
    });
  } catch (error) {
    console.error('Error in trending cryptos endpoint:', error);

    // Use top 5 mock cryptos as trending fallback
    const trendingCoins = mockCryptos.slice(0, 5).map(crypto => ({
      _id: crypto._id,
      name: crypto.name,
      symbol: crypto.symbol,
      image: `https://cryptoicons.org/api/${crypto.symbol.toLowerCase()}/200`,
      rank: parseInt(crypto._id),
      priceBtc: crypto.currentPrice / 50000, // Approximate BTC value
      score: 6 - parseInt(crypto._id), // Higher score for lower ID
    }));

    res.json({
      success: true,
      data: trendingCoins,
    });
  }
});

// Get crypto by ID from CoinGecko
app.get('/api/cryptos/:id', async (req, res) => {
  try {
    const coinId = req.params.id;
    let crypto;

    try {
      const coinData = await coinGeckoService.getCoinDetails(coinId);

      // Get historical market data
      const marketChart = await coinGeckoService.getCoinMarketChart(coinId, {
        days: '30',
        interval: 'daily',
      });

      // Transform CoinGecko data to match our API format
      crypto = {
        _id: coinData.id,
        name: coinData.name,
        symbol: coinData.symbol.toUpperCase(),
        description: coinData.description.en,
        currentPrice: coinData.market_data.current_price.usd,
        marketCap: coinData.market_data.market_cap.usd,
        volume24h: coinData.market_data.total_volume.usd,
        priceChange24h: coinData.market_data.price_change_24h,
        priceChangePercentage24h: coinData.market_data.price_change_percentage_24h,
        priceChangePercentage7d: coinData.market_data.price_change_percentage_7d,
        priceChangePercentage30d: coinData.market_data.price_change_percentage_30d,
        circulatingSupply: coinData.market_data.circulating_supply,
        totalSupply: coinData.market_data.total_supply,
        maxSupply: coinData.market_data.max_supply,
        allTimeHigh: coinData.market_data.ath.usd,
        allTimeHighDate: coinData.market_data.ath_date.usd,
        allTimeLow: coinData.market_data.atl.usd,
        allTimeLowDate: coinData.market_data.atl_date.usd,
        lastUpdated: coinData.last_updated,
        image: coinData.image.large,
        marketCapRank: coinData.market_cap_rank,
        genesisDate: coinData.genesis_date,
        categories: coinData.categories,
        links: {
          homepage: coinData.links.homepage[0],
          blockchain_site: coinData.links.blockchain_site.filter(Boolean)[0],
          official_forum_url: coinData.links.official_forum_url.filter(Boolean)[0],
          chat_url: coinData.links.chat_url.filter(Boolean)[0],
          announcement_url: coinData.links.announcement_url.filter(Boolean)[0],
          twitter_screen_name: coinData.links.twitter_screen_name,
          facebook_username: coinData.links.facebook_username,
          telegram_channel_identifier: coinData.links.telegram_channel_identifier,
          subreddit_url: coinData.links.subreddit_url,
          repos_url: coinData.links.repos_url,
        },
        marketData: {
          prices: marketChart.prices,
          market_caps: marketChart.market_caps,
          total_volumes: marketChart.total_volumes,
        },
        communityData: coinData.community_data,
        developerData: coinData.developer_data,
      };
    } catch (apiError) {
      console.error(`Error fetching from CoinGecko API for ${coinId}, using mock data:`, apiError);

      // Find matching mock crypto or use the first one
      const mockCrypto = mockCryptos.find(c => c._id === coinId || c.symbol.toLowerCase() === coinId.toLowerCase()) || mockCryptos[0];

      // Generate mock historical data
      const now = Date.now();
      const mockPrices = [];
      const mockMarketCaps = [];
      const mockVolumes = [];

      // Generate 30 days of mock data
      for (let i = 30; i >= 0; i--) {
        const timestamp = now - (i * 24 * 60 * 60 * 1000);
        const randomFactor = 0.9 + (Math.random() * 0.2); // Random between 0.9 and 1.1

        mockPrices.push([timestamp, mockCrypto.currentPrice * randomFactor]);
        mockMarketCaps.push([timestamp, mockCrypto.marketCap * randomFactor]);
        mockVolumes.push([timestamp, mockCrypto.volume24h * randomFactor]);
      }

      crypto = {
        _id: mockCrypto._id,
        name: mockCrypto.name,
        symbol: mockCrypto.symbol,
        description: `${mockCrypto.name} is a cryptocurrency used for digital transactions. This is mock data as the CoinGecko API is currently unavailable.`,
        currentPrice: mockCrypto.currentPrice,
        marketCap: mockCrypto.marketCap,
        volume24h: mockCrypto.volume24h,
        priceChange24h: mockCrypto.priceChange24h,
        priceChangePercentage24h: mockCrypto.priceChangePercentage24h,
        priceChangePercentage7d: mockCrypto.priceChangePercentage24h * 2,
        priceChangePercentage30d: mockCrypto.priceChangePercentage24h * 5,
        circulatingSupply: mockCrypto.circulatingSupply,
        totalSupply: mockCrypto.totalSupply,
        maxSupply: mockCrypto.maxSupply,
        allTimeHigh: mockCrypto.currentPrice * 2,
        allTimeHighDate: new Date(now - (90 * 24 * 60 * 60 * 1000)).toISOString(),
        allTimeLow: mockCrypto.currentPrice * 0.3,
        allTimeLowDate: new Date(now - (300 * 24 * 60 * 60 * 1000)).toISOString(),
        lastUpdated: new Date().toISOString(),
        image: `https://cryptoicons.org/api/${mockCrypto.symbol.toLowerCase()}/200`,
        marketCapRank: parseInt(mockCrypto._id),
        genesisDate: '2015-07-30',
        categories: ['Currency', 'Smart Contract Platform'],
        links: {
          homepage: `https://${mockCrypto.name.toLowerCase()}.org`,
          blockchain_site: `https://explorer.${mockCrypto.name.toLowerCase()}.org`,
          official_forum_url: `https://forum.${mockCrypto.name.toLowerCase()}.org`,
          chat_url: `https://chat.${mockCrypto.name.toLowerCase()}.org`,
          announcement_url: `https://blog.${mockCrypto.name.toLowerCase()}.org`,
          twitter_screen_name: mockCrypto.name.toLowerCase(),
          facebook_username: mockCrypto.name.toLowerCase(),
          telegram_channel_identifier: mockCrypto.name.toLowerCase(),
          subreddit_url: `https://reddit.com/r/${mockCrypto.name.toLowerCase()}`,
          repos_url: { github: [`https://github.com/${mockCrypto.name.toLowerCase()}`] },
        },
        marketData: {
          prices: mockPrices,
          market_caps: mockMarketCaps,
          total_volumes: mockVolumes,
        },
        communityData: {
          twitter_followers: 100000,
          reddit_subscribers: 50000,
          telegram_channel_user_count: 25000,
        },
        developerData: {
          forks: 100,
          stars: 500,
          subscribers: 200,
          total_issues: 50,
          closed_issues: 40,
          pull_requests_merged: 300,
          pull_request_contributors: 30,
          commit_count_4_weeks: 100,
        },
      };
    }

    res.json({
      success: true,
      data: crypto,
    });
  } catch (error) {
    console.error(`Error in crypto details endpoint for ${req.params.id}:`, error);

    // Find matching mock crypto or use the first one as fallback
    const coinId = req.params.id;
    const mockCrypto = mockCryptos.find(c => c._id === coinId || c.symbol.toLowerCase() === coinId.toLowerCase()) || mockCryptos[0];

    // Generate mock historical data
    const now = Date.now();
    const mockPrices = [];
    const mockMarketCaps = [];
    const mockVolumes = [];

    // Generate 30 days of mock data
    for (let i = 30; i >= 0; i--) {
      const timestamp = now - (i * 24 * 60 * 60 * 1000);
      const randomFactor = 0.9 + (Math.random() * 0.2); // Random between 0.9 and 1.1

      mockPrices.push([timestamp, mockCrypto.currentPrice * randomFactor]);
      mockMarketCaps.push([timestamp, mockCrypto.marketCap * randomFactor]);
      mockVolumes.push([timestamp, mockCrypto.volume24h * randomFactor]);
    }

    const crypto = {
      _id: mockCrypto._id,
      name: mockCrypto.name,
      symbol: mockCrypto.symbol,
      description: `${mockCrypto.name} is a cryptocurrency used for digital transactions. This is mock data as the CoinGecko API is currently unavailable.`,
      currentPrice: mockCrypto.currentPrice,
      marketCap: mockCrypto.marketCap,
      volume24h: mockCrypto.volume24h,
      priceChange24h: mockCrypto.priceChange24h,
      priceChangePercentage24h: mockCrypto.priceChangePercentage24h,
      priceChangePercentage7d: mockCrypto.priceChangePercentage24h * 2,
      priceChangePercentage30d: mockCrypto.priceChangePercentage24h * 5,
      circulatingSupply: mockCrypto.circulatingSupply,
      totalSupply: mockCrypto.totalSupply,
      maxSupply: mockCrypto.maxSupply,
      allTimeHigh: mockCrypto.currentPrice * 2,
      allTimeHighDate: new Date(now - (90 * 24 * 60 * 60 * 1000)).toISOString(),
      allTimeLow: mockCrypto.currentPrice * 0.3,
      allTimeLowDate: new Date(now - (300 * 24 * 60 * 60 * 1000)).toISOString(),
      lastUpdated: new Date().toISOString(),
      image: `https://cryptoicons.org/api/${mockCrypto.symbol.toLowerCase()}/200`,
      marketCapRank: parseInt(mockCrypto._id),
      genesisDate: '2015-07-30',
      categories: ['Currency', 'Smart Contract Platform'],
      links: {
        homepage: `https://${mockCrypto.name.toLowerCase()}.org`,
        blockchain_site: `https://explorer.${mockCrypto.name.toLowerCase()}.org`,
        official_forum_url: `https://forum.${mockCrypto.name.toLowerCase()}.org`,
        chat_url: `https://chat.${mockCrypto.name.toLowerCase()}.org`,
        announcement_url: `https://blog.${mockCrypto.name.toLowerCase()}.org`,
        twitter_screen_name: mockCrypto.name.toLowerCase(),
        facebook_username: mockCrypto.name.toLowerCase(),
        telegram_channel_identifier: mockCrypto.name.toLowerCase(),
        subreddit_url: `https://reddit.com/r/${mockCrypto.name.toLowerCase()}`,
        repos_url: { github: [`https://github.com/${mockCrypto.name.toLowerCase()}`] },
      },
      marketData: {
        prices: mockPrices,
        market_caps: mockMarketCaps,
        total_volumes: mockVolumes,
      },
      communityData: {
        twitter_followers: 100000,
        reddit_subscribers: 50000,
        telegram_channel_user_count: 25000,
      },
      developerData: {
        forks: 100,
        stars: 500,
        subscribers: 200,
        total_issues: 50,
        closed_issues: 40,
        pull_requests_merged: 300,
        pull_request_contributors: 30,
        commit_count_4_weeks: 100,
      },
    };

    res.json({
      success: true,
      data: crypto,
    });
  }
});

// Search cryptos
app.get('/api/cryptos/search/:query', async (req, res) => {
  try {
    const query = req.params.query.toLowerCase();
    let searchResults;

    try {
      searchResults = await coinGeckoService.searchCoins(query);
    } catch (apiError) {
      console.error(`Error fetching from CoinGecko API for search query ${query}, using mock data:`, apiError);

      // Filter mock cryptos based on query
      const filteredMockCryptos = mockCryptos.filter(crypto =>
        crypto.name.toLowerCase().includes(query) ||
        crypto.symbol.toLowerCase().includes(query)
      );

      // Format results to match CoinGecko API
      searchResults = {
        coins: filteredMockCryptos.map(crypto => ({
          id: crypto._id,
          name: crypto.name,
          symbol: crypto.symbol.toLowerCase(),
          market_cap_rank: parseInt(crypto._id),
          large: `https://cryptoicons.org/api/${crypto.symbol.toLowerCase()}/200`,
          thumb: `https://cryptoicons.org/api/${crypto.symbol.toLowerCase()}/60`,
        })),
        categories: [],
        exchanges: [],
        nfts: [],
      };
    }

    res.json({
      success: true,
      data: searchResults,
    });
  } catch (error) {
    console.error(`Error in search endpoint for query ${req.params.query}:`, error);

    // Filter mock cryptos based on query as fallback
    const query = req.params.query.toLowerCase();
    const filteredMockCryptos = mockCryptos.filter(crypto =>
      crypto.name.toLowerCase().includes(query) ||
      crypto.symbol.toLowerCase().includes(query)
    );

    // Format results to match CoinGecko API
    const searchResults = {
      coins: filteredMockCryptos.map(crypto => ({
        id: crypto._id,
        name: crypto.name,
        symbol: crypto.symbol.toLowerCase(),
        market_cap_rank: parseInt(crypto._id),
        large: `https://cryptoicons.org/api/${crypto.symbol.toLowerCase()}/200`,
        thumb: `https://cryptoicons.org/api/${crypto.symbol.toLowerCase()}/60`,
      })),
      categories: [],
      exchanges: [],
      nfts: [],
    };

    res.json({
      success: true,
      data: searchResults,
    });
  }
});

// Mount routes
app.use('/api/auth', authRoutes);
app.use('/api/portfolios', portfolioRoutes);
app.use('/api/watchlists', watchlistRoutes);
app.use('/api/ai', aiRoutes);

// Create HTTP server
const server = http.createServer(app);

// Create WebSocket server
const wss = new WebSocket.Server({ server });

// Store connected clients
const clients = new Set();

// WebSocket connection handler
wss.on('connection', (ws) => {
  // Add client to set
  clients.add(ws);
  console.log('WebSocket client connected');

  // Send initial data
  ws.send(JSON.stringify({ type: 'connection', message: 'Connected to WebSocket server' }));

  // Handle messages from client
  ws.on('message', (message) => {
    try {
      const data = JSON.parse(message);
      console.log('Received message:', data);

      // Handle subscription requests
      if (data.type === 'subscribe' && data.channel === 'prices') {
        ws.subscribed = true;
        ws.coins = data.coins || [];
        ws.send(JSON.stringify({
          type: 'subscribed',
          channel: 'prices',
          coins: ws.coins,
        }));
      }
    } catch (error) {
      console.error('Error processing message:', error);
    }
  });

  // Handle client disconnect
  ws.on('close', () => {
    clients.delete(ws);
    console.log('WebSocket client disconnected');
  });
});

// Simulate real-time price updates
const startPriceUpdates = () => {
  // Fetch initial prices
  let prices = {};

  // Update prices every 10 seconds
  setInterval(async () => {
    try {
      // Get top 100 coins
      const coins = await coinGeckoService.getCoins({
        page: 1,
        perPage: 100,
      });

      // Update prices
      coins.forEach(coin => {
        prices[coin.id] = {
          price: coin.current_price,
          change24h: coin.price_change_percentage_24h,
          volume: coin.total_volume,
          lastUpdated: new Date().toISOString(),
        };
      });

      // Send updates to subscribed clients
      clients.forEach(client => {
        if (client.readyState === WebSocket.OPEN && client.subscribed) {
          const coinUpdates = {};

          // If client specified coins, only send those
          if (client.coins && client.coins.length > 0) {
            client.coins.forEach(coinId => {
              if (prices[coinId]) {
                coinUpdates[coinId] = prices[coinId];
              }
            });
          } else {
            // Otherwise send all prices
            Object.assign(coinUpdates, prices);
          }

          client.send(JSON.stringify({
            type: 'priceUpdate',
            data: coinUpdates,
            timestamp: new Date().toISOString(),
          }));
        }
      });
    } catch (error) {
      console.error('Error updating prices:', error);
    }
  }, 10000); // Update every 10 seconds
};

// Add a health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    status: 'healthy',
    timestamp: new Date().toISOString(),
    message: 'Server is running properly'
  });
});

// Import error middleware
const { errorHandler } = require('./middleware/errorMiddleware');

// Add a fallback middleware for all other routes
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: 'Endpoint not found',
    path: req.path
  });
});

// Error handling middleware
app.use(errorHandler);

// Start server with error handling
const PORT = 3001;

try {
  // Check if port is in use
  server.on('error', (error) => {
    if (error.code === 'EADDRINUSE') {
      console.error(`Port ${PORT} is already in use. Please close the application using this port or use a different port.`);
    } else {
      console.error('Server error:', error);
    }
    process.exit(1);
  });

  // Start the server
  server.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
    console.log(`API available at http://localhost:${PORT}/api`);
    console.log(`WebSocket server available at ws://localhost:${PORT}`);

    // Start price updates after server is running
    try {
      startPriceUpdates();
      console.log('Price updates started successfully');
    } catch (error) {
      console.error('Failed to start price updates:', error);
      console.log('Using mock data for price updates');

      // Fallback to mock price updates
      setInterval(() => {
        try {
          // Create mock price updates
          const mockPrices = {};
          mockCryptos.forEach(crypto => {
            const randomChange = (Math.random() * 2 - 1) * 2; // Random between -2% and +2%
            const newPrice = crypto.currentPrice * (1 + randomChange / 100);

            mockPrices[crypto._id] = {
              price: newPrice,
              change24h: crypto.priceChangePercentage24h + randomChange,
              volume: crypto.volume24h * (1 + (Math.random() * 0.1 - 0.05)), // ±5% volume change
              lastUpdated: new Date().toISOString(),
            };
          });

          // Send updates to subscribed clients
          clients.forEach(client => {
            if (client.readyState === WebSocket.OPEN && client.subscribed) {
              const coinUpdates = {};

              // If client specified coins, only send those
              if (client.coins && client.coins.length > 0) {
                client.coins.forEach(coinId => {
                  if (mockPrices[coinId]) {
                    coinUpdates[coinId] = mockPrices[coinId];
                  }
                });
              } else {
                // Otherwise send all prices
                Object.assign(coinUpdates, mockPrices);
              }

              client.send(JSON.stringify({
                type: 'priceUpdate',
                data: coinUpdates,
                timestamp: new Date().toISOString(),
                isMock: true
              }));
            }
          });
        } catch (error) {
          console.error('Error in mock price updates:', error);
        }
      }, 10000); // Update every 10 seconds
    }
  });
} catch (error) {
  console.error('Failed to start server:', error);
}
