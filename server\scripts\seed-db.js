/**
 * Database Seeding Script
 * 
 * This script seeds the MongoDB database with initial cryptocurrency data.
 */

require('dotenv').config();
const mongoose = require('mongoose');
const axios = require('axios');
const Crypto = require('../models/Crypto');
const User = require('../models/User');
const Portfolio = require('../models/Portfolio');
const Watchlist = require('../models/Watchlist');

// MongoDB connection string
const MONGO_URI = process.env.MONGO_URI || 'mongodb+srv://ziqra:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0';

// Connect to MongoDB
const connectDB = async () => {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('MongoDB connected successfully!');
  } catch (error) {
    console.error('MongoDB connection error:', error.message);
    process.exit(1);
  }
};

// Fetch cryptocurrency data from CoinGecko API
const fetchCryptoData = async () => {
  try {
    console.log('Fetching cryptocurrency data from CoinGecko API...');
    const response = await axios.get(
      'https://api.coingecko.com/api/v3/coins/markets',
      {
        params: {
          vs_currency: 'usd',
          order: 'market_cap_desc',
          per_page: 100,
          page: 1,
          sparkline: true,
          price_change_percentage: '1h,24h,7d',
        },
      }
    );
    console.log(`Fetched ${response.data.length} cryptocurrencies`);
    return response.data;
  } catch (error) {
    console.error('Error fetching cryptocurrency data:', error.message);
    return null;
  }
};

// Format cryptocurrency data for MongoDB
const formatCryptoData = (data) => {
  return data.map((coin) => ({
    name: coin.name,
    symbol: coin.symbol,
    currentPrice: coin.current_price,
    marketCap: coin.market_cap,
    volume24h: coin.total_volume,
    priceChange24h: coin.price_change_24h,
    priceChangePercentage24h: coin.price_change_percentage_24h,
    priceChangePercentage1h: coin.price_change_percentage_1h_in_currency,
    priceChangePercentage7d: coin.price_change_percentage_7d_in_currency,
    circulatingSupply: coin.circulating_supply,
    totalSupply: coin.total_supply,
    maxSupply: coin.max_supply,
    rank: coin.market_cap_rank,
    image: coin.image,
    lastUpdated: new Date(coin.last_updated),
    sparklineData: coin.sparkline_in_7d?.price || [],
    coinGeckoId: coin.id,
  }));
};

// Seed cryptocurrencies
const seedCryptos = async () => {
  try {
    // Check if cryptocurrencies already exist
    const count = await Crypto.countDocuments();
    if (count > 0) {
      console.log(`Database already has ${count} cryptocurrencies. Skipping seeding.`);
      return;
    }

    // Fetch data from CoinGecko API
    const data = await fetchCryptoData();
    if (!data) {
      console.log('Using mock data instead...');
      // Use mock data if API fails
      const mockData = require('../data/mockCryptoData');
      await Crypto.insertMany(formatCryptoData(mockData));
      console.log(`Seeded ${mockData.length} cryptocurrencies from mock data`);
      return;
    }

    // Format and insert data
    const formattedData = formatCryptoData(data);
    await Crypto.insertMany(formattedData);
    console.log(`Seeded ${formattedData.length} cryptocurrencies from CoinGecko API`);
  } catch (error) {
    console.error('Error seeding cryptocurrencies:', error.message);
  }
};

// Create a test user
const createTestUser = async () => {
  try {
    // Check if test user already exists
    const existingUser = await User.findOne({ email: '<EMAIL>' });
    if (existingUser) {
      console.log('Test user already exists. Skipping creation.');
      return existingUser;
    }

    // Create test user
    const user = await User.create({
      username: 'testuser',
      email: '<EMAIL>',
      password: 'password123',
      role: 'user',
    });

    console.log('Created test user:', user.email);
    return user;
  } catch (error) {
    console.error('Error creating test user:', error.message);
    return null;
  }
};

// Create a test portfolio
const createTestPortfolio = async (userId) => {
  try {
    // Check if test portfolio already exists
    const existingPortfolio = await Portfolio.findOne({ user: userId, name: 'My Portfolio' });
    if (existingPortfolio) {
      console.log('Test portfolio already exists. Skipping creation.');
      return;
    }

    // Get some cryptocurrencies
    const bitcoin = await Crypto.findOne({ symbol: 'BTC' });
    const ethereum = await Crypto.findOne({ symbol: 'ETH' });
    const solana = await Crypto.findOne({ symbol: 'SOL' });

    if (!bitcoin || !ethereum || !solana) {
      console.log('Could not find required cryptocurrencies. Skipping portfolio creation.');
      return;
    }

    // Create portfolio
    const portfolio = await Portfolio.create({
      user: userId,
      name: 'My Portfolio',
      description: 'My cryptocurrency portfolio',
      isDefault: true,
      holdings: [
        {
          crypto: bitcoin._id,
          quantity: 0.5,
          averageBuyPrice: 40000,
        },
        {
          crypto: ethereum._id,
          quantity: 5,
          averageBuyPrice: 2500,
        },
        {
          crypto: solana._id,
          quantity: 20,
          averageBuyPrice: 100,
        },
      ],
      transactions: [
        {
          crypto: bitcoin._id,
          type: 'buy',
          quantity: 0.5,
          price: 40000,
          date: new Date('2023-01-01'),
        },
        {
          crypto: ethereum._id,
          type: 'buy',
          quantity: 5,
          price: 2500,
          date: new Date('2023-02-01'),
        },
        {
          crypto: solana._id,
          type: 'buy',
          quantity: 20,
          price: 100,
          date: new Date('2023-03-01'),
        },
      ],
    });

    // Calculate portfolio value
    await portfolio.calculateValue();

    console.log('Created test portfolio:', portfolio.name);
  } catch (error) {
    console.error('Error creating test portfolio:', error.message);
  }
};

// Create a test watchlist
const createTestWatchlist = async (userId) => {
  try {
    // Check if test watchlist already exists
    const existingWatchlist = await Watchlist.findOne({ user: userId, name: 'My Watchlist' });
    if (existingWatchlist) {
      console.log('Test watchlist already exists. Skipping creation.');
      return;
    }

    // Get some cryptocurrencies
    const cryptos = await Crypto.find().limit(10);
    if (cryptos.length === 0) {
      console.log('Could not find cryptocurrencies. Skipping watchlist creation.');
      return;
    }

    // Create watchlist
    const watchlist = await Watchlist.create({
      user: userId,
      name: 'My Watchlist',
      description: 'My cryptocurrency watchlist',
      isDefault: true,
      cryptos: cryptos.map((crypto) => crypto._id),
    });

    console.log('Created test watchlist:', watchlist.name);
  } catch (error) {
    console.error('Error creating test watchlist:', error.message);
  }
};

// Main function
const seedDatabase = async () => {
  try {
    // Connect to MongoDB
    await connectDB();

    // Seed cryptocurrencies
    await seedCryptos();

    // Create test user
    const user = await createTestUser();
    if (user) {
      // Create test portfolio
      await createTestPortfolio(user._id);

      // Create test watchlist
      await createTestWatchlist(user._id);
    }

    console.log('Database seeding completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Error seeding database:', error.message);
    process.exit(1);
  }
};

// Run the seeding function
seedDatabase();
