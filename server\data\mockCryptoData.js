/**
 * Mock Cryptocurrency Data
 * 
 * This file contains mock data for cryptocurrencies to be used as fallback
 * when the CoinGecko API is unavailable or rate limited.
 */

// Mock coins with market data
const mockCoins = [
  {
    id: 'bitcoin',
    symbol: 'btc',
    name: 'Bitcoin',
    image: 'https://assets.coingecko.com/coins/images/1/large/bitcoin.png',
    current_price: 50000,
    market_cap: 1000000000000,
    market_cap_rank: 1,
    total_volume: 50000000000,
    price_change_24h: 1000,
    price_change_percentage_24h: 2,
    price_change_percentage_1h_in_currency: 0.5,
    price_change_percentage_7d_in_currency: 5,
    circulating_supply: 19000000,
    total_supply: 21000000,
    max_supply: 21000000,
    last_updated: new Date().toISOString(),
  },
  {
    id: 'ethereum',
    symbol: 'eth',
    name: 'Ethereum',
    image: 'https://assets.coingecko.com/coins/images/279/large/ethereum.png',
    current_price: 3000,
    market_cap: 350000000000,
    market_cap_rank: 2,
    total_volume: 20000000000,
    price_change_24h: 100,
    price_change_percentage_24h: 3.5,
    price_change_percentage_1h_in_currency: 0.2,
    price_change_percentage_7d_in_currency: 7,
    circulating_supply: 120000000,
    total_supply: null,
    max_supply: null,
    last_updated: new Date().toISOString(),
  },
  {
    id: 'tether',
    symbol: 'usdt',
    name: 'Tether',
    image: 'https://assets.coingecko.com/coins/images/325/large/Tether.png',
    current_price: 1,
    market_cap: 80000000000,
    market_cap_rank: 3,
    total_volume: 60000000000,
    price_change_24h: 0,
    price_change_percentage_24h: 0.01,
    price_change_percentage_1h_in_currency: 0,
    price_change_percentage_7d_in_currency: 0.05,
    circulating_supply: 80000000000,
    total_supply: 80000000000,
    max_supply: null,
    last_updated: new Date().toISOString(),
  },
  {
    id: 'binancecoin',
    symbol: 'bnb',
    name: 'BNB',
    image: 'https://assets.coingecko.com/coins/images/825/large/bnb-icon2_2x.png',
    current_price: 400,
    market_cap: 65000000000,
    market_cap_rank: 4,
    total_volume: 2000000000,
    price_change_24h: 5,
    price_change_percentage_24h: 1.2,
    price_change_percentage_1h_in_currency: 0.1,
    price_change_percentage_7d_in_currency: 3,
    circulating_supply: 160000000,
    total_supply: 170000000,
    max_supply: 170000000,
    last_updated: new Date().toISOString(),
  },
  {
    id: 'solana',
    symbol: 'sol',
    name: 'Solana',
    image: 'https://assets.coingecko.com/coins/images/4128/large/solana.png',
    current_price: 100,
    market_cap: 40000000000,
    market_cap_rank: 5,
    total_volume: 3000000000,
    price_change_24h: 2,
    price_change_percentage_24h: 2,
    price_change_percentage_1h_in_currency: 0.3,
    price_change_percentage_7d_in_currency: 10,
    circulating_supply: 400000000,
    total_supply: 500000000,
    max_supply: null,
    last_updated: new Date().toISOString(),
  },
  {
    id: 'ripple',
    symbol: 'xrp',
    name: 'XRP',
    image: 'https://assets.coingecko.com/coins/images/44/large/xrp-symbol-white-128.png',
    current_price: 0.5,
    market_cap: 25000000000,
    market_cap_rank: 6,
    total_volume: 1500000000,
    price_change_24h: 0.01,
    price_change_percentage_24h: 2,
    price_change_percentage_1h_in_currency: 0.1,
    price_change_percentage_7d_in_currency: 5,
    circulating_supply: 50000000000,
    total_supply: 100000000000,
    max_supply: 100000000000,
    last_updated: new Date().toISOString(),
  },
  {
    id: 'cardano',
    symbol: 'ada',
    name: 'Cardano',
    image: 'https://assets.coingecko.com/coins/images/975/large/cardano.png',
    current_price: 0.4,
    market_cap: 15000000000,
    market_cap_rank: 7,
    total_volume: 500000000,
    price_change_24h: 0.01,
    price_change_percentage_24h: 2.5,
    price_change_percentage_1h_in_currency: 0.2,
    price_change_percentage_7d_in_currency: 6,
    circulating_supply: 35000000000,
    total_supply: 45000000000,
    max_supply: 45000000000,
    last_updated: new Date().toISOString(),
  },
  {
    id: 'dogecoin',
    symbol: 'doge',
    name: 'Dogecoin',
    image: 'https://assets.coingecko.com/coins/images/5/large/dogecoin.png',
    current_price: 0.08,
    market_cap: 12000000000,
    market_cap_rank: 8,
    total_volume: 800000000,
    price_change_24h: 0.002,
    price_change_percentage_24h: 2.5,
    price_change_percentage_1h_in_currency: 0.3,
    price_change_percentage_7d_in_currency: 8,
    circulating_supply: 140000000000,
    total_supply: null,
    max_supply: null,
    last_updated: new Date().toISOString(),
  },
  {
    id: 'polkadot',
    symbol: 'dot',
    name: 'Polkadot',
    image: 'https://assets.coingecko.com/coins/images/12171/large/polkadot.png',
    current_price: 6,
    market_cap: 8000000000,
    market_cap_rank: 9,
    total_volume: 400000000,
    price_change_24h: 0.2,
    price_change_percentage_24h: 3.5,
    price_change_percentage_1h_in_currency: 0.2,
    price_change_percentage_7d_in_currency: 9,
    circulating_supply: 1300000000,
    total_supply: 1400000000,
    max_supply: null,
    last_updated: new Date().toISOString(),
  },
  {
    id: 'matic-network',
    symbol: 'matic',
    name: 'Polygon',
    image: 'https://assets.coingecko.com/coins/images/4713/large/matic-token-icon.png',
    current_price: 0.8,
    market_cap: 7500000000,
    market_cap_rank: 10,
    total_volume: 350000000,
    price_change_24h: 0.03,
    price_change_percentage_24h: 4,
    price_change_percentage_1h_in_currency: 0.3,
    price_change_percentage_7d_in_currency: 12,
    circulating_supply: 9000000000,
    total_supply: 10000000000,
    max_supply: 10000000000,
    last_updated: new Date().toISOString(),
  },
];

// Generate more mock coins
for (let i = 0; i < 90; i++) {
  const price = Math.random() * 10 + 0.1;
  const marketCap = price * (Math.random() * 1000000000 + 10000000);
  const volume = marketCap * (Math.random() * 0.5 + 0.1);
  const priceChange = (Math.random() * 0.2 - 0.1) * price;
  const priceChangePercentage = (priceChange / price) * 100;
  
  mockCoins.push({
    id: `mock-coin-${i + 1}`,
    symbol: `mock${i + 1}`,
    name: `Mock Coin ${i + 1}`,
    image: `https://via.placeholder.com/64/4a90e2/ffffff?text=MC${i + 1}`,
    current_price: price,
    market_cap: marketCap,
    market_cap_rank: 11 + i,
    total_volume: volume,
    price_change_24h: priceChange,
    price_change_percentage_24h: priceChangePercentage,
    price_change_percentage_1h_in_currency: priceChangePercentage / 24,
    price_change_percentage_7d_in_currency: priceChangePercentage * 7,
    circulating_supply: marketCap / price,
    total_supply: (marketCap / price) * 1.2,
    max_supply: Math.random() > 0.5 ? (marketCap / price) * 2 : null,
    last_updated: new Date().toISOString(),
  });
}

// Mock global market data
const mockGlobalData = {
  data: {
    active_cryptocurrencies: 10000,
    upcoming_icos: 0,
    ongoing_icos: 50,
    ended_icos: 3375,
    markets: 642,
    total_market_cap: {
      btc: 43000000,
      eth: 652000000,
      ltc: 12400000000,
      bch: 6200000000,
      bnb: 5500000000,
      eos: 950000000000,
      xrp: 2200000000000,
      xlm: 10000000000000,
      link: 14000000000,
      dot: 1650000000000,
      yfi: 132000,
      usd: 2000000000000,
      aed: 7300000000000,
      ars: 172000000000000,
      aud: 2900000000000,
      bdt: 170000000000000,
      bhd: 750000000000,
      bmd: 2000000000000,
      brl: 11000000000000,
      cad: 2700000000000,
    },
    total_volume: {
      btc: 2100000,
      eth: 32000000,
      ltc: 610000000,
      bch: 300000000,
      bnb: 270000000,
      eos: 47000000000,
      xrp: 110000000000,
      xlm: 500000000000,
      link: 700000000,
      dot: 82000000000,
      yfi: 6500,
      usd: 100000000000,
      aed: 360000000000,
      ars: 8500000000000,
      aud: 140000000000,
      bdt: 8400000000000,
      bhd: 37000000000,
      bmd: 100000000000,
      brl: 540000000000,
      cad: 130000000000,
    },
    market_cap_percentage: {
      btc: 42.5,
      eth: 18.2,
      usdt: 4.1,
      bnb: 3.3,
      sol: 2.1,
      xrp: 1.3,
      ada: 0.8,
      doge: 0.6,
      dot: 0.4,
      matic: 0.4,
    },
    market_cap_change_percentage_24h_usd: 2.5,
    updated_at: Date.now(),
  },
};

// Mock trending coins data
const mockTrendingCoins = {
  coins: [
    {
      item: {
        id: 'bitcoin',
        coin_id: 1,
        name: 'Bitcoin',
        symbol: 'BTC',
        market_cap_rank: 1,
        thumb: 'https://assets.coingecko.com/coins/images/1/thumb/bitcoin.png',
        small: 'https://assets.coingecko.com/coins/images/1/small/bitcoin.png',
        large: 'https://assets.coingecko.com/coins/images/1/large/bitcoin.png',
        slug: 'bitcoin',
        price_btc: 1,
        score: 0,
      },
    },
    {
      item: {
        id: 'ethereum',
        coin_id: 279,
        name: 'Ethereum',
        symbol: 'ETH',
        market_cap_rank: 2,
        thumb: 'https://assets.coingecko.com/coins/images/279/thumb/ethereum.png',
        small: 'https://assets.coingecko.com/coins/images/279/small/ethereum.png',
        large: 'https://assets.coingecko.com/coins/images/279/large/ethereum.png',
        slug: 'ethereum',
        price_btc: 0.06,
        score: 1,
      },
    },
    {
      item: {
        id: 'solana',
        coin_id: 4128,
        name: 'Solana',
        symbol: 'SOL',
        market_cap_rank: 5,
        thumb: 'https://assets.coingecko.com/coins/images/4128/thumb/solana.png',
        small: 'https://assets.coingecko.com/coins/images/4128/small/solana.png',
        large: 'https://assets.coingecko.com/coins/images/4128/large/solana.png',
        slug: 'solana',
        price_btc: 0.002,
        score: 2,
      },
    },
    {
      item: {
        id: 'binancecoin',
        coin_id: 825,
        name: 'BNB',
        symbol: 'BNB',
        market_cap_rank: 4,
        thumb: 'https://assets.coingecko.com/coins/images/825/thumb/bnb-icon2_2x.png',
        small: 'https://assets.coingecko.com/coins/images/825/small/bnb-icon2_2x.png',
        large: 'https://assets.coingecko.com/coins/images/825/large/bnb-icon2_2x.png',
        slug: 'binancecoin',
        price_btc: 0.008,
        score: 3,
      },
    },
    {
      item: {
        id: 'cardano',
        coin_id: 975,
        name: 'Cardano',
        symbol: 'ADA',
        market_cap_rank: 7,
        thumb: 'https://assets.coingecko.com/coins/images/975/thumb/cardano.png',
        small: 'https://assets.coingecko.com/coins/images/975/small/cardano.png',
        large: 'https://assets.coingecko.com/coins/images/975/large/cardano.png',
        slug: 'cardano',
        price_btc: 0.00008,
        score: 4,
      },
    },
    {
      item: {
        id: 'ripple',
        coin_id: 44,
        name: 'XRP',
        symbol: 'XRP',
        market_cap_rank: 6,
        thumb: 'https://assets.coingecko.com/coins/images/44/thumb/xrp-symbol-white-128.png',
        small: 'https://assets.coingecko.com/coins/images/44/small/xrp-symbol-white-128.png',
        large: 'https://assets.coingecko.com/coins/images/44/large/xrp-symbol-white-128.png',
        slug: 'ripple',
        price_btc: 0.00001,
        score: 5,
      },
    },
    {
      item: {
        id: 'dogecoin',
        coin_id: 5,
        name: 'Dogecoin',
        symbol: 'DOGE',
        market_cap_rank: 8,
        thumb: 'https://assets.coingecko.com/coins/images/5/thumb/dogecoin.png',
        small: 'https://assets.coingecko.com/coins/images/5/small/dogecoin.png',
        large: 'https://assets.coingecko.com/coins/images/5/large/dogecoin.png',
        slug: 'dogecoin',
        price_btc: 0.0000016,
        score: 6,
      },
    },
  ],
  exchanges: [],
};

module.exports = {
  mockCoins,
  mockGlobalData,
  mockTrendingCoins,
};
