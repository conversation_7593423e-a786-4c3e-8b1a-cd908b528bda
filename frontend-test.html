<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            font-weight: bold;
            margin-bottom: 10px;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .pending {
            background-color: #fff3cd;
            color: #856404;
        }
        button {
            padding: 8px 16px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0069d9;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Crypto Tracker Frontend Test</h1>
    
    <div class="test-section">
        <div class="test-title">Frontend Server (http://localhost:3002)</div>
        <button onclick="testFrontendServer()">Test Frontend Server</button>
        <div id="frontend-result" class="test-result pending">Click the button to test the frontend server</div>
        <pre id="frontend-details" style="display: none;"></pre>
    </div>
    
    <script>
        // Test the frontend server
        async function testFrontendServer() {
            const resultElement = document.getElementById('frontend-result');
            const detailsElement = document.getElementById('frontend-details');
            
            resultElement.className = 'test-result pending';
            resultElement.textContent = 'Testing frontend server...';
            detailsElement.style.display = 'none';
            
            try {
                // Since we're just checking if the server responds,
                // we'll use no-cors mode to avoid CORS issues
                const response = await fetch('http://localhost:3002/', {
                    method: 'GET',
                    mode: 'no-cors',
                    cache: 'no-cache',
                    timeout: 5000
                });
                
                resultElement.className = 'test-result success';
                resultElement.textContent = 'Frontend server is running!';
                detailsElement.textContent = 'The frontend server is accessible at http://localhost:3002';
                detailsElement.style.display = 'block';
                
                // Add a link to open the frontend
                const link = document.createElement('a');
                link.href = 'http://localhost:3002';
                link.textContent = 'Open Crypto Tracker Frontend';
                link.target = '_blank';
                link.style.display = 'block';
                link.style.marginTop = '10px';
                link.style.padding = '8px 16px';
                link.style.backgroundColor = '#28a745';
                link.style.color = 'white';
                link.style.textDecoration = 'none';
                link.style.borderRadius = '4px';
                link.style.textAlign = 'center';
                
                detailsElement.appendChild(document.createElement('br'));
                detailsElement.appendChild(link);
            } catch (error) {
                resultElement.className = 'test-result error';
                resultElement.textContent = 'Frontend server is not running or not accessible';
                detailsElement.textContent = error.toString();
                detailsElement.style.display = 'block';
                
                // Add instructions to start the frontend
                const instructions = document.createElement('div');
                instructions.innerHTML = `
                    <h3>How to start the frontend:</h3>
                    <ol>
                        <li>Open a command prompt or terminal</li>
                        <li>Navigate to the frontend directory: <code>cd frontend</code></li>
                        <li>Install dependencies: <code>npm install</code></li>
                        <li>Start the development server: <code>npm run dev</code></li>
                        <li>The frontend should be available at <a href="http://localhost:3002" target="_blank">http://localhost:3002</a></li>
                    </ol>
                    <p>Alternatively, you can run the <code>run-frontend.bat</code> script to start the frontend automatically.</p>
                `;
                
                detailsElement.appendChild(document.createElement('br'));
                detailsElement.appendChild(instructions);
            }
        }
        
        // Run the test automatically
        setTimeout(() => {
            testFrontendServer();
        }, 1000);
    </script>
</body>
</html>
