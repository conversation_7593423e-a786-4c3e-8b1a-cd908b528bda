{"version": 3, "sources": ["../src/index.ts", "../src/attrs.ts", "../src/is-html-element.ts", "../src/contains.ts", "../src/create-scope.ts", "../src/is-document.ts", "../src/is-shadow-root.ts", "../src/env.ts", "../src/get-active-element.ts", "../src/get-before-input-value.ts", "../src/get-by-id.ts", "../src/get-by-text.ts", "../src/get-by-typeahead.ts", "../src/get-computed-style.ts", "../src/get-event-target.ts", "../src/get-scroll-parent.ts", "../src/is-editable-element.ts", "../src/platform.ts", "../src/query.ts", "../src/raf.ts"], "sourcesContent": ["export * from \"./attrs\"\nexport * from \"./contains\"\nexport * from \"./create-scope\"\nexport * from \"./env\"\nexport * from \"./get-active-element\"\nexport * from \"./get-before-input-value\"\nexport * from \"./get-by-id\"\nexport * from \"./get-by-text\"\nexport * from \"./get-by-typeahead\"\nexport * from \"./get-computed-style\"\nexport * from \"./get-event-target\"\nexport * from \"./get-scroll-parent\"\nexport * from \"./is-editable-element\"\nexport * from \"./is-html-element\"\nexport * from \"./platform\"\nexport * from \"./query\"\nexport * from \"./raf\"\n\nexport const MAX_Z_INDEX = 2147483647\n", "type Booleanish = boolean | \"true\" | \"false\"\n\nexport const dataAttr = (guard: boolean | undefined) => {\n  return (guard ? \"\" : undefined) as Booleani<PERSON>\n}\n\nexport const ariaAttr = (guard: boolean | undefined) => {\n  return guard ? \"true\" : undefined\n}\n", "export const isHTMLElement = (v: any): v is HTMLElement =>\n  typeof v === \"object\" && v?.nodeType === Node.ELEMENT_NODE && typeof v?.nodeName === \"string\"\n", "import { isHTMLElement } from \"./is-html-element\"\n\ntype Target = HTMLElement | EventTarget | null | undefined\n\nexport function contains(parent: Target, child: Target) {\n  if (!parent || !child) return false\n  if (!isHTMLElement(parent) || !isHTMLElement(child)) return false\n  return parent === child || parent.contains(child)\n}\n\nexport const isSelfEvent = (event: Pick<UIEvent, \"currentTarget\" | \"target\">) =>\n  contains(event.currentTarget, event.target)\n", "type Ctx = { getRootNode?: () => Document | ShadowRoot | Node }\n\nconst getDocument = (node: Document | ShadowRoot | Node) => {\n  if (node.nodeType === Node.DOCUMENT_NODE) return node as Document\n  return node.ownerDocument ?? document\n}\n\nexport function createScope<T>(methods: T) {\n  const screen = {\n    getRootNode: (ctx: Ctx) => (ctx.getRootNode?.() ?? document) as Document | ShadowRoot,\n    getDoc: (ctx: Ctx) => getDocument(screen.getRootNode(ctx)),\n    getWin: (ctx: Ctx) => screen.getDoc(ctx).defaultView ?? window,\n    getActiveElement: (ctx: Ctx) => screen.getDoc(ctx).activeElement as HTMLElement | null,\n    isActiveElement: (ctx: Ctx, elem: HTMLElement | null) => elem === screen.getActiveElement(ctx),\n    focus(ctx: Ctx, elem: HTMLElement | null | undefined) {\n      if (elem == null) return\n      if (!screen.isActiveElement(ctx, elem)) elem.focus({ preventScroll: true })\n    },\n    getById: <T extends HTMLElement = HTMLElement>(ctx: Ctx, id: string) =>\n      screen.getRootNode(ctx).getElementById(id) as T | null,\n    setValue: <T extends { value: string }>(elem: T | null, value: string | number | null | undefined) => {\n      if (elem == null || value == null) return\n      const valueAsString = value.toString()\n      if (elem.value === valueAsString) return\n      elem.value = value.toString()\n    },\n  }\n  return { ...screen, ...methods }\n}\n", "export const isDocument = (el: any): el is Document => el.nodeType === Node.DOCUMENT_NODE\n", "const isNode = (el: any): el is Node => el.nodeType !== undefined\n\nexport const isShadowRoot = (el: any): el is ShadowRoot =>\n  el && isNode(el) && el.nodeType === Node.DOCUMENT_FRAGMENT_NODE && \"host\" in el\n", "import { isDocument } from \"./is-document\"\nimport { isHTMLElement } from \"./is-html-element\"\nimport { isShadowRoot } from \"./is-shadow-root\"\n\nexport function getDocument(el: Element | Node | Document | null) {\n  if (isDocument(el)) return el\n  return el?.ownerDocument ?? document\n}\n\nexport function getWindow(el: Node | ShadowRoot | Document | undefined) {\n  if (isShadowRoot(el)) return getWindow(el.host)\n  if (isDocument(el)) return el.defaultView ?? window\n  if (isHTMLElement(el)) return el.ownerDocument?.defaultView ?? window\n  return window\n}\n", "export function getActiveElement(el: HTMLElement): HTMLElement | null {\n  let activeElement = el.ownerDocument.activeElement as HTMLElement | null\n\n  while (activeElement?.shadowRoot) {\n    const el = activeElement.shadowRoot.activeElement as HTMLElement | null\n    if (el === activeElement) break\n    else activeElement = el\n  }\n\n  return activeElement\n}\n", "export function getBeforeInputValue(event: Pick<InputEvent, \"currentTarget\">) {\n  const { selectionStart, selectionEnd, value } = event.currentTarget as HTMLInputElement\n  return value.slice(0, selectionStart!) + (event as any).data + value.slice(selectionEnd!)\n}\n", "export function itemById<T extends HTMLElement>(v: T[], id: string) {\n  return v.find((node) => node.id === id)\n}\n\nexport function indexOfId<T extends HTMLElement>(v: T[], id: string) {\n  const item = itemById(v, id)\n  return item ? v.indexOf(item) : -1\n}\n\nexport function nextById<T extends HTMLElement>(v: T[], id: string, loop = true) {\n  let idx = indexOfId(v, id)\n  idx = loop ? (idx + 1) % v.length : Math.min(idx + 1, v.length - 1)\n  return v[idx]\n}\n\nexport function prevById<T extends HTMLElement>(v: T[], id: string, loop = true) {\n  let idx = indexOfId(v, id)\n  if (idx === -1) return loop ? v[v.length - 1] : null\n  idx = loop ? (idx - 1 + v.length) % v.length : Math.max(0, idx - 1)\n  return v[idx]\n}\n", "import { indexOfId } from \"./get-by-id\"\n\nconst getValueText = <T extends HTMLElement>(item: T) => item.dataset.valuetext ?? item.textContent ?? \"\"\n\nconst match = (valueText: string, query: string) => valueText.trim().toLowerCase().startsWith(query.toLowerCase())\n\nconst wrap = <T>(v: T[], idx: number) => {\n  return v.map((_, index) => v[(Math.max(idx, 0) + index) % v.length])\n}\n\nexport function getByText<T extends HTMLElement>(v: T[], text: string, currentId?: string | null) {\n  const index = currentId ? indexOfId(v, currentId) : -1\n  let items = currentId ? wrap(v, index) : v\n\n  const isSingleKey = text.length === 1\n\n  if (isSingleKey) {\n    items = items.filter((item) => item.id !== currentId)\n  }\n\n  return items.find((item) => match(getValueText(item), text))\n}\n", "import { getByText } from \"./get-by-text\"\n\nexport interface TypeaheadState {\n  keysSoFar: string\n  timer: number\n}\n\nexport interface TypeaheadOptions {\n  state: TypeaheadState\n  activeId: string | null\n  key: string\n  timeout?: number\n}\n\nfunction getByTypeaheadImpl<T extends HTMLElement>(_items: T[], options: TypeaheadOptions) {\n  const { state, activeId, key, timeout = 350 } = options\n\n  const search = state.keysSoFar + key\n  const isRepeated = search.length > 1 && Array.from(search).every((char) => char === search[0])\n\n  const query = isRepeated ? search[0] : search\n\n  let items = _items.slice()\n\n  const next = getByText(items, query, activeId)\n\n  function cleanup() {\n    clearTimeout(state.timer)\n    state.timer = -1\n  }\n\n  function update(value: string) {\n    state.keysSoFar = value\n    cleanup()\n\n    if (value !== \"\") {\n      state.timer = +setTimeout(() => {\n        update(\"\")\n        cleanup()\n      }, timeout)\n    }\n  }\n\n  update(search)\n\n  return next\n}\nexport const getByTypeahead = /*#__PURE__*/ Object.assign(getByTypeaheadImpl, {\n  defaultOptions: { keysSoFar: \"\", timer: -1 },\n  isValidEvent: isValidTypeaheadEvent,\n})\n\nfunction isValidTypeaheadEvent(event: Pick<KeyboardEvent, \"key\" | \"ctrlKey\" | \"metaKey\">) {\n  return event.key.length === 1 && !event.ctrlKey && !event.metaKey\n}\n", "const styleCache = new WeakMap<HTMLElement, any>()\n\nexport function getComputedStyle(el: HTMLElement) {\n  if (!styleCache.has(el)) {\n    const win = el.ownerDocument.defaultView || window\n    styleCache.set(el, win.getComputedStyle(el))\n  }\n  return styleCache.get(el)\n}\n", "export function getEventTarget<T extends EventTarget>(event: Event): T | null {\n  return (event.composedPath?.()[0] ?? event.target) as T | null\n}\n", "import { isHTMLElement } from \"./is-html-element\"\n\nfunction isScrollParent(el: HTMLElement): boolean {\n  const win = el.ownerDocument.defaultView || window\n  const { overflow, overflowX, overflowY } = win.getComputedStyle(el)\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX)\n}\n\nexport function getParent(el: HTMLElement): HTMLElement {\n  if (el.localName === \"html\") return el\n  return el.assignedSlot || el.parentElement || el.ownerDocument.documentElement\n}\n\nexport function getScrollParent(el: HTMLElement): HTMLElement {\n  if ([\"html\", \"body\", \"#document\"].includes(el.localName)) {\n    return el.ownerDocument.body\n  }\n\n  if (isHTMLElement(el) && isScrollParent(el)) {\n    return el\n  }\n\n  return getScrollParent(getParent(el))\n}\n\ntype Target = Array<VisualViewport | Window | HTMLElement | null>\n\nexport function getScrollParents(el: HTMLElement, list: Target = []): Target {\n  const parent = getScrollParent(el)\n  const isBody = parent === el.ownerDocument.body\n  const win = parent.ownerDocument.defaultView || window\n\n  //@ts-expect-error\n  const target = isBody ? [win].concat(win.visualViewport || [], isScrollParent(parent) ? parent : []) : parent\n\n  const parents = list.concat(target)\n  return isBody ? parents : parents.concat(getScrollParents(getParent(<HTMLElement>target)))\n}\n", "import { isHTMLElement } from \"./is-html-element\"\n\nexport function isEditableElement(el: HTMLElement | EventTarget | null) {\n  if (el == null || !isHTMLElement(el)) {\n    return false\n  }\n\n  try {\n    const win = el.ownerDocument.defaultView || window\n    return (\n      (el instanceof win.HTMLInputElement && el.selectionStart != null) ||\n      /(textarea|select)/.test(el.localName) ||\n      el.isContentEditable\n    )\n  } catch {\n    return false\n  }\n}\n", "export const isDom = () => typeof document !== \"undefined\"\n\nexport function getPlatform() {\n  const agent = (navigator as any).userAgentData\n  return agent?.platform ?? navigator.platform\n}\n\nconst pt = (v: RegExp) => isDom() && v.test(getPlatform())\nconst ua = (v: RegExp) => isDom() && v.test(navigator.userAgent)\nconst vn = (v: RegExp) => isDom() && v.test(navigator.vendor)\n\nexport const isTouchDevice = () => isDom() && !!navigator.maxTouchPoints\nexport const isMac = () => pt(/^Mac/) && !isTouchDevice()\nexport const isIPhone = () => pt(/^iPhone/)\nexport const isSafari = () => isApple() && vn(/apple/i)\nexport const isFirefox = () => ua(/firefox\\//i)\nexport const isApple = () => pt(/mac|iphone|ipad|ipod/i)\nexport const isIos = () => isApple() && !isMac()\n", "type Root = Document | Element | null | undefined\n\nexport function queryAll<T extends HTMLElement = HTMLElement>(root: Root, selector: string) {\n  return Array.from(root?.querySelectorAll<T>(selector) ?? [])\n}\n\nexport function query<T extends HTMLElement = HTMLElement>(root: Root, selector: string) {\n  return root?.querySelector<T>(selector)\n}\n", "export function nextTick(fn: VoidFunction) {\n  const set = new Set<VoidFunction>()\n  function raf(fn: VoidFunction) {\n    const id = globalThis.requestAnimationFrame(fn)\n    set.add(() => globalThis.cancelAnimationFrame(id))\n  }\n  raf(() => raf(fn))\n  return function cleanup() {\n    set.forEach((fn) => fn())\n  }\n}\n\nexport function raf(fn: VoidFunction) {\n  const id = globalThis.requestAnimationFrame(fn)\n  return () => {\n    globalThis.cancelAnimationFrame(id)\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBAAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACEO,IAAM,WAAW,CAAC,UAA+B;AACtD,SAAQ,QAAQ,KAAK;AACvB;AAEO,IAAM,WAAW,CAAC,UAA+B;AACtD,SAAO,QAAQ,SAAS;AAC1B;;;ACRO,IAAM,gBAAgB,CAAC,MAC5B,OAAO,MAAM,YAAY,GAAG,aAAa,KAAK,gBAAgB,OAAO,GAAG,aAAa;;;ACGhF,SAAS,SAAS,QAAgB,OAAe;AACtD,MAAI,CAAC,UAAU,CAAC;AAAO,WAAO;AAC9B,MAAI,CAAC,cAAc,MAAM,KAAK,CAAC,cAAc,KAAK;AAAG,WAAO;AAC5D,SAAO,WAAW,SAAS,OAAO,SAAS,KAAK;AAClD;AAEO,IAAM,cAAc,CAAC,UAC1B,SAAS,MAAM,eAAe,MAAM,MAAM;;;ACT5C,IAAM,cAAc,CAAC,SAAuC;AAC1D,MAAI,KAAK,aAAa,KAAK;AAAe,WAAO;AACjD,SAAO,KAAK,iBAAiB;AAC/B;AAEO,SAAS,YAAe,SAAY;AACzC,QAAM,SAAS;AAAA,IACb,aAAa,CAAC,QAAc,IAAI,cAAc,KAAK;AAAA,IACnD,QAAQ,CAAC,QAAa,YAAY,OAAO,YAAY,GAAG,CAAC;AAAA,IACzD,QAAQ,CAAC,QAAa,OAAO,OAAO,GAAG,EAAE,eAAe;AAAA,IACxD,kBAAkB,CAAC,QAAa,OAAO,OAAO,GAAG,EAAE;AAAA,IACnD,iBAAiB,CAAC,KAAU,SAA6B,SAAS,OAAO,iBAAiB,GAAG;AAAA,IAC7F,MAAM,KAAU,MAAsC;AACpD,UAAI,QAAQ;AAAM;AAClB,UAAI,CAAC,OAAO,gBAAgB,KAAK,IAAI;AAAG,aAAK,MAAM,EAAE,eAAe,KAAK,CAAC;AAAA,IAC5E;AAAA,IACA,SAAS,CAAsC,KAAU,OACvD,OAAO,YAAY,GAAG,EAAE,eAAe,EAAE;AAAA,IAC3C,UAAU,CAA8B,MAAgB,UAA8C;AACpG,UAAI,QAAQ,QAAQ,SAAS;AAAM;AACnC,YAAM,gBAAgB,MAAM,SAAS;AACrC,UAAI,KAAK,UAAU;AAAe;AAClC,WAAK,QAAQ,MAAM,SAAS;AAAA,IAC9B;AAAA,EACF;AACA,SAAO,EAAE,GAAG,QAAQ,GAAG,QAAQ;AACjC;;;AC5BO,IAAM,aAAa,CAAC,OAA4B,GAAG,aAAa,KAAK;;;ACA5E,IAAM,SAAS,CAAC,OAAwB,GAAG,aAAa;AAEjD,IAAM,eAAe,CAAC,OAC3B,MAAM,OAAO,EAAE,KAAK,GAAG,aAAa,KAAK,0BAA0B,UAAU;;;ACCxE,SAASC,aAAY,IAAsC;AAChE,MAAI,WAAW,EAAE;AAAG,WAAO;AAC3B,SAAO,IAAI,iBAAiB;AAC9B;AAEO,SAAS,UAAU,IAA8C;AACtE,MAAI,aAAa,EAAE;AAAG,WAAO,UAAU,GAAG,IAAI;AAC9C,MAAI,WAAW,EAAE;AAAG,WAAO,GAAG,eAAe;AAC7C,MAAI,cAAc,EAAE;AAAG,WAAO,GAAG,eAAe,eAAe;AAC/D,SAAO;AACT;;;ACdO,SAAS,iBAAiB,IAAqC;AACpE,MAAI,gBAAgB,GAAG,cAAc;AAErC,SAAO,eAAe,YAAY;AAChC,UAAMC,MAAK,cAAc,WAAW;AACpC,QAAIA,QAAO;AAAe;AAAA;AACrB,sBAAgBA;AAAA,EACvB;AAEA,SAAO;AACT;;;ACVO,SAAS,oBAAoB,OAA0C;AAC5E,QAAM,EAAE,gBAAgB,cAAc,MAAM,IAAI,MAAM;AACtD,SAAO,MAAM,MAAM,GAAG,cAAe,IAAK,MAAc,OAAO,MAAM,MAAM,YAAa;AAC1F;;;ACHO,SAAS,SAAgC,GAAQ,IAAY;AAClE,SAAO,EAAE,KAAK,CAAC,SAAS,KAAK,OAAO,EAAE;AACxC;AAEO,SAAS,UAAiC,GAAQ,IAAY;AACnE,QAAM,OAAO,SAAS,GAAG,EAAE;AAC3B,SAAO,OAAO,EAAE,QAAQ,IAAI,IAAI;AAClC;AAEO,SAAS,SAAgC,GAAQ,IAAY,OAAO,MAAM;AAC/E,MAAI,MAAM,UAAU,GAAG,EAAE;AACzB,QAAM,QAAQ,MAAM,KAAK,EAAE,SAAS,KAAK,IAAI,MAAM,GAAG,EAAE,SAAS,CAAC;AAClE,SAAO,EAAE,GAAG;AACd;AAEO,SAAS,SAAgC,GAAQ,IAAY,OAAO,MAAM;AAC/E,MAAI,MAAM,UAAU,GAAG,EAAE;AACzB,MAAI,QAAQ;AAAI,WAAO,OAAO,EAAE,EAAE,SAAS,CAAC,IAAI;AAChD,QAAM,QAAQ,MAAM,IAAI,EAAE,UAAU,EAAE,SAAS,KAAK,IAAI,GAAG,MAAM,CAAC;AAClE,SAAO,EAAE,GAAG;AACd;;;AClBA,IAAM,eAAe,CAAwB,SAAY,KAAK,QAAQ,aAAa,KAAK,eAAe;AAEvG,IAAM,QAAQ,CAAC,WAAmBC,WAAkB,UAAU,KAAK,EAAE,YAAY,EAAE,WAAWA,OAAM,YAAY,CAAC;AAEjH,IAAM,OAAO,CAAI,GAAQ,QAAgB;AACvC,SAAO,EAAE,IAAI,CAAC,GAAG,UAAU,GAAG,KAAK,IAAI,KAAK,CAAC,IAAI,SAAS,EAAE,MAAM,CAAC;AACrE;AAEO,SAAS,UAAiC,GAAQ,MAAc,WAA2B;AAChG,QAAM,QAAQ,YAAY,UAAU,GAAG,SAAS,IAAI;AACpD,MAAI,QAAQ,YAAY,KAAK,GAAG,KAAK,IAAI;AAEzC,QAAM,cAAc,KAAK,WAAW;AAEpC,MAAI,aAAa;AACf,YAAQ,MAAM,OAAO,CAAC,SAAS,KAAK,OAAO,SAAS;AAAA,EACtD;AAEA,SAAO,MAAM,KAAK,CAAC,SAAS,MAAM,aAAa,IAAI,GAAG,IAAI,CAAC;AAC7D;;;ACPA,SAAS,mBAA0C,QAAa,SAA2B;AACzF,QAAM,EAAE,OAAO,UAAU,KAAK,UAAU,IAAI,IAAI;AAEhD,QAAM,SAAS,MAAM,YAAY;AACjC,QAAM,aAAa,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM,EAAE,MAAM,CAAC,SAAS,SAAS,OAAO,CAAC,CAAC;AAE7F,QAAMC,SAAQ,aAAa,OAAO,CAAC,IAAI;AAEvC,MAAI,QAAQ,OAAO,MAAM;AAEzB,QAAM,OAAO,UAAU,OAAOA,QAAO,QAAQ;AAE7C,WAAS,UAAU;AACjB,iBAAa,MAAM,KAAK;AACxB,UAAM,QAAQ;AAAA,EAChB;AAEA,WAAS,OAAO,OAAe;AAC7B,UAAM,YAAY;AAClB,YAAQ;AAER,QAAI,UAAU,IAAI;AAChB,YAAM,QAAQ,CAAC,WAAW,MAAM;AAC9B,eAAO,EAAE;AACT,gBAAQ;AAAA,MACV,GAAG,OAAO;AAAA,IACZ;AAAA,EACF;AAEA,SAAO,MAAM;AAEb,SAAO;AACT;AACO,IAAM,iBAA+B,uBAAO,OAAO,oBAAoB;AAAA,EAC5E,gBAAgB,EAAE,WAAW,IAAI,OAAO,GAAG;AAAA,EAC3C,cAAc;AAChB,CAAC;AAED,SAAS,sBAAsB,OAA2D;AACxF,SAAO,MAAM,IAAI,WAAW,KAAK,CAAC,MAAM,WAAW,CAAC,MAAM;AAC5D;;;ACtDA,IAAM,aAAa,oBAAI,QAA0B;AAE1C,SAAS,iBAAiB,IAAiB;AAChD,MAAI,CAAC,WAAW,IAAI,EAAE,GAAG;AACvB,UAAM,MAAM,GAAG,cAAc,eAAe;AAC5C,eAAW,IAAI,IAAI,IAAI,iBAAiB,EAAE,CAAC;AAAA,EAC7C;AACA,SAAO,WAAW,IAAI,EAAE;AAC1B;;;ACRO,SAAS,eAAsC,OAAwB;AAC5E,SAAQ,MAAM,eAAe,EAAE,CAAC,KAAK,MAAM;AAC7C;;;ACAA,SAAS,eAAe,IAA0B;AAChD,QAAM,MAAM,GAAG,cAAc,eAAe;AAC5C,QAAM,EAAE,UAAU,WAAW,UAAU,IAAI,IAAI,iBAAiB,EAAE;AAClE,SAAO,6BAA6B,KAAK,WAAW,YAAY,SAAS;AAC3E;AAEO,SAAS,UAAU,IAA8B;AACtD,MAAI,GAAG,cAAc;AAAQ,WAAO;AACpC,SAAO,GAAG,gBAAgB,GAAG,iBAAiB,GAAG,cAAc;AACjE;AAEO,SAAS,gBAAgB,IAA8B;AAC5D,MAAI,CAAC,QAAQ,QAAQ,WAAW,EAAE,SAAS,GAAG,SAAS,GAAG;AACxD,WAAO,GAAG,cAAc;AAAA,EAC1B;AAEA,MAAI,cAAc,EAAE,KAAK,eAAe,EAAE,GAAG;AAC3C,WAAO;AAAA,EACT;AAEA,SAAO,gBAAgB,UAAU,EAAE,CAAC;AACtC;AAIO,SAAS,iBAAiB,IAAiB,OAAe,CAAC,GAAW;AAC3E,QAAM,SAAS,gBAAgB,EAAE;AACjC,QAAM,SAAS,WAAW,GAAG,cAAc;AAC3C,QAAM,MAAM,OAAO,cAAc,eAAe;AAGhD,QAAM,SAAS,SAAS,CAAC,GAAG,EAAE,OAAO,IAAI,kBAAkB,CAAC,GAAG,eAAe,MAAM,IAAI,SAAS,CAAC,CAAC,IAAI;AAEvG,QAAM,UAAU,KAAK,OAAO,MAAM;AAClC,SAAO,SAAS,UAAU,QAAQ,OAAO,iBAAiB,UAAuB,MAAM,CAAC,CAAC;AAC3F;;;ACnCO,SAAS,kBAAkB,IAAsC;AACtE,MAAI,MAAM,QAAQ,CAAC,cAAc,EAAE,GAAG;AACpC,WAAO;AAAA,EACT;AAEA,MAAI;AACF,UAAM,MAAM,GAAG,cAAc,eAAe;AAC5C,WACG,cAAc,IAAI,oBAAoB,GAAG,kBAAkB,QAC5D,oBAAoB,KAAK,GAAG,SAAS,KACrC,GAAG;AAAA,EAEP,QAAQ;AACN,WAAO;AAAA,EACT;AACF;;;ACjBO,IAAM,QAAQ,MAAM,OAAO,aAAa;AAExC,SAAS,cAAc;AAC5B,QAAM,QAAS,UAAkB;AACjC,SAAO,OAAO,YAAY,UAAU;AACtC;AAEA,IAAM,KAAK,CAAC,MAAc,MAAM,KAAK,EAAE,KAAK,YAAY,CAAC;AACzD,IAAM,KAAK,CAAC,MAAc,MAAM,KAAK,EAAE,KAAK,UAAU,SAAS;AAC/D,IAAM,KAAK,CAAC,MAAc,MAAM,KAAK,EAAE,KAAK,UAAU,MAAM;AAErD,IAAM,gBAAgB,MAAM,MAAM,KAAK,CAAC,CAAC,UAAU;AACnD,IAAM,QAAQ,MAAM,GAAG,MAAM,KAAK,CAAC,cAAc;AACjD,IAAM,WAAW,MAAM,GAAG,SAAS;AACnC,IAAM,WAAW,MAAM,QAAQ,KAAK,GAAG,QAAQ;AAC/C,IAAM,YAAY,MAAM,GAAG,YAAY;AACvC,IAAM,UAAU,MAAM,GAAG,uBAAuB;AAChD,IAAM,QAAQ,MAAM,QAAQ,KAAK,CAAC,MAAM;;;ACfxC,SAAS,SAA8C,MAAY,UAAkB;AAC1F,SAAO,MAAM,KAAK,MAAM,iBAAoB,QAAQ,KAAK,CAAC,CAAC;AAC7D;AAEO,SAAS,MAA2C,MAAY,UAAkB;AACvF,SAAO,MAAM,cAAiB,QAAQ;AACxC;;;ACRO,SAAS,SAAS,IAAkB;AACzC,QAAM,MAAM,oBAAI,IAAkB;AAClC,WAASC,KAAIC,KAAkB;AAC7B,UAAM,KAAK,WAAW,sBAAsBA,GAAE;AAC9C,QAAI,IAAI,MAAM,WAAW,qBAAqB,EAAE,CAAC;AAAA,EACnD;AACA,EAAAD,KAAI,MAAMA,KAAI,EAAE,CAAC;AACjB,SAAO,SAAS,UAAU;AACxB,QAAI,QAAQ,CAACC,QAAOA,IAAG,CAAC;AAAA,EAC1B;AACF;AAEO,SAAS,IAAI,IAAkB;AACpC,QAAM,KAAK,WAAW,sBAAsB,EAAE;AAC9C,SAAO,MAAM;AACX,eAAW,qBAAqB,EAAE;AAAA,EACpC;AACF;;;AnBCO,IAAM,cAAc;", "names": ["getDocument", "getDocument", "el", "query", "query", "raf", "fn"]}