/**
 * Crypto Web Application Backend Server
 * A robust Express server with proper error handling and CORS configuration
 */

const express = require('express');
const cors = require('cors');
const http = require('http');
const WebSocket = require('ws');
const path = require('path');
const fs = require('fs');

// Create Express app
const app = express();

// Enable CORS for all routes with specific configuration for the frontend
app.use(cors({
  origin: ['http://localhost:3002', 'http://127.0.0.1:3002'],
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  credentials: true,
  maxAge: 86400 // 24 hours
}));

// Add CORS preflight
app.options('*', cors());

// Parse JSON bodies
app.use(express.json());

// Create logs directory if it doesn't exist
const logsDir = path.join(__dirname, 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir);
}

// Simple logging middleware
app.use((req, res, next) => {
  const timestamp = new Date().toISOString();
  const log = `${timestamp} - ${req.method} ${req.url}\n`;
  fs.appendFile(path.join(logsDir, 'access.log'), log, (err) => {
    if (err) console.error('Error writing to log file:', err);
  });
  console.log(`${timestamp} - ${req.method} ${req.url}`);
  next();
});

// Mock data for cryptocurrencies
const mockCryptos = [
  {
    _id: '1',
    name: 'Bitcoin',
    symbol: 'BTC',
    currentPrice: 50000,
    marketCap: 1000000000000,
    volume24h: 50000000000,
    priceChange24h: 1000,
    priceChangePercentage24h: 2,
    circulatingSupply: 19000000,
    totalSupply: 21000000,
    maxSupply: 21000000,
    lastUpdated: new Date(),
    image: 'https://assets.coingecko.com/coins/images/1/large/bitcoin.png'
  },
  {
    _id: '2',
    name: 'Ethereum',
    symbol: 'ETH',
    currentPrice: 3000,
    marketCap: 400000000000,
    volume24h: 20000000000,
    priceChange24h: 100,
    priceChangePercentage24h: 3.5,
    circulatingSupply: 120000000,
    totalSupply: 120000000,
    maxSupply: null,
    lastUpdated: new Date(),
    image: 'https://assets.coingecko.com/coins/images/279/large/ethereum.png'
  },
  {
    _id: '3',
    name: 'Cardano',
    symbol: 'ADA',
    currentPrice: 1.5,
    marketCap: 50000000000,
    volume24h: 2000000000,
    priceChange24h: 0.05,
    priceChangePercentage24h: 3.5,
    circulatingSupply: 33000000000,
    totalSupply: 45000000000,
    maxSupply: 45000000000,
    lastUpdated: new Date(),
    image: 'https://assets.coingecko.com/coins/images/975/large/cardano.png'
  },
  {
    _id: '4',
    name: 'Solana',
    symbol: 'SOL',
    currentPrice: 100,
    marketCap: 30000000000,
    volume24h: 1500000000,
    priceChange24h: 5,
    priceChangePercentage24h: 5,
    circulatingSupply: 300000000,
    totalSupply: 500000000,
    maxSupply: null,
    lastUpdated: new Date(),
    image: 'https://assets.coingecko.com/coins/images/4128/large/solana.png'
  },
  {
    _id: '5',
    name: 'Ripple',
    symbol: 'XRP',
    currentPrice: 0.75,
    marketCap: 35000000000,
    volume24h: 1800000000,
    priceChange24h: -0.02,
    priceChangePercentage24h: -2.5,
    circulatingSupply: 46000000000,
    totalSupply: 100000000000,
    maxSupply: 100000000000,
    lastUpdated: new Date(),
    image: 'https://assets.coingecko.com/coins/images/44/large/xrp-symbol-white-128.png'
  },
  {
    _id: '6',
    name: 'Polkadot',
    symbol: 'DOT',
    currentPrice: 20,
    marketCap: 20000000000,
    volume24h: 1000000000,
    priceChange24h: 0.5,
    priceChangePercentage24h: 2.5,
    circulatingSupply: 1000000000,
    totalSupply: 1100000000,
    maxSupply: null,
    lastUpdated: new Date(),
    image: 'https://assets.coingecko.com/coins/images/12171/large/polkadot.png'
  },
  {
    _id: '7',
    name: 'Dogecoin',
    symbol: 'DOGE',
    currentPrice: 0.15,
    marketCap: 19000000000,
    volume24h: 900000000,
    priceChange24h: 0.01,
    priceChangePercentage24h: 7,
    circulatingSupply: 130000000000,
    totalSupply: 130000000000,
    maxSupply: null,
    lastUpdated: new Date(),
    image: 'https://assets.coingecko.com/coins/images/5/large/dogecoin.png'
  },
  {
    _id: '8',
    name: 'Avalanche',
    symbol: 'AVAX',
    currentPrice: 80,
    marketCap: 18000000000,
    volume24h: 850000000,
    priceChange24h: 3,
    priceChangePercentage24h: 4,
    circulatingSupply: 220000000,
    totalSupply: 720000000,
    maxSupply: 720000000,
    lastUpdated: new Date(),
    image: 'https://assets.coingecko.com/coins/images/12559/large/Avalanche_Circle_RedWhite_Trans.png'
  },
  {
    _id: '9',
    name: 'Chainlink',
    symbol: 'LINK',
    currentPrice: 25,
    marketCap: 12000000000,
    volume24h: 600000000,
    priceChange24h: 1,
    priceChangePercentage24h: 4.2,
    circulatingSupply: 460000000,
    totalSupply: 1000000000,
    maxSupply: 1000000000,
    lastUpdated: new Date(),
    image: 'https://assets.coingecko.com/coins/images/877/large/chainlink-new-logo.png'
  },
  {
    _id: '10',
    name: 'Polygon',
    symbol: 'MATIC',
    currentPrice: 1.8,
    marketCap: 11000000000,
    volume24h: 550000000,
    priceChange24h: 0.08,
    priceChangePercentage24h: 4.7,
    circulatingSupply: 6000000000,
    totalSupply: 10000000000,
    maxSupply: 10000000000,
    lastUpdated: new Date(),
    image: 'https://assets.coingecko.com/coins/images/4713/large/matic-token-icon.png'
  }
];

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'Crypto Web Application API is running',
    status: 'ok',
    timestamp: new Date().toISOString()
  });
});

// API root endpoint
app.get('/api', (req, res) => {
  res.json({
    success: true,
    message: 'Crypto API is running',
    version: '1.0.0',
    timestamp: new Date().toISOString()
  });
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    status: 'healthy',
    timestamp: new Date().toISOString(),
    message: 'Server is running properly'
  });
});

// Get all cryptos
app.get('/api/cryptos', (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.perPage) || 20;
    
    // Calculate pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedCryptos = mockCryptos.slice(startIndex, endIndex);
    
    res.json({
      success: true,
      data: {
        cryptos: paginatedCryptos,
        page,
        pages: Math.ceil(mockCryptos.length / limit),
        total: mockCryptos.length,
      },
    });
  } catch (error) {
    console.error('Error fetching cryptos:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching cryptocurrencies',
      error: error.message
    });
  }
});

// Get crypto by ID
app.get('/api/cryptos/:id', (req, res) => {
  try {
    const coinId = req.params.id;
    
    // Find matching crypto or return 404
    const crypto = mockCryptos.find(c => c._id === coinId || c.symbol.toLowerCase() === coinId.toLowerCase());
    
    if (!crypto) {
      return res.status(404).json({
        success: false,
        message: `Cryptocurrency with ID ${coinId} not found`
      });
    }
    
    // Generate mock historical data
    const now = Date.now();
    const mockPrices = [];
    const mockMarketCaps = [];
    const mockVolumes = [];
    
    // Generate 30 days of mock data
    for (let i = 30; i >= 0; i--) {
      const timestamp = now - (i * 24 * 60 * 60 * 1000);
      const randomFactor = 0.9 + (Math.random() * 0.2); // Random between 0.9 and 1.1
      
      mockPrices.push([timestamp, crypto.currentPrice * randomFactor]);
      mockMarketCaps.push([timestamp, crypto.marketCap * randomFactor]);
      mockVolumes.push([timestamp, crypto.volume24h * randomFactor]);
    }
    
    // Create detailed response
    const detailedCrypto = {
      _id: crypto._id,
      name: crypto.name,
      symbol: crypto.symbol,
      description: `${crypto.name} is a cryptocurrency used for digital transactions.`,
      currentPrice: crypto.currentPrice,
      marketCap: crypto.marketCap,
      volume24h: crypto.volume24h,
      priceChange24h: crypto.priceChange24h,
      priceChangePercentage24h: crypto.priceChangePercentage24h,
      priceChangePercentage7d: crypto.priceChangePercentage24h * 2,
      priceChangePercentage30d: crypto.priceChangePercentage24h * 5,
      circulatingSupply: crypto.circulatingSupply,
      totalSupply: crypto.totalSupply,
      maxSupply: crypto.maxSupply,
      allTimeHigh: crypto.currentPrice * 2,
      allTimeHighDate: new Date(now - (90 * 24 * 60 * 60 * 1000)).toISOString(),
      allTimeLow: crypto.currentPrice * 0.3,
      allTimeLowDate: new Date(now - (300 * 24 * 60 * 60 * 1000)).toISOString(),
      lastUpdated: new Date().toISOString(),
      image: crypto.image,
      marketCapRank: parseInt(crypto._id),
      genesisDate: '2015-07-30',
      categories: ['Currency', 'Smart Contract Platform'],
      links: {
        homepage: `https://${crypto.name.toLowerCase()}.org`,
        blockchain_site: `https://explorer.${crypto.name.toLowerCase()}.org`,
        official_forum_url: `https://forum.${crypto.name.toLowerCase()}.org`,
        chat_url: `https://chat.${crypto.name.toLowerCase()}.org`,
        announcement_url: `https://blog.${crypto.name.toLowerCase()}.org`,
        twitter_screen_name: crypto.name.toLowerCase(),
        facebook_username: crypto.name.toLowerCase(),
        telegram_channel_identifier: crypto.name.toLowerCase(),
        subreddit_url: `https://reddit.com/r/${crypto.name.toLowerCase()}`,
        repos_url: { github: [`https://github.com/${crypto.name.toLowerCase()}`] },
      },
      marketData: {
        prices: mockPrices,
        market_caps: mockMarketCaps,
        total_volumes: mockVolumes,
      },
      communityData: {
        twitter_followers: 100000,
        reddit_subscribers: 50000,
        telegram_channel_user_count: 25000,
      },
      developerData: {
        forks: 100,
        stars: 500,
        subscribers: 200,
        total_issues: 50,
        closed_issues: 40,
        pull_requests_merged: 300,
        pull_request_contributors: 30,
        commit_count_4_weeks: 100,
      },
    };
    
    res.json({
      success: true,
      data: detailedCrypto,
    });
  } catch (error) {
    console.error(`Error fetching crypto details:`, error);
    res.status(500).json({
      success: false,
      message: 'Error fetching cryptocurrency details',
      error: error.message
    });
  }
});

// Get trending cryptos
app.get('/api/cryptos/trending', (req, res) => {
  try {
    // Use top 5 cryptos as trending
    const trendingCryptos = mockCryptos.slice(0, 5).map(crypto => ({
      _id: crypto._id,
      name: crypto.name,
      symbol: crypto.symbol,
      image: crypto.image,
      rank: parseInt(crypto._id),
      priceBtc: crypto.currentPrice / 50000, // Approximate BTC value
      score: 6 - parseInt(crypto._id), // Higher score for lower ID
    }));
    
    res.json({
      success: true,
      data: trendingCryptos,
    });
  } catch (error) {
    console.error('Error in trending cryptos endpoint:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching trending cryptocurrencies',
      error: error.message
    });
  }
});

// Search cryptos
app.get('/api/search/:query', (req, res) => {
  try {
    const query = req.params.query.toLowerCase();
    
    // Filter cryptos based on query
    const filteredCryptos = mockCryptos.filter(crypto =>
      crypto.name.toLowerCase().includes(query) ||
      crypto.symbol.toLowerCase().includes(query)
    );
    
    // Format results
    const searchResults = {
      coins: filteredCryptos.map(crypto => ({
        id: crypto._id,
        name: crypto.name,
        symbol: crypto.symbol.toLowerCase(),
        market_cap_rank: parseInt(crypto._id),
        large: crypto.image,
        thumb: crypto.image,
      })),
      categories: [],
      exchanges: [],
      nfts: [],
    };
    
    res.json({
      success: true,
      data: searchResults,
    });
  } catch (error) {
    console.error(`Error in search endpoint:`, error);
    res.status(500).json({
      success: false,
      message: 'Error searching cryptocurrencies',
      error: error.message
    });
  }
});

// AI insights endpoint
app.get('/api/ai/insights', (req, res) => {
  try {
    const insights = [
      {
        id: 1,
        title: "Bitcoin Price Prediction",
        content: "Based on current market trends and historical data, our AI model predicts Bitcoin may reach $75,000 by the end of the year. Key factors include institutional adoption and regulatory clarity.",
        confidence: 0.85,
        timestamp: new Date().toISOString(),
        tags: ["Bitcoin", "Price Prediction", "Institutional Adoption"]
      },
      {
        id: 2,
        title: "Ethereum Technical Analysis",
        content: "Ethereum is showing strong support at $2,800 with resistance at $3,200. The completion of the ETH 2.0 upgrade could be a significant catalyst for price movement in the coming months.",
        confidence: 0.78,
        timestamp: new Date().toISOString(),
        tags: ["Ethereum", "Technical Analysis", "ETH 2.0"]
      },
      {
        id: 3,
        title: "Market Sentiment Analysis",
        content: "Overall crypto market sentiment is currently neutral with a slight bullish bias. Social media analysis shows increasing interest in DeFi and NFT projects.",
        confidence: 0.72,
        timestamp: new Date().toISOString(),
        tags: ["Market Sentiment", "Social Media", "DeFi", "NFT"]
      },
      {
        id: 4,
        title: "Altcoin Season Probability",
        content: "Our models indicate a 65% probability of an altcoin season beginning within the next 30 days, with mid-cap cryptocurrencies showing the most potential for growth.",
        confidence: 0.65,
        timestamp: new Date().toISOString(),
        tags: ["Altcoins", "Market Cycles", "Mid-cap Cryptocurrencies"]
      },
      {
        id: 5,
        title: "Regulatory Impact Assessment",
        content: "Recent regulatory developments in the US and EU are likely to have a net positive impact on the cryptocurrency market in the long term, despite short-term volatility.",
        confidence: 0.81,
        timestamp: new Date().toISOString(),
        tags: ["Regulation", "Policy", "Market Impact"]
      }
    ];
    
    res.json({
      success: true,
      data: insights
    });
  } catch (error) {
    console.error('Error in AI insights endpoint:', error);
    res.status(500).json({
      success: false,
      message: 'Error generating AI insights',
      error: error.message
    });
  }
});

// AI prediction endpoint
app.post('/api/ai/predict', (req, res) => {
  try {
    const { coin, timeframe } = req.body;
    
    if (!coin) {
      return res.status(400).json({
        success: false,
        message: 'Coin parameter is required'
      });
    }
    
    const validTimeframes = ['24h', '7d', '30d', '90d', '1y'];
    const selectedTimeframe = validTimeframes.includes(timeframe) ? timeframe : '30d';
    
    // Find the requested coin
    const cryptoData = mockCryptos.find(c => 
      c._id === coin || 
      c.symbol.toLowerCase() === coin.toLowerCase() || 
      c.name.toLowerCase() === coin.toLowerCase()
    );
    
    if (!cryptoData) {
      return res.status(404).json({
        success: false,
        message: `Cryptocurrency ${coin} not found`
      });
    }
    
    // Generate mock prediction data
    const now = Date.now();
    const predictions = [];
    let days;
    
    switch (selectedTimeframe) {
      case '24h': days = 1; break;
      case '7d': days = 7; break;
      case '30d': days = 30; break;
      case '90d': days = 90; break;
      case '1y': days = 365; break;
      default: days = 30;
    }
    
    // Generate prediction data points
    const trend = Math.random() > 0.5 ? 1 : -1; // Random trend direction
    const volatility = 0.02; // 2% daily volatility
    let lastPrice = cryptoData.currentPrice;
    
    for (let i = 1; i <= days; i++) {
      const timestamp = now + (i * 24 * 60 * 60 * 1000);
      const randomChange = (Math.random() * volatility * 2 - volatility) + (trend * volatility / 2);
      lastPrice = lastPrice * (1 + randomChange);
      
      predictions.push({
        timestamp: new Date(timestamp).toISOString(),
        price: lastPrice,
        confidence: 0.95 - (i / days * 0.3) // Confidence decreases over time
      });
    }
    
    res.json({
      success: true,
      data: {
        coin: cryptoData.name,
        symbol: cryptoData.symbol,
        timeframe: selectedTimeframe,
        currentPrice: cryptoData.currentPrice,
        predictions,
        analysis: {
          trend: trend > 0 ? 'bullish' : 'bearish',
          summary: `Our AI model predicts a ${trend > 0 ? 'bullish' : 'bearish'} trend for ${cryptoData.name} over the next ${days} days, with an estimated ${trend > 0 ? 'increase' : 'decrease'} of approximately ${Math.abs(Math.round(((predictions[predictions.length-1].price / cryptoData.currentPrice) - 1) * 100))}%.`,
          keyFactors: [
            "Market sentiment",
            "Technical indicators",
            "Trading volume patterns",
            "Historical price action",
            "Macroeconomic factors"
          ],
          confidence: 0.75
        }
      }
    });
  } catch (error) {
    console.error('Error in AI prediction endpoint:', error);
    res.status(500).json({
      success: false,
      message: 'Error generating AI prediction',
      error: error.message
    });
  }
});

// Fallback for all other routes
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: 'Endpoint not found',
    path: req.path
  });
});

// Create HTTP server
const server = http.createServer(app);

// Create WebSocket server
const wss = new WebSocket.Server({ server });

// Store connected clients
const clients = new Set();

// WebSocket connection handler
wss.on('connection', (ws) => {
  // Add client to set
  clients.add(ws);
  console.log('WebSocket client connected');
  
  // Send initial data
  ws.send(JSON.stringify({ 
    type: 'connection', 
    message: 'Connected to WebSocket server',
    timestamp: new Date().toISOString()
  }));
  
  // Handle messages from client
  ws.on('message', (message) => {
    try {
      const data = JSON.parse(message.toString());
      console.log('Received message:', data);
      
      // Handle subscription requests
      if (data.type === 'subscribe' && data.channel === 'prices') {
        ws.subscribed = true;
        ws.coins = data.coins || [];
        ws.send(JSON.stringify({
          type: 'subscribed',
          channel: 'prices',
          coins: ws.coins,
          timestamp: new Date().toISOString()
        }));
      }
    } catch (error) {
      console.error('Error processing message:', error);
    }
  });
  
  // Handle client disconnect
  ws.on('close', () => {
    clients.delete(ws);
    console.log('WebSocket client disconnected');
  });
});

// Simulate real-time price updates
const startPriceUpdates = () => {
  // Update prices every 5 seconds
  setInterval(() => {
    try {
      // Create mock price updates
      const mockPrices = {};
      mockCryptos.forEach(crypto => {
        const randomChange = (Math.random() * 2 - 1) * 2; // Random between -2% and +2%
        const newPrice = crypto.currentPrice * (1 + randomChange / 100);
        
        mockPrices[crypto._id] = {
          price: newPrice,
          change24h: crypto.priceChangePercentage24h + randomChange,
          volume: crypto.volume24h * (1 + (Math.random() * 0.1 - 0.05)), // ±5% volume change
          lastUpdated: new Date().toISOString(),
        };
      });
      
      // Send updates to subscribed clients
      clients.forEach(client => {
        if (client.readyState === WebSocket.OPEN && client.subscribed) {
          const coinUpdates = {};
          
          // If client specified coins, only send those
          if (client.coins && client.coins.length > 0) {
            client.coins.forEach(coinId => {
              if (mockPrices[coinId]) {
                coinUpdates[coinId] = mockPrices[coinId];
              }
            });
          } else {
            // Otherwise send all prices
            Object.assign(coinUpdates, mockPrices);
          }
          
          client.send(JSON.stringify({
            type: 'priceUpdate',
            data: coinUpdates,
            timestamp: new Date().toISOString()
          }));
        }
      });
    } catch (error) {
      console.error('Error in price updates:', error);
    }
  }, 5000); // Update every 5 seconds
};

// Start server with error handling
const PORT = process.env.PORT || 3001;

try {
  // Check if port is in use
  server.on('error', (error) => {
    if (error.code === 'EADDRINUSE') {
      console.error(`Port ${PORT} is already in use. Please close the application using this port or use a different port.`);
    } else {
      console.error('Server error:', error);
    }
    process.exit(1);
  });
  
  // Start the server
  server.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
    console.log(`API available at http://localhost:${PORT}/api`);
    console.log(`WebSocket server available at ws://localhost:${PORT}`);
    
    // Start price updates
    startPriceUpdates();
    console.log('Price updates started successfully');
  });
} catch (error) {
  console.error('Failed to start server:', error);
}
