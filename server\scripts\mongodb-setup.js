/**
 * MongoDB Setup Script
 * 
 * This script helps set up the MongoDB database for the Crypto Tracker application.
 * It creates the necessary collections and indexes.
 */

require('dotenv').config();
const mongoose = require('mongoose');
const readline = require('readline');
const fs = require('fs');
const path = require('path');

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// MongoDB connection string
const MONGO_URI = process.env.MONGO_URI;

// Check if MongoDB URI is set
if (!MONGO_URI) {
  console.error('\n❌ MongoDB URI is not set in the .env file.');
  console.log('\nPlease set the MONGO_URI environment variable in the .env file:');
  console.log('MONGO_URI=mongodb+srv://<username>:<password>@<cluster>/<database>?retryWrites=true&w=majority');
  process.exit(1);
}

// Display the MongoDB URI (with password masked)
console.log('\n🔍 Using MongoDB URI:');
console.log(MONGO_URI.replace(/\/\/([^:]+):([^@]+)@/, '//***:***@'));

// Connect to MongoDB
async function connectToMongoDB() {
  try {
    console.log('\n🔄 Connecting to MongoDB...');
    
    const conn = await mongoose.connect(MONGO_URI, {
      serverSelectionTimeoutMS: 10000, // 10 seconds
      socketTimeoutMS: 45000, // 45 seconds
      connectTimeoutMS: 10000, // 10 seconds
    });
    
    console.log('\n✅ MongoDB Connected!');
    console.log(`Connected to: ${conn.connection.host}`);
    console.log(`Database name: ${conn.connection.name}`);
    console.log(`Connection state: ${mongoose.STATES[conn.connection.readyState]}`);
    
    return conn;
  } catch (error) {
    console.error('\n❌ MongoDB Connection Failed!');
    console.error(`Error: ${error.message}`);
    
    // Provide more helpful error messages based on common issues
    if (error.message.includes('ECONNREFUSED')) {
      console.error('\nPossible causes:');
      console.error('- MongoDB server is not running');
      console.error('- Connection string has incorrect host or port');
    } else if (error.message.includes('Authentication failed')) {
      console.error('\nPossible causes:');
      console.error('- Username or password in connection string is incorrect');
      console.error('- User does not have access to the database');
    } else if (error.message.includes('ETIMEDOUT')) {
      console.error('\nPossible causes:');
      console.error('- Network connectivity issues');
      console.error('- Firewall blocking the connection');
      console.error('- MongoDB Atlas IP whitelist restrictions');
    }
    
    process.exit(1);
  }
}

// Create collections and indexes
async function setupDatabase(conn) {
  try {
    console.log('\n🔄 Setting up database...');
    
    // Get the database
    const db = conn.connection.db;
    
    // List of collections to create
    const collections = [
      'users',
      'portfolios',
      'transactions',
      'watchlists',
    ];
    
    // Create collections if they don't exist
    const existingCollections = await db.listCollections().toArray();
    const existingCollectionNames = existingCollections.map(c => c.name);
    
    for (const collection of collections) {
      if (!existingCollectionNames.includes(collection)) {
        console.log(`Creating collection: ${collection}`);
        await db.createCollection(collection);
      } else {
        console.log(`Collection already exists: ${collection}`);
      }
    }
    
    // Create indexes
    console.log('\n🔄 Creating indexes...');
    
    // Users collection indexes
    await db.collection('users').createIndex({ email: 1 }, { unique: true });
    await db.collection('users').createIndex({ username: 1 }, { unique: true });
    
    // Portfolios collection indexes
    await db.collection('portfolios').createIndex({ user: 1 });
    
    // Transactions collection indexes
    await db.collection('transactions').createIndex({ portfolio: 1 });
    await db.collection('transactions').createIndex({ user: 1 });
    await db.collection('transactions').createIndex({ cryptoId: 1 });
    await db.collection('transactions').createIndex({ date: -1 });
    
    // Watchlists collection indexes
    await db.collection('watchlists').createIndex({ user: 1 });
    
    console.log('\n✅ Database setup completed successfully!');
    
    // List all collections
    const updatedCollections = await db.listCollections().toArray();
    console.log('\nCollections in database:');
    updatedCollections.forEach(collection => {
      console.log(`- ${collection.name}`);
    });
    
    return true;
  } catch (error) {
    console.error('\n❌ Database setup failed!');
    console.error(`Error: ${error.message}`);
    return false;
  }
}

// Create a test user
async function createTestUser(conn) {
  try {
    console.log('\n🔄 Creating test user...');
    
    // Get the database
    const db = conn.connection.db;
    
    // Check if test user already exists
    const existingUser = await db.collection('users').findOne({ email: '<EMAIL>' });
    
    if (existingUser) {
      console.log('\n✅ Test user already exists:');
      console.log(`Username: ${existingUser.username}`);
      console.log(`Email: ${existingUser.email}`);
      console.log(`Role: ${existingUser.role}`);
      return true;
    }
    
    // Create test user
    const bcrypt = require('bcryptjs');
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash('password123', salt);
    
    const testUser = {
      username: 'testuser',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'user',
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    await db.collection('users').insertOne(testUser);
    
    console.log('\n✅ Test user created successfully:');
    console.log('Username: testuser');
    console.log('Email: <EMAIL>');
    console.log('Password: password123');
    
    return true;
  } catch (error) {
    console.error('\n❌ Failed to create test user!');
    console.error(`Error: ${error.message}`);
    return false;
  }
}

// Main function
async function main() {
  let conn;
  
  try {
    // Connect to MongoDB
    conn = await connectToMongoDB();
    
    // Setup database
    await setupDatabase(conn);
    
    // Ask if user wants to create a test user
    rl.question('\nDo you want to create a test user? (y/n): ', async (answer) => {
      if (answer.toLowerCase() === 'y') {
        await createTestUser(conn);
      }
      
      console.log('\n🎉 MongoDB setup completed!');
      console.log('You can now start the application with:');
      console.log('npm run dev');
      
      // Close connection and exit
      await mongoose.disconnect();
      rl.close();
      process.exit(0);
    });
  } catch (error) {
    console.error('\n❌ An unexpected error occurred:');
    console.error(error);
    
    // Close connection and exit
    if (conn) {
      await mongoose.disconnect();
    }
    
    rl.close();
    process.exit(1);
  }
}

// Run the main function
main();
