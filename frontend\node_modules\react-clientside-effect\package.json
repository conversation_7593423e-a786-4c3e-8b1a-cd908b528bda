{"name": "react-clientside-effect", "version": "1.2.7", "description": "Create components whose prop changes map to a global side effect", "main": "lib/index.js", "module": "lib/index.es.js", "scripts": {"build": "node scripts/build.js", "clean": "<PERSON><PERSON><PERSON> lib", "prepare": "npm test && npm run clean && npm run build", "test": "mocha", "test:watch": "mocha --watch", "test:cov": "babel-node ./node_modules/.bin/isparta cover ./node_modules/.bin/_mocha"}, "repository": {"type": "git", "url": "https://github.com/thekashey/react-clientside-effect.git"}, "keywords": ["react", "component", "side", "effect"], "author": "<PERSON> <<EMAIL>> (http://github.com/gaearon)", "license": "MIT", "bugs": {"url": "https://github.com/thekashey/react-clientside-effect/issues"}, "homepage": "https://github.com/thekashey/react-clientside-effect", "contributors": ["<PERSON> <<EMAIL>>"], "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc"}, "dependencies": {"@babel/runtime": "^7.12.13"}, "devDependencies": {"@babel/cli": "^7.12.13", "@babel/core": "^7.12.13", "@babel/node": "^7.12.13", "@babel/plugin-proposal-class-properties": "^7.12.13", "@babel/plugin-proposal-object-rest-spread": "^7.12.13", "@babel/plugin-transform-runtime": "^7.12.13", "@babel/preset-env": "^7.12.13", "@babel/preset-react": "^7.12.13", "@babel/register": "^7.12.13", "babel-plugin-add-module-exports": "^0.2.1", "chai": "^3.2.0", "enzyme": "^2.7.0", "exenv": "^1.2.2", "gzip-size": "^4.1.0", "isparta": "^4.0.0", "jsdom": "^11.12.0", "jsdom-global": "^3.0.2", "mocha": "^3.2.0", "pretty-bytes": "^4.0.2", "react": "^15.4.2", "react-addons-test-utils": "^15.4.2", "react-dom": "^15.4.2", "rimraf": "^2.4.3", "rollup": "^0.67.1", "rollup-plugin-babel": "^4.0.3", "rollup-plugin-node-resolve": "^3.0.3", "rollup-plugin-uglify": "^3.0.0"}, "files": ["LICENSE", "README.md", "lib/"]}