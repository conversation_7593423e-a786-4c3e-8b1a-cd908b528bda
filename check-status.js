const http = require('http');

const checkService = (name, port, path = '/') => {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: port,
      path: path,
      method: 'GET',
      timeout: 5000
    };

    const req = http.request(options, (res) => {
      resolve({
        name,
        port,
        status: res.statusCode,
        running: res.statusCode === 200,
        message: `HTTP ${res.statusCode}`
      });
    });

    req.on('error', (e) => {
      resolve({
        name,
        port,
        status: 'ERROR',
        running: false,
        message: e.message
      });
    });

    req.on('timeout', () => {
      resolve({
        name,
        port,
        status: 'TIMEOUT',
        running: false,
        message: 'Connection timeout'
      });
    });

    req.end();
  });
};

const checkAllServices = async () => {
  console.log('🔍 Checking all services...\n');

  const services = [
    { name: 'Frontend (Vite)', port: 5173 },
    { name: 'Backend API', port: 3001, path: '/api/health' },
    { name: 'Alternative Backend', port: 3000 }
  ];

  const results = await Promise.all(
    services.map(service => checkService(service.name, service.port, service.path))
  );

  console.log('📊 Service Status Report:');
  console.log('========================');

  results.forEach(result => {
    const status = result.running ? '✅ RUNNING' : '❌ NOT RUNNING';
    console.log(`${result.name}: ${status}`);
    console.log(`   Port: ${result.port}`);
    console.log(`   Status: ${result.message}`);
    if (result.running) {
      console.log(`   URL: http://localhost:${result.port}`);
    }
    console.log('');
  });

  const runningServices = results.filter(r => r.running);
  console.log(`📈 Summary: ${runningServices.length}/${results.length} services running`);

  if (runningServices.some(s => s.name.includes('Frontend'))) {
    console.log('\n🎉 Frontend is ready!');
    console.log('🌐 Open your browser and go to: http://localhost:5173');
  }
};

checkAllServices();
