import React from 'react';
import {
  Box,
  But<PERSON>,
  Heading,
  Text,
  VStack,
  OrderedList,
  ListItem,
  Code,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Image,
  Link,
  Divider,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  useColorModeValue,
} from '@chakra-ui/react';
import { FiExternalLink } from 'react-icons/fi';

interface MongoDBSetupGuideProps {
  onClose?: () => void;
}

const MongoDBSetupGuide: React.FC<MongoDBSetupGuideProps> = ({ onClose }) => {
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  return (
    <Box
      p={5}
      borderRadius="md"
      bg={bgColor}
      borderWidth="1px"
      borderColor={borderColor}
      boxShadow="md"
    >
      <VStack spacing={6} align="stretch">
        <Heading as="h2" size="lg">MongoDB Atlas Setup Guide</Heading>
        
        <Alert status="info" borderRadius="md">
          <AlertIcon />
          <Box>
            <AlertTitle>Connection Issue Detected</AlertTitle>
            <AlertDescription>
              Follow this guide to set up your MongoDB Atlas connection properly.
            </AlertDescription>
          </Box>
        </Alert>

        <Text>
          MongoDB Atlas is a cloud database service that hosts your MongoDB database. Follow these steps to create a free
          MongoDB Atlas account and connect it to your application.
        </Text>

        <Divider />

        <VStack spacing={4} align="stretch">
          <Heading as="h3" size="md">Step 1: Create a MongoDB Atlas Account</Heading>
          <OrderedList spacing={3} pl={5}>
            <ListItem>
              Go to <Link href="https://www.mongodb.com/cloud/atlas/register" isExternal color="blue.500">
                MongoDB Atlas <FiExternalLink style={{ display: 'inline' }} />
              </Link> and sign up for a free account.
            </ListItem>
            <ListItem>Complete the registration process with your email and password.</ListItem>
            <ListItem>Select the free tier option (M0) when prompted.</ListItem>
          </OrderedList>
        </VStack>

        <VStack spacing={4} align="stretch">
          <Heading as="h3" size="md">Step 2: Create a New Cluster</Heading>
          <OrderedList spacing={3} pl={5}>
            <ListItem>After logging in, click "Build a Database".</ListItem>
            <ListItem>Select the "FREE" option (Shared Clusters).</ListItem>
            <ListItem>Choose a cloud provider (AWS, Google Cloud, or Azure) and a region closest to you.</ListItem>
            <ListItem>Click "Create Cluster" and wait for the cluster to be created (this may take a few minutes).</ListItem>
          </OrderedList>
        </VStack>

        <VStack spacing={4} align="stretch">
          <Heading as="h3" size="md">Step 3: Set Up Database Access</Heading>
          <OrderedList spacing={3} pl={5}>
            <ListItem>In the left sidebar, click on "Database Access" under the Security section.</ListItem>
            <ListItem>Click "Add New Database User".</ListItem>
            <ListItem>
              Create a new user with a username and password. Make sure to use a strong password and remember it.
              <Box mt={2}>
                <Code p={2} borderRadius="md" width="100%" fontSize="sm">
                  Username: crypto_user
                  Password: [your-secure-password]
                </Code>
              </Box>
            </ListItem>
            <ListItem>Set the user privileges to "Read and Write to Any Database" and click "Add User".</ListItem>
          </OrderedList>
        </VStack>

        <VStack spacing={4} align="stretch">
          <Heading as="h3" size="md">Step 4: Set Up Network Access</Heading>
          <OrderedList spacing={3} pl={5}>
            <ListItem>In the left sidebar, click on "Network Access" under the Security section.</ListItem>
            <ListItem>Click "Add IP Address".</ListItem>
            <ListItem>
              For development, you can click "Allow Access from Anywhere" (not recommended for production).
              Alternatively, add your current IP address.
            </ListItem>
            <ListItem>Click "Confirm" to save the IP address.</ListItem>
          </OrderedList>
        </VStack>

        <VStack spacing={4} align="stretch">
          <Heading as="h3" size="md">Step 5: Get Your Connection String</Heading>
          <OrderedList spacing={3} pl={5}>
            <ListItem>Go back to the "Database" section and click "Connect" on your cluster.</ListItem>
            <ListItem>Select "Connect your application".</ListItem>
            <ListItem>Choose "Node.js" as your driver and the latest version.</ListItem>
            <ListItem>
              Copy the connection string. It will look something like this:
              <Box mt={2}>
                <Code p={2} borderRadius="md" width="100%" fontSize="sm" overflowX="auto">
                  *****************************************************************************************************************
                </Code>
              </Box>
            </ListItem>
            <ListItem>
              Replace <Code>&lt;password&gt;</Code> with your actual password and <Code>myFirstDatabase</Code> with <Code>crypto_tracker</Code>.
            </ListItem>
          </OrderedList>
        </VStack>

        <VStack spacing={4} align="stretch">
          <Heading as="h3" size="md">Step 6: Update Your .env File</Heading>
          <OrderedList spacing={3} pl={5}>
            <ListItem>Open the .env file in your project root directory.</ListItem>
            <ListItem>
              Update the MONGO_URI variable with your connection string:
              <Box mt={2}>
                <Code p={2} borderRadius="md" width="100%" fontSize="sm">
                  MONGO_URI=mongodb+srv://crypto_user:<EMAIL>/crypto_tracker?retryWrites=true&w=majority
                </Code>
              </Box>
            </ListItem>
            <ListItem>Save the file and restart your server.</ListItem>
          </OrderedList>
        </VStack>

        <Accordion allowToggle>
          <AccordionItem>
            <h2>
              <AccordionButton>
                <Box flex="1" textAlign="left" fontWeight="medium">
                  Troubleshooting Common Issues
                </Box>
                <AccordionIcon />
              </AccordionButton>
            </h2>
            <AccordionPanel pb={4}>
              <VStack spacing={4} align="stretch">
                <Box>
                  <Text fontWeight="bold">Authentication Failed</Text>
                  <Text>
                    If you see "Authentication failed" errors, double-check your username and password in the connection string.
                    Make sure you've replaced the &lt;password&gt; placeholder with your actual password, and that the password
                    doesn't contain special characters that need to be URL-encoded.
                  </Text>
                </Box>
                
                <Box>
                  <Text fontWeight="bold">Connection Timeout</Text>
                  <Text>
                    If you see connection timeout errors, check your network access settings in MongoDB Atlas.
                    Make sure your current IP address is whitelisted or that you've allowed access from anywhere.
                  </Text>
                </Box>
                
                <Box>
                  <Text fontWeight="bold">DNS Resolution Issues</Text>
                  <Text>
                    If you see DNS resolution errors, it might be a temporary issue with MongoDB Atlas.
                    Try again later or check the MongoDB Atlas status page.
                  </Text>
                </Box>
              </VStack>
            </AccordionPanel>
          </AccordionItem>
        </Accordion>

        {onClose && (
          <Button colorScheme="blue" onClick={onClose} alignSelf="flex-end">
            Close Guide
          </Button>
        )}
      </VStack>
    </Box>
  );
};

export default MongoDBSetupGuide;
