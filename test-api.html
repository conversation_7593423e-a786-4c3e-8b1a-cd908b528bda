<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Connection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <h1>API Connection Test</h1>
    
    <div class="test-section">
        <h2>Test Controls</h2>
        <button onclick="testApiConnection.testApi().then(result => displayResult('api-result', result))">Test API Connection</button>
        <button onclick="testApiConnection.testCryptos().then(result => displayResult('cryptos-result', result))">Test Cryptos Endpoint</button>
        <button onclick="testApiConnection.testTrending().then(result => displayResult('trending-result', result))">Test Trending Endpoint</button>
        <button onclick="testApiConnection.runAll().then(results => {
            displayResult('api-result', results.api);
            displayResult('cryptos-result', results.cryptos);
            displayResult('trending-result', results.trending);
        })">Run All Tests</button>
    </div>
    
    <div class="test-section">
        <h2>API Connection Result</h2>
        <div id="api-result">
            <p>Click "Test API Connection" to run the test.</p>
        </div>
    </div>
    
    <div class="test-section">
        <h2>Cryptos Endpoint Result</h2>
        <div id="cryptos-result">
            <p>Click "Test Cryptos Endpoint" to run the test.</p>
        </div>
    </div>
    
    <div class="test-section">
        <h2>Trending Endpoint Result</h2>
        <div id="trending-result">
            <p>Click "Test Trending Endpoint" to run the test.</p>
        </div>
    </div>
    
    <script>
        function displayResult(elementId, result) {
            const element = document.getElementById(elementId);
            
            if (result.success) {
                element.innerHTML = `
                    <p class="success">SUCCESS</p>
                    <p>Status: ${result.status}</p>
                    <pre>${JSON.stringify(result.data, null, 2)}</pre>
                `;
            } else {
                element.innerHTML = `
                    <p class="error">ERROR</p>
                    <p>${result.error}</p>
                `;
            }
        }
    </script>
    
    <script src="test-api-connection.js"></script>
</body>
</html>
