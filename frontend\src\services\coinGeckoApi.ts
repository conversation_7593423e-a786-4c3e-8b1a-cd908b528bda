/**
 * Direct CoinGecko API Integration
 * This service fetches live cryptocurrency data directly from CoinGecko API
 */

const COINGECKO_API_BASE = 'https://api.coingecko.com/api/v3';

export interface CoinGeckoMarketData {
  id: string;
  symbol: string;
  name: string;
  image: string;
  current_price: number;
  market_cap: number;
  market_cap_rank: number;
  fully_diluted_valuation: number | null;
  total_volume: number;
  high_24h: number;
  low_24h: number;
  price_change_24h: number;
  price_change_percentage_24h: number;
  market_cap_change_24h: number;
  market_cap_change_percentage_24h: number;
  circulating_supply: number;
  total_supply: number | null;
  max_supply: number | null;
  ath: number;
  ath_change_percentage: number;
  ath_date: string;
  atl: number;
  atl_change_percentage: number;
  atl_date: string;
  roi: any;
  last_updated: string;
  sparkline_in_7d?: {
    price: number[];
  };
  price_change_percentage_1h_in_currency?: number;
  price_change_percentage_24h_in_currency?: number;
  price_change_percentage_7d_in_currency?: number;
}

export interface CoinGeckoGlobalData {
  data: {
    active_cryptocurrencies: number;
    upcoming_icos: number;
    ongoing_icos: number;
    ended_icos: number;
    markets: number;
    total_market_cap: { [key: string]: number };
    total_volume: { [key: string]: number };
    market_cap_percentage: { [key: string]: number };
    market_cap_change_percentage_24h_usd: number;
    updated_at: number;
  };
}

export interface TrendingCoin {
  id: string;
  coin_id: number;
  name: string;
  symbol: string;
  market_cap_rank: number;
  thumb: string;
  small: string;
  large: string;
  slug: string;
  price_btc: number;
  score: number;
}

export interface CoinGeckoTrendingData {
  coins: Array<{
    item: TrendingCoin;
  }>;
  nfts: any[];
  categories: any[];
}

class CoinGeckoApiService {
  private baseUrl = COINGECKO_API_BASE;
  private requestCount = 0;
  private lastRequestTime = 0;
  private readonly RATE_LIMIT_DELAY = 1000; // 1 second between requests

  private async makeRequest<T>(endpoint: string, params: Record<string, any> = {}): Promise<T> {
    // Simple rate limiting
    const now = Date.now();
    if (now - this.lastRequestTime < this.RATE_LIMIT_DELAY) {
      await new Promise(resolve => setTimeout(resolve, this.RATE_LIMIT_DELAY));
    }
    this.lastRequestTime = Date.now();
    this.requestCount++;

    const url = new URL(`${this.baseUrl}${endpoint}`);
    Object.keys(params).forEach(key => {
      if (params[key] !== undefined && params[key] !== null) {
        url.searchParams.append(key, params[key].toString());
      }
    });

    console.log(`🌐 CoinGecko API Request #${this.requestCount}: ${url.toString()}`);

    try {
      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log(`✅ CoinGecko API Response #${this.requestCount}: Success`);
      return data;
    } catch (error) {
      console.error(`❌ CoinGecko API Request #${this.requestCount} failed:`, error);
      throw error;
    }
  }

  /**
   * Get market data for cryptocurrencies
   */
  async getMarketData(options: {
    vs_currency?: string;
    ids?: string;
    order?: string;
    per_page?: number;
    page?: number;
    sparkline?: boolean;
    price_change_percentage?: string;
  } = {}): Promise<CoinGeckoMarketData[]> {
    const defaultOptions = {
      vs_currency: 'usd',
      order: 'market_cap_desc',
      per_page: 20,
      page: 1,
      sparkline: false,
      price_change_percentage: '1h,24h,7d',
    };

    const params = { ...defaultOptions, ...options };
    return this.makeRequest<CoinGeckoMarketData[]>('/coins/markets', params);
  }

  /**
   * Get global cryptocurrency market data
   */
  async getGlobalData(): Promise<CoinGeckoGlobalData> {
    return this.makeRequest<CoinGeckoGlobalData>('/global');
  }

  /**
   * Get trending cryptocurrencies
   */
  async getTrendingCoins(): Promise<CoinGeckoTrendingData> {
    return this.makeRequest<CoinGeckoTrendingData>('/search/trending');
  }

  /**
   * Get specific coins by IDs
   */
  async getCoinsByIds(ids: string[]): Promise<CoinGeckoMarketData[]> {
    return this.getMarketData({
      ids: ids.join(','),
      sparkline: true,
      price_change_percentage: '1h,24h,7d',
    });
  }

  /**
   * Get top cryptocurrencies for homepage
   */
  async getTopCoins(limit: number = 10): Promise<CoinGeckoMarketData[]> {
    return this.getMarketData({
      per_page: limit,
      sparkline: true,
      price_change_percentage: '1h,24h,7d',
    });
  }

  /**
   * Search for cryptocurrencies
   */
  async searchCoins(query: string): Promise<any> {
    return this.makeRequest(`/search`, { query });
  }

  /**
   * Get detailed information about a specific coin
   */
  async getCoinDetails(id: string): Promise<any> {
    return this.makeRequest(`/coins/${id}`, {
      localization: false,
      tickers: false,
      market_data: true,
      community_data: false,
      developer_data: false,
      sparkline: true,
    });
  }
}

// Create and export a singleton instance
export const coinGeckoApi = new CoinGeckoApiService();
export default coinGeckoApi;
