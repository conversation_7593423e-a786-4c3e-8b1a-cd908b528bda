import { useState, useEffect, useCallback } from 'react';
import { coinGeckoApi, CoinGeckoMarketData } from '../services/coinGeckoApi';

export interface LiveCryptoData {
  id: string;
  name: string;
  symbol: string;
  price: string;
  change: string;
  changePercent: number;
  positive: boolean;
  image: string;
  marketCap: number;
  volume: number;
  rank: number;
  sparkline?: number[];
}

export interface UseLiveCryptoDataResult {
  data: LiveCryptoData[];
  loading: boolean;
  error: string | null;
  refetch: () => void;
  lastUpdated: Date | null;
}

export const useLiveCryptoData = (coinIds?: string[], limit: number = 10): UseLiveCryptoDataResult => {
  const [data, setData] = useState<LiveCryptoData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const formatPrice = (price: number): string => {
    if (price >= 1) {
      return `$${price.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    } else {
      return `$${price.toFixed(6)}`;
    }
  };

  const formatChange = (change: number): string => {
    const sign = change >= 0 ? '+' : '';
    return `${sign}${change.toFixed(2)}%`;
  };

  const transformCoinGeckoData = (coins: CoinGeckoMarketData[]): LiveCryptoData[] => {
    return coins.map(coin => ({
      id: coin.id,
      name: coin.name,
      symbol: coin.symbol.toUpperCase(),
      price: formatPrice(coin.current_price),
      change: formatChange(coin.price_change_percentage_24h || 0),
      changePercent: coin.price_change_percentage_24h || 0,
      positive: (coin.price_change_percentage_24h || 0) >= 0,
      image: coin.image,
      marketCap: coin.market_cap,
      volume: coin.total_volume,
      rank: coin.market_cap_rank,
      sparkline: coin.sparkline_in_7d?.price,
    }));
  };

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      let coins: CoinGeckoMarketData[];
      
      if (coinIds && coinIds.length > 0) {
        // Fetch specific coins
        coins = await coinGeckoApi.getCoinsByIds(coinIds);
      } else {
        // Fetch top coins
        coins = await coinGeckoApi.getTopCoins(limit);
      }

      const transformedData = transformCoinGeckoData(coins);
      setData(transformedData);
      setLastUpdated(new Date());
      
      console.log(`✅ Successfully fetched ${transformedData.length} cryptocurrencies`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch cryptocurrency data';
      setError(errorMessage);
      console.error('❌ Error fetching crypto data:', err);
      
      // Fallback to mock data
      const mockData: LiveCryptoData[] = [
        {
          id: 'bitcoin',
          name: 'Bitcoin',
          symbol: 'BTC',
          price: '$43,250.00',
          change: '+2.5%',
          changePercent: 2.5,
          positive: true,
          image: 'https://assets.coingecko.com/coins/images/1/large/bitcoin.png',
          marketCap: 850000000000,
          volume: 25000000000,
          rank: 1,
        },
        {
          id: 'ethereum',
          name: 'Ethereum',
          symbol: 'ETH',
          price: '$2,650.00',
          change: '+1.8%',
          changePercent: 1.8,
          positive: true,
          image: 'https://assets.coingecko.com/coins/images/279/large/ethereum.png',
          marketCap: 320000000000,
          volume: 15000000000,
          rank: 2,
        },
        {
          id: 'cardano',
          name: 'Cardano',
          symbol: 'ADA',
          price: '$0.45',
          change: '-0.5%',
          changePercent: -0.5,
          positive: false,
          image: 'https://assets.coingecko.com/coins/images/975/large/cardano.png',
          marketCap: 16000000000,
          volume: 800000000,
          rank: 8,
        },
        {
          id: 'solana',
          name: 'Solana',
          symbol: 'SOL',
          price: '$98.50',
          change: '+3.2%',
          changePercent: 3.2,
          positive: true,
          image: 'https://assets.coingecko.com/coins/images/4128/large/solana.png',
          marketCap: 42000000000,
          volume: 2500000000,
          rank: 5,
        },
      ];
      
      setData(mockData);
      console.log('📊 Using fallback mock data');
    } finally {
      setLoading(false);
    }
  }, [coinIds, limit]);

  useEffect(() => {
    fetchData();

    // Set up auto-refresh every 30 seconds
    const interval = setInterval(fetchData, 30000);

    return () => clearInterval(interval);
  }, [fetchData]);

  return {
    data,
    loading,
    error,
    refetch: fetchData,
    lastUpdated,
  };
};
