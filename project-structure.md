# Cryptocurrency Market Application Structure

## Root Directory Structure

```
crypto-tracker/
├── frontend/               # React frontend application
├── server/                 # Node.js backend API
├── shared/                 # Shared types and utilities
├── config/                 # Configuration files
├── docs/                   # Documentation
├── .gitignore              # Git ignore file
├── package.json            # Root package.json for scripts
└── README.md               # Project documentation
```

## Frontend Structure

```
frontend/
├── public/                 # Public assets
│   ├── index.html          # HTML template
│   ├── favicon.ico         # Favicon
│   └── assets/             # Static assets
│       └── images/         # Image files
│           └── crypto-logos/ # Cryptocurrency logos
├── src/                    # Source code
│   ├── components/         # Reusable components
│   │   ├── common/         # Common UI components
│   │   │   ├── Button.tsx
│   │   │   ├── Card.tsx
│   │   │   ├── Input.tsx
│   │   │   ├── Loader.tsx
│   │   │   └── ErrorBoundary.tsx
│   │   ├── layout/         # Layout components
│   │   │   ├── Header.tsx
│   │   │   ├── Footer.tsx
│   │   │   ├── Sidebar.tsx
│   │   │   └── Layout.tsx
│   │   ├── market/         # Market-specific components
│   │   │   ├── CoinCarousel.tsx
│   │   │   ├── CryptoTable.tsx
│   │   │   ├── CryptoCard.tsx
│   │   │   ├── PriceChart.tsx
│   │   │   └── MarketStats.tsx
│   │   ├── crypto/         # Cryptocurrency components
│   │   │   ├── CryptoDetails.tsx
│   │   │   ├── CryptoHistory.tsx
│   │   │   └── CryptoConverter.tsx
│   │   ├── user/           # User-related components
│   │   │   ├── LoginForm.tsx
│   │   │   ├── SignupForm.tsx
│   │   │   └── UserProfile.tsx
│   │   ├── portfolio/      # Portfolio components
│   │   │   ├── PortfolioSummary.tsx
│   │   │   ├── PortfolioList.tsx
│   │   │   └── AddTransaction.tsx
│   │   ├── watchlist/      # Watchlist components
│   │   │   ├── WatchlistItem.tsx
│   │   │   └── WatchlistManager.tsx
│   │   └── ai/             # AI-related components
│   │       ├── AIInsights.tsx
│   │       ├── PricePrediction.tsx
│   │       └── SentimentAnalysis.tsx
│   ├── pages/              # Page components
│   │   ├── HomePage.tsx
│   │   ├── MarketPage.tsx
│   │   ├── CryptoDetailPage.tsx
│   │   ├── PortfolioPage.tsx
│   │   ├── WatchlistPage.tsx
│   │   ├── AIInsightsPage.tsx
│   │   ├── LoginPage.tsx
│   │   ├── SignupPage.tsx
│   │   ├── ProfilePage.tsx
│   │   ├── SettingsPage.tsx
│   │   └── NotFoundPage.tsx
│   ├── services/           # API services
│   │   ├── api.ts          # Base API configuration
│   │   ├── cryptoService.ts # Cryptocurrency data service
│   │   ├── authService.ts  # Authentication service
│   │   ├── portfolioService.ts # Portfolio service
│   │   ├── watchlistService.ts # Watchlist service
│   │   ├── aiService.ts    # AI insights service
│   │   └── websocketService.ts # Real-time data service
│   ├── hooks/              # Custom React hooks
│   │   ├── useCryptos.ts   # Hook for crypto data
│   │   ├── useAuth.ts      # Hook for authentication
│   │   ├── usePortfolio.ts # Hook for portfolio data
│   │   ├── useWatchlist.ts # Hook for watchlist data
│   │   └── useAI.ts        # Hook for AI insights
│   ├── contexts/           # React contexts
│   │   ├── AuthContext.tsx # Authentication context
│   │   ├── ThemeContext.tsx # Theme context
│   │   └── ApiContext.tsx  # API status context
│   ├── utils/              # Utility functions
│   │   ├── format.ts       # Formatting utilities
│   │   ├── storage.ts      # Local storage utilities
│   │   ├── validation.ts   # Form validation utilities
│   │   └── helpers.ts      # General helper functions
│   ├── styles/             # Styling
│   │   ├── theme.ts        # Theme configuration
│   │   ├── global.css      # Global styles
│   │   └── variables.css   # CSS variables
│   ├── types/              # TypeScript type definitions
│   │   ├── crypto.ts       # Cryptocurrency types
│   │   ├── user.ts         # User types
│   │   ├── portfolio.ts    # Portfolio types
│   │   └── api.ts          # API response types
│   ├── lib/                # Library configurations
│   │   ├── axios.ts        # Axios configuration
│   │   └── react-query.ts  # React Query configuration
│   ├── App.tsx             # Main App component
│   ├── main.tsx            # Entry point
│   ├── routes.tsx          # Application routes
│   └── vite-env.d.ts       # Vite environment types
├── .eslintrc.js            # ESLint configuration
├── .prettierrc             # Prettier configuration
├── tsconfig.json           # TypeScript configuration
├── vite.config.ts          # Vite configuration
└── package.json            # Frontend dependencies
```

## Server Structure

```
server/
├── src/                    # Source code
│   ├── api/                # API routes
│   │   ├── routes/         # Route definitions
│   │   │   ├── auth.js     # Authentication routes
│   │   │   ├── cryptos.js  # Cryptocurrency routes
│   │   │   ├── portfolio.js # Portfolio routes
│   │   │   ├── watchlist.js # Watchlist routes
│   │   │   └── ai.js       # AI insights routes
│   │   ├── controllers/    # Route controllers
│   │   │   ├── authController.js
│   │   │   ├── cryptoController.js
│   │   │   ├── portfolioController.js
│   │   │   ├── watchlistController.js
│   │   │   └── aiController.js
│   │   └── middleware/     # API middleware
│   │       ├── auth.js     # Authentication middleware
│   │       ├── errorHandler.js # Error handling middleware
│   │       └── rateLimiter.js # Rate limiting middleware
│   ├── services/           # Business logic services
│   │   ├── coinGeckoService.js # CoinGecko API service
│   │   ├── authService.js  # Authentication service
│   │   ├── portfolioService.js # Portfolio service
│   │   ├── watchlistService.js # Watchlist service
│   │   └── aiService.js    # AI insights service
│   ├── models/             # Data models
│   │   ├── User.js         # User model
│   │   ├── Portfolio.js    # Portfolio model
│   │   ├── Watchlist.js    # Watchlist model
│   │   └── Transaction.js  # Transaction model
│   ├── utils/              # Utility functions
│   │   ├── cryptoApi.js    # Cryptocurrency API utilities
│   │   ├── jwt.js          # JWT utilities
│   │   ├── validation.js   # Validation utilities
│   │   └── logger.js       # Logging utilities
│   ├── config/             # Configuration
│   │   ├── database.js     # Database configuration
│   │   ├── auth.js         # Authentication configuration
│   │   └── api.js          # API configuration
│   ├── websocket/          # WebSocket server
│   │   ├── server.js       # WebSocket server setup
│   │   └── handlers.js     # WebSocket event handlers
│   ├── db/                 # Database setup
│   │   ├── connection.js   # Database connection
│   │   └── migrations/     # Database migrations
│   ├── app.js              # Express application setup
│   └── server.js           # Server entry point
├── .env.example            # Example environment variables
├── .eslintrc.js            # ESLint configuration
├── .prettierrc             # Prettier configuration
├── nodemon.json            # Nodemon configuration
└── package.json            # Backend dependencies
```

## Shared Directory

```
shared/
├── types/                  # Shared TypeScript types
│   ├── crypto.ts           # Cryptocurrency types
│   ├── user.ts             # User types
│   └── api.ts              # API response types
└── utils/                  # Shared utilities
    ├── constants.js        # Shared constants
    └── helpers.js          # Shared helper functions
```

## Configuration Directory

```
config/
├── default.json            # Default configuration
├── development.json        # Development configuration
├── production.json         # Production configuration
└── test.json               # Test configuration
```

## Documentation Directory

```
docs/
├── api/                    # API documentation
│   └── swagger.yaml        # Swagger API specification
├── setup.md                # Setup instructions
├── architecture.md         # Architecture documentation
└── user-guide.md           # User guide
```

## Key Features Based on the Screenshot

1. **Cryptocurrency Market Page**
   - Live prices and stats for cryptocurrencies
   - Market cap, 24h volume, BTC dominance, ETH dominance
   - Searchable cryptocurrency table
   - Tabs for All Cryptocurrencies, Top Gainers, Top Losers

2. **API Connection Status**
   - API status indicator
   - Fallback to mock data when API is unavailable
   - Troubleshooting guidance for API connection issues

3. **Navigation**
   - Home, Market, AI Insights sections
   - User authentication (Sign In, Sign Up)

4. **Responsive Design**
   - Mobile-friendly layout
   - Dark/light mode support

5. **Error Handling**
   - Graceful degradation when API is unavailable
   - Clear error messages and recovery options
