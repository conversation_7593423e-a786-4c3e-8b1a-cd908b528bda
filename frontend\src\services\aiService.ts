import api from './api';

export interface PricePrediction {
  currency: string;
  currentPrice: number;
  predictions: Array<{
    day: number;
    price: number;
    date: string;
  }>;
  confidence: number;
  methodology: string;
}

export interface TradingSignal {
  indicator: string;
  signal: string;
  strength: string;
  description: string;
}

export interface TradingSignals {
  currency: string;
  currentPrice: number;
  indicators: {
    sma20: number;
    sma50: number;
    ema12: number;
    ema26: number;
    macd: number;
    rsi: number;
  };
  signals: TradingSignal[];
  recommendation: string;
  timestamp: string;
}

export interface NewsItem {
  source: string;
  headline: string;
  url: string;
  sentiment: number;
  date: string;
}

export interface SocialSentiment {
  platform: string;
  sentiment: number;
  volume: number;
  trending: boolean;
}

export interface SentimentAnalysis {
  currency: string;
  news: NewsItem[];
  social: SocialSentiment[];
  overallSentiment: number;
  sentimentCategory: string;
  wordCloud: string[];
  timestamp: string;
}

export interface PortfolioRecommendation {
  riskTolerance: string;
  portfolio: Array<{
    currency: string;
    name: string;
    allocation: number;
    reasoning: string;
  }>;
  expectedReturn: string;
  volatility: string;
  rebalancingPeriod: string;
  timestamp: string;
}

export interface CryptoInsights {
  crypto: any;
  predictions: PricePrediction;
  signals: TradingSignals;
  sentiment: SentimentAnalysis;
}

export interface AIResponse<T> {
  success: boolean;
  data: T;
}

const aiService = {
  // Get price predictions for a cryptocurrency
  getPricePrediction: async (id: string): Promise<PricePrediction> => {
    const response = await api.get<AIResponse<PricePrediction>>(`/ai/price-prediction/${id}`);
    return response.data.data;
  },

  // Get trading signals for a cryptocurrency
  getTradingSignals: async (id: string): Promise<TradingSignals> => {
    const response = await api.get<AIResponse<TradingSignals>>(`/ai/trading-signals/${id}`);
    return response.data.data;
  },

  // Get sentiment analysis for a cryptocurrency
  getSentimentAnalysis: async (id: string): Promise<SentimentAnalysis> => {
    const response = await api.get<AIResponse<SentimentAnalysis>>(`/ai/sentiment/${id}`);
    return response.data.data;
  },

  // Get portfolio recommendations
  getPortfolioRecommendations: async (riskTolerance?: string): Promise<PortfolioRecommendation> => {
    const params = riskTolerance ? { riskTolerance } : {};
    const response = await api.get<AIResponse<PortfolioRecommendation>>('/ai/portfolio-recommendations', { params });
    return response.data.data;
  },

  // Get all AI insights for a cryptocurrency
  getCryptoInsights: async (id: string): Promise<CryptoInsights> => {
    const response = await api.get<AIResponse<CryptoInsights>>(`/ai/insights/${id}`);
    return response.data.data;
  }
};

export default aiService;
