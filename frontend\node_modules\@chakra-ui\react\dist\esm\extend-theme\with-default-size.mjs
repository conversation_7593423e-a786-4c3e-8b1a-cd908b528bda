'use client';
import { isObject } from '@chakra-ui/utils';
import { mergeThemeOverride } from './extend-theme.mjs';

function withDefaultSize({
  size,
  components
}) {
  return (theme) => {
    let names = Object.keys(theme.components || {});
    if (Array.isArray(components)) {
      names = components;
    } else if (isObject(components)) {
      names = Object.keys(components);
    }
    return mergeThemeOverride(theme, {
      components: Object.fromEntries(
        names.map((componentName) => {
          const withSize = {
            defaultProps: {
              size
            }
          };
          return [componentName, withSize];
        })
      )
    });
  };
}

export { withDefaultSize };
