const axios = require('axios');

// CoinGecko API base URL
const COINGECKO_API_URL = 'https://api.coingecko.com/api/v3';

// Create axios instance with base URL
const coinGeckoApi = axios.create({
  baseURL: COINGECKO_API_URL,
  timeout: 10000, // Reduced timeout to 10 seconds for faster fallback
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json',
  },
});

// Add response interceptor for better error handling
coinGeckoApi.interceptors.response.use(
  response => response,
  error => {
    if (error.code === 'ECONNABORTED') {
      console.error('Request timeout:', error.message);
    } else if (error.response) {
      console.error('API error response:', error.response.status, error.response.data);
    } else if (error.request) {
      console.error('No response received:', error.request);
    } else {
      console.error('Request error:', error.message);
    }
    return Promise.reject(error);
  }
);

// Cache for API responses
const cache = {
  coins: { data: null, timestamp: 0 },
  global: { data: null, timestamp: 0 },
  trending: { data: null, timestamp: 0 },
};

// Cache expiration time (5 minutes)
const CACHE_EXPIRATION = 5 * 60 * 1000;

// Rate limiting
let lastRequestTime = 0;
const REQUEST_DELAY = 1500; // 1.5 seconds between requests to avoid rate limiting

// Helper function to delay requests
const delayRequest = async () => {
  const now = Date.now();
  const timeSinceLastRequest = now - lastRequestTime;

  if (timeSinceLastRequest < REQUEST_DELAY) {
    const delay = REQUEST_DELAY - timeSinceLastRequest;
    await new Promise(resolve => setTimeout(resolve, delay));
  }

  lastRequestTime = Date.now();
};

// Mock data for fallback
const mockCoins = [
  {
    id: 'bitcoin',
    symbol: 'btc',
    name: 'Bitcoin',
    image: 'https://assets.coingecko.com/coins/images/1/large/bitcoin.png',
    current_price: 50000,
    market_cap: 1000000000000,
    market_cap_rank: 1,
    total_volume: 50000000000,
    price_change_24h: 1000,
    price_change_percentage_24h: 2,
    price_change_percentage_1h_in_currency: 0.5,
    price_change_percentage_7d_in_currency: 5,
    circulating_supply: 19000000,
    total_supply: 21000000,
    max_supply: 21000000,
    last_updated: new Date().toISOString(),
  },
  {
    id: 'ethereum',
    symbol: 'eth',
    name: 'Ethereum',
    image: 'https://assets.coingecko.com/coins/images/279/large/ethereum.png',
    current_price: 3000,
    market_cap: 400000000000,
    market_cap_rank: 2,
    total_volume: 20000000000,
    price_change_24h: 100,
    price_change_percentage_24h: 3.5,
    price_change_percentage_1h_in_currency: 0.2,
    price_change_percentage_7d_in_currency: 7,
    circulating_supply: 120000000,
    total_supply: 120000000,
    max_supply: null,
    last_updated: new Date().toISOString(),
  },
  {
    id: 'cardano',
    symbol: 'ada',
    name: 'Cardano',
    image: 'https://assets.coingecko.com/coins/images/975/large/cardano.png',
    current_price: 1.5,
    market_cap: 50000000000,
    market_cap_rank: 3,
    total_volume: 2000000000,
    price_change_24h: 0.05,
    price_change_percentage_24h: 3.5,
    price_change_percentage_1h_in_currency: 0.1,
    price_change_percentage_7d_in_currency: 10,
    circulating_supply: 33000000000,
    total_supply: 45000000000,
    max_supply: 45000000000,
    last_updated: new Date().toISOString(),
  },
  {
    id: 'solana',
    symbol: 'sol',
    name: 'Solana',
    image: 'https://assets.coingecko.com/coins/images/4128/large/solana.png',
    current_price: 100,
    market_cap: 30000000000,
    market_cap_rank: 4,
    total_volume: 1500000000,
    price_change_24h: 5,
    price_change_percentage_24h: 5,
    price_change_percentage_1h_in_currency: 0.3,
    price_change_percentage_7d_in_currency: 15,
    circulating_supply: 300000000,
    total_supply: 500000000,
    max_supply: null,
    last_updated: new Date().toISOString(),
  },
  {
    id: 'ripple',
    symbol: 'xrp',
    name: 'XRP',
    image: 'https://assets.coingecko.com/coins/images/44/large/xrp-symbol-white-128.png',
    current_price: 0.75,
    market_cap: 35000000000,
    market_cap_rank: 5,
    total_volume: 1800000000,
    price_change_24h: -0.02,
    price_change_percentage_24h: -2.5,
    price_change_percentage_1h_in_currency: -0.1,
    price_change_percentage_7d_in_currency: -5,
    circulating_supply: 46000000000,
    total_supply: 100000000000,
    max_supply: 100000000000,
    last_updated: new Date().toISOString(),
  },
];

// Mock global data
const mockGlobalData = {
  data: {
    active_cryptocurrencies: 10000,
    upcoming_icos: 0,
    ongoing_icos: 50,
    ended_icos: 3375,
    markets: 642,
    total_market_cap: {
      usd: 2000000000000,
    },
    total_volume: {
      usd: 100000000000,
    },
    market_cap_percentage: {
      btc: 42.5,
      eth: 18.2,
    },
    market_cap_change_percentage_24h_usd: 2.5,
    updated_at: Date.now(),
  },
};

// Mock trending coins
const mockTrendingCoins = {
  coins: [
    {
      item: {
        id: 'bitcoin',
        name: 'Bitcoin',
        symbol: 'BTC',
        market_cap_rank: 1,
        thumb: 'https://assets.coingecko.com/coins/images/1/thumb/bitcoin.png',
        small: 'https://assets.coingecko.com/coins/images/1/small/bitcoin.png',
        large: 'https://assets.coingecko.com/coins/images/1/large/bitcoin.png',
        slug: 'bitcoin',
        price_btc: 1,
        score: 0,
      },
    },
    {
      item: {
        id: 'ethereum',
        name: 'Ethereum',
        symbol: 'ETH',
        market_cap_rank: 2,
        thumb: 'https://assets.coingecko.com/coins/images/279/thumb/ethereum.png',
        small: 'https://assets.coingecko.com/coins/images/279/small/ethereum.png',
        large: 'https://assets.coingecko.com/coins/images/279/large/ethereum.png',
        slug: 'ethereum',
        price_btc: 0.06,
        score: 1,
      },
    },
    {
      item: {
        id: 'solana',
        name: 'Solana',
        symbol: 'SOL',
        market_cap_rank: 4,
        thumb: 'https://assets.coingecko.com/coins/images/4128/thumb/solana.png',
        small: 'https://assets.coingecko.com/coins/images/4128/small/solana.png',
        large: 'https://assets.coingecko.com/coins/images/4128/large/solana.png',
        slug: 'solana',
        price_btc: 0.002,
        score: 2,
      },
    },
  ],
};

/**
 * Get list of coins with market data
 * @param {Object} options - Options for the request
 * @param {number} options.page - Page number (default: 1)
 * @param {number} options.perPage - Number of results per page (default: 100, max: 250)
 * @param {string} options.vsCurrency - Currency to compare against (default: 'usd')
 * @param {string} options.order - Sort results by (default: 'market_cap_desc')
 * @param {boolean} options.sparkline - Include sparkline 7d data (default: false)
 * @returns {Promise<Array>} - List of coins with market data
 */
const getCoins = async (options = {}) => {
  const {
    page = 1,
    perPage = 100,
    vsCurrency = 'usd',
    order = 'market_cap_desc',
    sparkline = false,
  } = options;

  // Check cache first
  const cacheKey = `coins_${page}_${perPage}_${vsCurrency}_${order}_${sparkline}`;
  if (cache.coins.data && cache.coins.timestamp > Date.now() - CACHE_EXPIRATION) {
    console.log('Using cached coin data');
    return cache.coins.data;
  }

  try {
    // Delay request to avoid rate limiting
    await delayRequest();

    const response = await coinGeckoApi.get('/coins/markets', {
      params: {
        vs_currency: vsCurrency,
        order,
        per_page: perPage,
        page,
        sparkline,
        price_change_percentage: '1h,24h,7d',
      },
    });

    // Update cache
    cache.coins = {
      data: response.data,
      timestamp: Date.now(),
    };

    return response.data;
  } catch (error) {
    console.error('Error fetching coins from CoinGecko:', error);
    console.log('Using mock coin data as fallback');

    // Return mock data as fallback
    return mockCoins;
  }
};

/**
 * Get detailed data for a specific coin
 * @param {string} id - Coin ID (e.g., 'bitcoin')
 * @param {Object} options - Options for the request
 * @param {boolean} options.tickers - Include ticker data (default: false)
 * @param {boolean} options.marketData - Include market data (default: true)
 * @param {boolean} options.communityData - Include community data (default: true)
 * @param {boolean} options.developerData - Include developer data (default: true)
 * @param {boolean} options.sparkline - Include sparkline 7d data (default: false)
 * @returns {Promise<Object>} - Detailed coin data
 */
const getCoinDetails = async (id, options = {}) => {
  const {
    tickers = false,
    marketData = true,
    communityData = true,
    developerData = true,
    sparkline = false,
  } = options;

  try {
    // Delay request to avoid rate limiting
    await delayRequest();

    const response = await coinGeckoApi.get(`/coins/${id}`, {
      params: {
        tickers,
        market_data: marketData,
        community_data: communityData,
        developer_data: developerData,
        sparkline,
      },
    });

    return response.data;
  } catch (error) {
    console.error(`Error fetching details for coin ${id} from CoinGecko:`, error);

    // Find matching mock coin or use the first one
    const mockCoin = mockCoins.find(coin =>
      coin.id === id || coin.symbol.toLowerCase() === id.toLowerCase()
    ) || mockCoins[0];

    // Create a mock detailed response
    const mockCoinDetails = {
      id: mockCoin.id,
      symbol: mockCoin.symbol,
      name: mockCoin.name,
      description: { en: `${mockCoin.name} is a cryptocurrency used for digital transactions. This is mock data as the CoinGecko API is currently unavailable.` },
      image: {
        thumb: mockCoin.image.replace('/large/', '/thumb/'),
        small: mockCoin.image.replace('/large/', '/small/'),
        large: mockCoin.image,
      },
      market_data: {
        current_price: { usd: mockCoin.current_price },
        market_cap: { usd: mockCoin.market_cap },
        total_volume: { usd: mockCoin.total_volume },
        price_change_24h: mockCoin.price_change_24h,
        price_change_percentage_24h: mockCoin.price_change_percentage_24h,
        price_change_percentage_7d: mockCoin.price_change_percentage_7d_in_currency,
        price_change_percentage_30d: mockCoin.price_change_percentage_24h * 5,
        circulating_supply: mockCoin.circulating_supply,
        total_supply: mockCoin.total_supply,
        max_supply: mockCoin.max_supply,
        ath: { usd: mockCoin.current_price * 2 },
        ath_date: { usd: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString() },
        atl: { usd: mockCoin.current_price * 0.3 },
        atl_date: { usd: new Date(Date.now() - 300 * 24 * 60 * 60 * 1000).toISOString() },
      },
      last_updated: new Date().toISOString(),
      market_cap_rank: mockCoin.market_cap_rank,
      genesis_date: '2015-07-30',
      categories: ['Currency', 'Smart Contract Platform'],
      links: {
        homepage: [`https://${mockCoin.name.toLowerCase()}.org`],
        blockchain_site: [`https://explorer.${mockCoin.name.toLowerCase()}.org`],
        official_forum_url: [`https://forum.${mockCoin.name.toLowerCase()}.org`],
        chat_url: [`https://chat.${mockCoin.name.toLowerCase()}.org`],
        announcement_url: [`https://blog.${mockCoin.name.toLowerCase()}.org`],
        twitter_screen_name: mockCoin.name.toLowerCase(),
        facebook_username: mockCoin.name.toLowerCase(),
        telegram_channel_identifier: mockCoin.name.toLowerCase(),
        subreddit_url: `https://reddit.com/r/${mockCoin.name.toLowerCase()}`,
        repos_url: { github: [`https://github.com/${mockCoin.name.toLowerCase()}`] },
      },
      community_data: {
        twitter_followers: 100000,
        reddit_subscribers: 50000,
        telegram_channel_user_count: 25000,
      },
      developer_data: {
        forks: 100,
        stars: 500,
        subscribers: 200,
        total_issues: 50,
        closed_issues: 40,
        pull_requests_merged: 300,
        pull_request_contributors: 30,
        commit_count_4_weeks: 100,
      },
    };

    console.log(`Using mock data for coin ${id}`);
    return mockCoinDetails;
  }
};

/**
 * Get historical market data for a specific coin
 * @param {string} id - Coin ID (e.g., 'bitcoin')
 * @param {Object} options - Options for the request
 * @param {string} options.vsCurrency - Currency to compare against (default: 'usd')
 * @param {string} options.days - Data up to number of days ago (default: '30')
 * @param {string} options.interval - Data interval (default: 'daily')
 * @returns {Promise<Object>} - Historical market data
 */
const getCoinMarketChart = async (id, options = {}) => {
  const {
    vsCurrency = 'usd',
    days = '30',
    interval = 'daily',
  } = options;

  try {
    // Delay request to avoid rate limiting
    await delayRequest();

    const response = await coinGeckoApi.get(`/coins/${id}/market_chart`, {
      params: {
        vs_currency: vsCurrency,
        days,
        interval,
      },
    });

    return response.data;
  } catch (error) {
    console.error(`Error fetching market chart for coin ${id} from CoinGecko:`, error);

    // Find matching mock coin or use the first one
    const mockCoin = mockCoins.find(coin =>
      coin.id === id || coin.symbol.toLowerCase() === id.toLowerCase()
    ) || mockCoins[0];

    // Generate mock historical data
    const daysNum = parseInt(days) || 30;
    const now = Date.now();
    const mockPrices = [];
    const mockMarketCaps = [];
    const mockVolumes = [];

    // Generate data points
    for (let i = daysNum; i >= 0; i--) {
      const timestamp = now - (i * 24 * 60 * 60 * 1000);
      const randomFactor = 0.9 + (Math.random() * 0.2); // Random between 0.9 and 1.1

      mockPrices.push([timestamp, mockCoin.current_price * randomFactor]);
      mockMarketCaps.push([timestamp, mockCoin.market_cap * randomFactor]);
      mockVolumes.push([timestamp, mockCoin.total_volume * randomFactor]);
    }

    console.log(`Using mock market chart data for coin ${id}`);
    return {
      prices: mockPrices,
      market_caps: mockMarketCaps,
      total_volumes: mockVolumes,
    };
  }
};

/**
 * Get global cryptocurrency market data
 * @returns {Promise<Object>} - Global market data
 */
const getGlobalData = async () => {
  // Check cache first
  if (cache.global.data && cache.global.timestamp > Date.now() - CACHE_EXPIRATION) {
    console.log('Using cached global data');
    return cache.global.data;
  }

  try {
    // Delay request to avoid rate limiting
    await delayRequest();

    const response = await coinGeckoApi.get('/global');

    // Update cache
    cache.global = {
      data: response.data,
      timestamp: Date.now(),
    };

    return response.data;
  } catch (error) {
    console.error('Error fetching global data from CoinGecko:', error);
    console.log('Using mock global data as fallback');

    // Return mock data as fallback
    return mockGlobalData;
  }
};

/**
 * Get trending coins (top-7 trending coins on CoinGecko)
 * @returns {Promise<Object>} - Trending coins data
 */
const getTrendingCoins = async () => {
  // Check cache first
  if (cache.trending.data && cache.trending.timestamp > Date.now() - CACHE_EXPIRATION) {
    console.log('Using cached trending data');
    return cache.trending.data;
  }

  try {
    // Delay request to avoid rate limiting
    await delayRequest();

    const response = await coinGeckoApi.get('/search/trending');

    // Update cache
    cache.trending = {
      data: response.data,
      timestamp: Date.now(),
    };

    return response.data;
  } catch (error) {
    console.error('Error fetching trending coins from CoinGecko:', error);
    console.log('Using mock trending data as fallback');

    // Return mock data as fallback
    return mockTrendingCoins;
  }
};

/**
 * Search for coins, categories and markets
 * @param {string} query - Search query
 * @returns {Promise<Object>} - Search results
 */
const searchCoins = async (query) => {
  try {
    // Delay request to avoid rate limiting
    await delayRequest();

    const response = await coinGeckoApi.get('/search', {
      params: { query },
    });
    return response.data;
  } catch (error) {
    console.error(`Error searching for "${query}" on CoinGecko:`, error);

    // Filter mock coins based on query
    const lowercaseQuery = query.toLowerCase();
    const filteredMockCoins = mockCoins.filter(coin =>
      coin.name.toLowerCase().includes(lowercaseQuery) ||
      coin.symbol.toLowerCase().includes(lowercaseQuery)
    );

    // Format results to match CoinGecko API
    const mockSearchResults = {
      coins: filteredMockCoins.map(coin => ({
        id: coin.id,
        name: coin.name,
        symbol: coin.symbol,
        market_cap_rank: coin.market_cap_rank,
        large: coin.image,
        thumb: coin.image.replace('/large/', '/thumb/'),
      })),
      categories: [],
      exchanges: [],
      nfts: [],
    };

    console.log(`Using mock search results for query "${query}"`);
    return mockSearchResults;
  }
};

module.exports = {
  getCoins,
  getCoinDetails,
  getCoinMarketChart,
  getGlobalData,
  getTrendingCoins,
  searchCoins,
};
