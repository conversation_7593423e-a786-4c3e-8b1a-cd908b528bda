const express = require('express');
const cors = require('cors');
const path = require('path');

// Create Express app
const app = express();

// Middleware
app.use(cors({
  origin: ['http://localhost:3002', 'http://localhost:3003', 'http://127.0.0.1:3002', 'http://127.0.0.1:3003'],
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
}));
app.use(express.json());

// Handle preflight requests
app.options('*', cors());

// Mock data
const mockCryptos = [
  {
    _id: '1',
    name: 'Bitcoin',
    symbol: 'BTC',
    currentPrice: 50000,
    marketCap: 1000000000000,
    volume24h: 50000000000,
    priceChange24h: 1000,
    priceChangePercentage24h: 2,
    circulatingSupply: 19000000,
    totalSupply: 21000000,
    maxSupply: 21000000,
    lastUpdated: new Date(),
  },
  {
    _id: '2',
    name: 'Ether<PERSON>',
    symbol: 'ETH',
    currentPrice: 3000,
    marketCap: 400000000000,
    volume24h: 20000000000,
    priceChange24h: 100,
    priceChangePercentage24h: 3.5,
    circulatingSupply: 120000000,
    totalSupply: 120000000,
    maxSupply: null,
    lastUpdated: new Date(),
  },
  {
    _id: '3',
    name: 'Cardano',
    symbol: 'ADA',
    currentPrice: 1.5,
    marketCap: 50000000000,
    volume24h: 2000000000,
    priceChange24h: 0.05,
    priceChangePercentage24h: 3.5,
    circulatingSupply: 33000000000,
    totalSupply: 45000000000,
    maxSupply: 45000000000,
    lastUpdated: new Date(),
  },
  {
    _id: '4',
    name: 'Solana',
    symbol: 'SOL',
    currentPrice: 100,
    marketCap: 30000000000,
    volume24h: 1500000000,
    priceChange24h: 5,
    priceChangePercentage24h: 5,
    circulatingSupply: 300000000,
    totalSupply: 500000000,
    maxSupply: null,
    lastUpdated: new Date(),
  },
  {
    _id: '5',
    name: 'Ripple',
    symbol: 'XRP',
    currentPrice: 0.75,
    marketCap: 35000000000,
    volume24h: 1800000000,
    priceChange24h: -0.02,
    priceChangePercentage24h: -2.5,
    circulatingSupply: 46000000000,
    totalSupply: 100000000000,
    maxSupply: 100000000000,
    lastUpdated: new Date(),
  },
];

const mockPortfolios = [
  {
    _id: '1',
    user: '123456789',
    name: 'My Portfolio',
    items: [
      {
        _id: '101',
        crypto: '1',
        symbol: 'BTC',
        quantity: 0.5,
        purchasePrice: 45000,
        purchaseDate: new Date(),
      },
      {
        _id: '102',
        crypto: '2',
        symbol: 'ETH',
        quantity: 5,
        purchasePrice: 2800,
        purchaseDate: new Date(),
      },
    ],
    totalValue: 36500,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

const mockWatchlists = [
  {
    _id: '1',
    user: '123456789',
    name: 'My Watchlist',
    cryptos: ['1', '2', '3'],
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

// Routes
app.get('/', (req, res) => {
  res.json({
    message: 'API is running...',
  });
});

app.get('/api', (req, res) => {
  res.json({
    success: true,
    message: 'API is running...',
    version: '1.0.0'
  });
});

// User routes
app.post('/api/users/register', (req, res) => {
  res.json({
    success: true,
    data: {
      _id: '123456789',
      username: req.body.username || 'testuser',
      email: req.body.email || '<EMAIL>',
      token: 'test-token-123456789',
    },
  });
});

app.post('/api/users/login', (req, res) => {
  res.json({
    success: true,
    data: {
      _id: '123456789',
      username: 'testuser',
      email: req.body.email || '<EMAIL>',
      token: 'test-token-123456789',
    },
  });
});

app.get('/api/users/me', (req, res) => {
  res.json({
    success: true,
    data: {
      _id: '123456789',
      username: 'testuser',
      email: '<EMAIL>',
    },
  });
});

// Crypto routes
app.get('/api/cryptos', (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;
  const total = mockCryptos.length;

  res.json({
    success: true,
    data: {
      cryptos: mockCryptos,
      page,
      pages: Math.ceil(total / limit),
      total,
    },
  });
});

app.get('/api/cryptos/:id', (req, res) => {
  const crypto = mockCryptos.find(c => c._id === req.params.id) || mockCryptos[0];

  res.json({
    success: true,
    data: crypto,
  });
});

// Portfolio routes
app.get('/api/portfolios', (req, res) => {
  res.json({
    success: true,
    data: mockPortfolios,
  });
});

app.get('/api/portfolios/:id', (req, res) => {
  const portfolio = mockPortfolios.find(p => p._id === req.params.id) || mockPortfolios[0];

  res.json({
    success: true,
    data: portfolio,
  });
});

app.post('/api/portfolios', (req, res) => {
  const newPortfolio = {
    _id: Date.now().toString(),
    user: '123456789',
    name: req.body.name || 'New Portfolio',
    items: [],
    totalValue: 0,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  mockPortfolios.push(newPortfolio);

  res.status(201).json({
    success: true,
    data: newPortfolio,
  });
});

// Watchlist routes
app.get('/api/watchlists', (req, res) => {
  res.json({
    success: true,
    data: mockWatchlists,
  });
});

app.get('/api/watchlists/:id', (req, res) => {
  const watchlist = mockWatchlists.find(w => w._id === req.params.id) || mockWatchlists[0];

  res.json({
    success: true,
    data: watchlist,
  });
});

app.post('/api/watchlists', (req, res) => {
  const newWatchlist = {
    _id: Date.now().toString(),
    user: '123456789',
    name: req.body.name || 'New Watchlist',
    cryptos: [],
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  mockWatchlists.push(newWatchlist);

  res.status(201).json({
    success: true,
    data: newWatchlist,
  });
});

// Add logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Start server
const PORT = 3001;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Server time: ${new Date().toISOString()}`);
  console.log('Available routes:');
  console.log('  GET  /');
  console.log('  GET  /api');
  console.log('  GET  /api/cryptos');
  console.log('  GET  /api/cryptos/:id');
  console.log('  POST /api/users/login');
  console.log('  POST /api/users/register');
  console.log('  GET  /api/users/me');
  console.log('  GET  /api/portfolios');
  console.log('  POST /api/portfolios');
  console.log('  GET  /api/watchlists');
  console.log('  POST /api/watchlists');
});
