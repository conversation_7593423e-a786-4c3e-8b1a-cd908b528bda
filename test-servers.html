<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Server Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            font-weight: bold;
            margin-bottom: 10px;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .pending {
            background-color: #fff3cd;
            color: #856404;
        }
        button {
            padding: 8px 16px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0069d9;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Crypto Tracker Server Test</h1>
    
    <div class="test-section">
        <div class="test-title">Backend Server (http://localhost:3001)</div>
        <button onclick="testBackendServer()">Test Backend Server</button>
        <div id="backend-result" class="test-result pending">Click the button to test the backend server</div>
        <pre id="backend-details" style="display: none;"></pre>
    </div>
    
    <div class="test-section">
        <div class="test-title">Frontend Server (http://localhost:3002)</div>
        <button onclick="testFrontendServer()">Test Frontend Server</button>
        <div id="frontend-result" class="test-result pending">Click the button to test the frontend server</div>
        <pre id="frontend-details" style="display: none;"></pre>
    </div>
    
    <div class="test-section">
        <div class="test-title">API Health Endpoint (http://localhost:3001/api/health)</div>
        <button onclick="testHealthEndpoint()">Test Health Endpoint</button>
        <div id="health-result" class="test-result pending">Click the button to test the health endpoint</div>
        <pre id="health-details" style="display: none;"></pre>
    </div>
    
    <div class="test-section">
        <div class="test-title">WebSocket Connection (ws://localhost:3001)</div>
        <button onclick="testWebSocketConnection()">Test WebSocket Connection</button>
        <div id="websocket-result" class="test-result pending">Click the button to test the WebSocket connection</div>
        <pre id="websocket-details" style="display: none;"></pre>
    </div>
    
    <script>
        // Test the backend server
        async function testBackendServer() {
            const resultElement = document.getElementById('backend-result');
            const detailsElement = document.getElementById('backend-details');
            
            resultElement.className = 'test-result pending';
            resultElement.textContent = 'Testing backend server...';
            detailsElement.style.display = 'none';
            
            try {
                const response = await fetch('http://localhost:3001/api', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    },
                    mode: 'cors',
                    cache: 'no-cache',
                    timeout: 5000
                });
                
                const data = await response.json();
                
                resultElement.className = 'test-result success';
                resultElement.textContent = 'Backend server is running!';
                detailsElement.textContent = JSON.stringify(data, null, 2);
                detailsElement.style.display = 'block';
            } catch (error) {
                resultElement.className = 'test-result error';
                resultElement.textContent = 'Backend server is not running or not accessible';
                detailsElement.textContent = error.toString();
                detailsElement.style.display = 'block';
            }
        }
        
        // Test the frontend server
        async function testFrontendServer() {
            const resultElement = document.getElementById('frontend-result');
            const detailsElement = document.getElementById('frontend-details');
            
            resultElement.className = 'test-result pending';
            resultElement.textContent = 'Testing frontend server...';
            detailsElement.style.display = 'none';
            
            try {
                // Since we're already on the frontend server if this page loads,
                // we'll just check if we can access the index.html
                const response = await fetch('http://localhost:3002/', {
                    method: 'GET',
                    headers: {
                        'Accept': 'text/html'
                    },
                    mode: 'no-cors', // This is needed because we're just checking if the server responds
                    cache: 'no-cache',
                    timeout: 5000
                });
                
                resultElement.className = 'test-result success';
                resultElement.textContent = 'Frontend server is running!';
                detailsElement.textContent = 'Status: ' + response.status + ' ' + response.statusText;
                detailsElement.style.display = 'block';
            } catch (error) {
                resultElement.className = 'test-result error';
                resultElement.textContent = 'Frontend server is not running or not accessible';
                detailsElement.textContent = error.toString();
                detailsElement.style.display = 'block';
            }
        }
        
        // Test the health endpoint
        async function testHealthEndpoint() {
            const resultElement = document.getElementById('health-result');
            const detailsElement = document.getElementById('health-details');
            
            resultElement.className = 'test-result pending';
            resultElement.textContent = 'Testing health endpoint...';
            detailsElement.style.display = 'none';
            
            try {
                const response = await fetch('http://localhost:3001/api/health', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    },
                    mode: 'cors',
                    cache: 'no-cache',
                    timeout: 5000
                });
                
                const data = await response.json();
                
                resultElement.className = 'test-result success';
                resultElement.textContent = 'Health endpoint is accessible!';
                detailsElement.textContent = JSON.stringify(data, null, 2);
                detailsElement.style.display = 'block';
            } catch (error) {
                resultElement.className = 'test-result error';
                resultElement.textContent = 'Health endpoint is not accessible';
                detailsElement.textContent = error.toString();
                detailsElement.style.display = 'block';
            }
        }
        
        // Test WebSocket connection
        function testWebSocketConnection() {
            const resultElement = document.getElementById('websocket-result');
            const detailsElement = document.getElementById('websocket-details');
            
            resultElement.className = 'test-result pending';
            resultElement.textContent = 'Testing WebSocket connection...';
            detailsElement.style.display = 'none';
            
            try {
                const ws = new WebSocket('ws://localhost:3001');
                
                ws.onopen = () => {
                    resultElement.className = 'test-result success';
                    resultElement.textContent = 'WebSocket connection established!';
                    detailsElement.textContent = 'Connected to ws://localhost:3001';
                    detailsElement.style.display = 'block';
                    
                    // Send a test message
                    ws.send(JSON.stringify({
                        type: 'test',
                        message: 'Hello from test page'
                    }));
                };
                
                ws.onmessage = (event) => {
                    detailsElement.textContent += '\n\nReceived message: ' + event.data;
                };
                
                ws.onerror = (error) => {
                    resultElement.className = 'test-result error';
                    resultElement.textContent = 'WebSocket connection failed';
                    detailsElement.textContent = 'Error connecting to WebSocket server';
                    detailsElement.style.display = 'block';
                };
                
                ws.onclose = () => {
                    detailsElement.textContent += '\n\nWebSocket connection closed';
                };
                
                // Close the connection after 5 seconds
                setTimeout(() => {
                    if (ws.readyState === WebSocket.OPEN) {
                        ws.close();
                    }
                }, 5000);
            } catch (error) {
                resultElement.className = 'test-result error';
                resultElement.textContent = 'WebSocket connection failed';
                detailsElement.textContent = error.toString();
                detailsElement.style.display = 'block';
            }
        }
    </script>
</body>
</html>
