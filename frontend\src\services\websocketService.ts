/**
 * WebSocket Service for real-time cryptocurrency price updates
 */

// WebSocket connection URL from environment variables
const WS_URL = import.meta.env.VITE_WS_URL || 'ws://localhost:3001';

// Event types
export enum WebSocketEventType {
  CONNECT = 'connect',
  DISCONNECT = 'disconnect',
  PRICE_UPDATE = 'priceUpdate',
  ERROR = 'error',
  SUBSCRIBED = 'subscribed',
}

// Event listener type
type WebSocketEventListener = (data: any) => void;

// WebSocket service class
class WebSocketService {
  private socket: WebSocket | null = null;
  private reconnectTimer: number | null = null;
  private eventListeners: Map<WebSocketEventType, WebSocketEventListener[]> = new Map();
  private subscribedCoins: string[] = [];
  private isConnected: boolean = false;
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 5;
  private reconnectDelay: number = 3000; // 3 seconds

  /**
   * Connect to the WebSocket server
   */
  public connect(): void {
    if (this.socket && (this.socket.readyState === WebSocket.OPEN || this.socket.readyState === WebSocket.CONNECTING)) {
      console.log('WebSocket already connected or connecting');
      return;
    }

    try {
      this.socket = new WebSocket(WS_URL);

      // Connection opened
      this.socket.addEventListener('open', () => {
        console.log('WebSocket connection established');
        this.isConnected = true;
        this.reconnectAttempts = 0;

        // Notify listeners
        this.notifyListeners(WebSocketEventType.CONNECT, { connected: true });

        // Resubscribe to coins if needed
        if (this.subscribedCoins.length > 0) {
          this.subscribeToCoins(this.subscribedCoins);
        }
      });

      // Listen for messages
      this.socket.addEventListener('message', (event) => {
        try {
          const data = JSON.parse(event.data);

          // Handle different message types
          if (data.type === 'priceUpdate') {
            this.notifyListeners(WebSocketEventType.PRICE_UPDATE, data.data);
          } else if (data.type === 'subscribed') {
            this.notifyListeners(WebSocketEventType.SUBSCRIBED, data);
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      });

      // Connection closed
      this.socket.addEventListener('close', (event) => {
        console.log(`WebSocket connection closed: ${event.code} ${event.reason}`);
        this.isConnected = false;
        this.notifyListeners(WebSocketEventType.DISCONNECT, {
          code: event.code,
          reason: event.reason
        });

        // Attempt to reconnect
        this.attemptReconnect();
      });

      // Connection error
      this.socket.addEventListener('error', (error) => {
        console.error('WebSocket error:', error);
        this.notifyListeners(WebSocketEventType.ERROR, { error });
      });
    } catch (error) {
      console.error('Failed to connect to WebSocket server:', error);
      this.notifyListeners(WebSocketEventType.ERROR, { error });
      this.attemptReconnect();
    }
  }

  /**
   * Disconnect from the WebSocket server
   */
  public disconnect(): void {
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }

    if (this.reconnectTimer) {
      window.clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    this.isConnected = false;
    this.reconnectAttempts = 0;
  }

  /**
   * Subscribe to price updates for specific coins
   * @param coinIds Array of coin IDs to subscribe to
   */
  public subscribeToCoins(coinIds: string[]): void {
    if (!this.socket || this.socket.readyState !== WebSocket.OPEN) {
      // Save coins for when connection is established
      this.subscribedCoins = [...new Set([...this.subscribedCoins, ...coinIds])];

      // Connect if not already connecting
      if (!this.socket || this.socket.readyState !== WebSocket.CONNECTING) {
        this.connect();
      }
      return;
    }

    // Update subscribed coins
    this.subscribedCoins = [...new Set([...this.subscribedCoins, ...coinIds])];

    // Send subscription message
    this.socket.send(JSON.stringify({
      type: 'subscribe',
      channel: 'prices',
      coins: this.subscribedCoins,
    }));
  }

  /**
   * Unsubscribe from price updates for specific coins
   * @param coinIds Array of coin IDs to unsubscribe from
   */
  public unsubscribeFromCoins(coinIds: string[]): void {
    if (!this.socket || this.socket.readyState !== WebSocket.OPEN) {
      return;
    }

    // Update subscribed coins
    this.subscribedCoins = this.subscribedCoins.filter(id => !coinIds.includes(id));

    // Send updated subscription
    this.socket.send(JSON.stringify({
      type: 'subscribe',
      channel: 'prices',
      coins: this.subscribedCoins,
    }));
  }

  /**
   * Add event listener
   * @param eventType Event type to listen for
   * @param listener Callback function
   */
  public addEventListener(eventType: WebSocketEventType, listener: WebSocketEventListener): void {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, []);
    }

    const listeners = this.eventListeners.get(eventType);
    if (listeners && !listeners.includes(listener)) {
      listeners.push(listener);
    }
  }

  /**
   * Remove event listener
   * @param eventType Event type
   * @param listener Callback function to remove
   */
  public removeEventListener(eventType: WebSocketEventType, listener: WebSocketEventListener): void {
    if (!this.eventListeners.has(eventType)) {
      return;
    }

    const listeners = this.eventListeners.get(eventType);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index !== -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * Check if WebSocket is connected
   * @returns True if connected, false otherwise
   */
  public isSocketConnected(): boolean {
    return this.isConnected;
  }

  /**
   * Get list of subscribed coin IDs
   * @returns Array of subscribed coin IDs
   */
  public getSubscribedCoins(): string[] {
    return [...this.subscribedCoins];
  }

  /**
   * Attempt to reconnect to the WebSocket server
   */
  private attemptReconnect(): void {
    if (this.reconnectTimer) {
      window.clearTimeout(this.reconnectTimer);
    }

    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('Maximum reconnect attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(1.5, this.reconnectAttempts - 1);

    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

    this.reconnectTimer = window.setTimeout(() => {
      this.connect();
    }, delay);
  }

  /**
   * Notify all listeners of an event
   * @param eventType Event type
   * @param data Event data
   */
  private notifyListeners(eventType: WebSocketEventType, data: any): void {
    if (!this.eventListeners.has(eventType)) {
      return;
    }

    const listeners = this.eventListeners.get(eventType);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(data);
        } catch (error) {
          console.error(`Error in WebSocket event listener for ${eventType}:`, error);
        }
      });
    }
  }
}

// Create singleton instance
const websocketService = new WebSocketService();

export default websocketService;
