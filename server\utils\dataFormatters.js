/**
 * Data Formatters
 * 
 * Utility functions to format data from external APIs to match
 * the expected format for the frontend.
 */

/**
 * Format coin data from CoinGecko API to match frontend expectations
 * @param {Object} coin - Coin data from CoinGecko API
 * @param {boolean} isDetailed - Whether this is detailed coin data
 * @returns {Object} - Formatted coin data
 */
const formatCoinData = (coin, isDetailed = false) => {
  if (!coin) return null;
  
  // Basic coin data (for list view)
  const formattedCoin = {
    _id: coin.id,
    name: coin.name,
    symbol: coin.symbol.toUpperCase(),
    currentPrice: coin.current_price || (coin.market_data?.current_price?.usd || 0),
    marketCap: coin.market_cap || (coin.market_data?.market_cap?.usd || 0),
    volume24h: coin.total_volume || (coin.market_data?.total_volume?.usd || 0),
    priceChange24h: coin.price_change_24h || (coin.market_data?.price_change_24h || 0),
    priceChangePercentage24h: coin.price_change_percentage_24h || (coin.market_data?.price_change_percentage_24h || 0),
    circulatingSupply: coin.circulating_supply || (coin.market_data?.circulating_supply || 0),
    totalSupply: coin.total_supply || (coin.market_data?.total_supply || null),
    maxSupply: coin.max_supply || (coin.market_data?.max_supply || null),
    lastUpdated: coin.last_updated || new Date().toISOString(),
    image: coin.image || (typeof coin.image === 'string' ? coin.image : coin.image?.large || ''),
    rank: coin.market_cap_rank || 0,
  };
  
  // Add additional data for detailed view
  if (isDetailed) {
    formattedCoin.priceChangePercentage1h = coin.price_change_percentage_1h_in_currency?.usd || 
      (coin.market_data?.price_change_percentage_1h_in_currency?.usd || 0);
    
    formattedCoin.priceChangePercentage7d = coin.price_change_percentage_7d_in_currency?.usd || 
      (coin.market_data?.price_change_percentage_7d_in_currency?.usd || 0);
    
    formattedCoin.priceChangePercentage30d = coin.price_change_percentage_30d_in_currency?.usd || 
      (coin.market_data?.price_change_percentage_30d_in_currency?.usd || 0);
    
    formattedCoin.allTimeHigh = coin.market_data?.ath?.usd || 0;
    formattedCoin.allTimeHighDate = coin.market_data?.ath_date?.usd || null;
    formattedCoin.allTimeLow = coin.market_data?.atl?.usd || 0;
    formattedCoin.allTimeLowDate = coin.market_data?.atl_date?.usd || null;
    
    // Add description if available
    if (coin.description?.en) {
      formattedCoin.description = coin.description.en;
    }
    
    // Add links if available
    if (coin.links) {
      formattedCoin.links = {
        homepage: coin.links.homepage?.[0] || '',
        blockchain_site: coin.links.blockchain_site?.filter(Boolean) || [],
        official_forum_url: coin.links.official_forum_url?.filter(Boolean) || [],
        chat_url: coin.links.chat_url?.filter(Boolean) || [],
        announcement_url: coin.links.announcement_url?.filter(Boolean) || [],
        twitter_screen_name: coin.links.twitter_screen_name || '',
        facebook_username: coin.links.facebook_username || '',
        telegram_channel_identifier: coin.links.telegram_channel_identifier || '',
        subreddit_url: coin.links.subreddit_url || '',
        repos_url: coin.links.repos_url || { github: [], bitbucket: [] },
      };
    }
    
    // Add community data if available
    if (coin.community_data) {
      formattedCoin.communityData = {
        twitter_followers: coin.community_data.twitter_followers || 0,
        reddit_subscribers: coin.community_data.reddit_subscribers || 0,
        telegram_channel_user_count: coin.community_data.telegram_channel_user_count || 0,
      };
    }
    
    // Add developer data if available
    if (coin.developer_data) {
      formattedCoin.developerData = {
        forks: coin.developer_data.forks || 0,
        stars: coin.developer_data.stars || 0,
        subscribers: coin.developer_data.subscribers || 0,
        total_issues: coin.developer_data.total_issues || 0,
        closed_issues: coin.developer_data.closed_issues || 0,
        pull_requests_merged: coin.developer_data.pull_requests_merged || 0,
        pull_request_contributors: coin.developer_data.pull_request_contributors || 0,
        commit_count_4_weeks: coin.developer_data.commit_count_4_weeks || 0,
      };
    }
  }
  
  return formattedCoin;
};

/**
 * Format market chart data from CoinGecko API
 * @param {Object} marketChart - Market chart data from CoinGecko API
 * @returns {Object} - Formatted market chart data
 */
const formatMarketData = (marketChart) => {
  if (!marketChart) return null;
  
  return {
    prices: marketChart.prices || [],
    market_caps: marketChart.market_caps || [],
    total_volumes: marketChart.total_volumes || [],
  };
};

/**
 * Format global market data from CoinGecko API
 * @param {Object} globalData - Global market data from CoinGecko API
 * @returns {Object} - Formatted global market data
 */
const formatGlobalData = (globalData) => {
  if (!globalData || !globalData.data) return null;
  
  return {
    totalMarketCap: globalData.data.total_market_cap.usd || 0,
    total24hVolume: globalData.data.total_volume.usd || 0,
    btcDominance: globalData.data.market_cap_percentage.btc || 0,
    ethDominance: globalData.data.market_cap_percentage.eth || 0,
    activeCryptocurrencies: globalData.data.active_cryptocurrencies || 0,
    markets: globalData.data.markets || 0,
    marketCapChangePercentage24h: globalData.data.market_cap_change_percentage_24h_usd || 0,
    lastUpdated: globalData.data.updated_at || Date.now(),
  };
};

module.exports = {
  formatCoinData,
  formatMarketData,
  formatGlobalData,
};
