{"version": 3, "sources": ["../src/index.ts"], "sourcesContent": ["import { isDom } from \"@zag-js/dom-query\"\n\ntype Modality = \"keyboard\" | \"pointer\" | \"virtual\"\ntype HandlerEvent = PointerEvent | MouseEvent | KeyboardEvent | FocusEvent\ntype Handler = (modality: Modality, e: HandlerEvent | null) => void\ntype FocusVisibleCallback = (isFocusVisible: boolean) => void\n\nlet hasSetup = false\nlet modality: Modality | null = null\nlet hasEventBeforeFocus = false\nlet hasBlurredWindowRecently = false\n\nconst handlers = new Set<Handler>()\n\nfunction trigger(modality: Modality, event: HandlerEvent | null) {\n  handlers.forEach((handler) => handler(modality, event))\n}\n\nconst isMac = typeof window !== \"undefined\" && window.navigator != null ? /^Mac/.test(window.navigator.platform) : false\n\nfunction isValidKey(e: KeyboardEvent) {\n  return !(\n    e.metaKey ||\n    (!isMac && e.altKey) ||\n    e.ctrl<PERSON>ey ||\n    e.key === \"Control\" ||\n    e.key === \"Shift\" ||\n    e.key === \"Meta\"\n  )\n}\n\nfunction onKeyboardEvent(event: KeyboardEvent) {\n  hasEventBeforeFocus = true\n  if (isValidKey(event)) {\n    modality = \"keyboard\"\n    trigger(\"keyboard\", event)\n  }\n}\n\nfunction onPointerEvent(event: PointerEvent | MouseEvent) {\n  modality = \"pointer\"\n\n  if (event.type === \"mousedown\" || event.type === \"pointerdown\") {\n    hasEventBeforeFocus = true\n    const target = event.composedPath ? event.composedPath()[0] : event.target\n\n    let matches = false\n    try {\n      matches = (target as any).matches(\":focus-visible\")\n    } catch {}\n\n    if (matches) return\n    trigger(\"pointer\", event)\n  }\n}\n\nfunction isVirtualClick(event: MouseEvent | PointerEvent): boolean {\n  // JAWS/NVDA with Firefox.\n  if ((event as any).mozInputSource === 0 && event.isTrusted) return true\n  return event.detail === 0 && !(event as PointerEvent).pointerType\n}\n\nfunction onClickEvent(e: MouseEvent) {\n  if (isVirtualClick(e)) {\n    hasEventBeforeFocus = true\n    modality = \"virtual\"\n  }\n}\n\nfunction onWindowFocus(event: FocusEvent) {\n  // Firefox fires two extra focus events when the user first clicks into an iframe:\n  // first on the window, then on the document. We ignore these events so they don't\n  // cause keyboard focus rings to appear.\n  if (event.target === window || event.target === document) {\n    return\n  }\n\n  // An extra event is fired when the user first clicks inside an element with tabindex attribute.\n  // We ignore these events so they don't cause keyboard focus ring to appear.\n  if (event.target instanceof Element && event.target.hasAttribute(\"tabindex\")) {\n    return\n  }\n\n  // If a focus event occurs without a preceding keyboard or pointer event, switch to keyboard modality.\n  // This occurs, for example, when navigating a form with the next/previous buttons on iOS.\n  if (!hasEventBeforeFocus && !hasBlurredWindowRecently) {\n    modality = \"virtual\"\n    trigger(\"virtual\", event)\n  }\n\n  hasEventBeforeFocus = false\n  hasBlurredWindowRecently = false\n}\n\nfunction onWindowBlur() {\n  // When the window is blurred, reset state. This is necessary when tabbing out of the window,\n  // for example, since a subsequent focus event won't be fired.\n  hasEventBeforeFocus = false\n  hasBlurredWindowRecently = true\n}\n\nfunction isFocusVisible() {\n  return modality !== \"pointer\"\n}\n\nfunction setupGlobalFocusEvents() {\n  if (!isDom() || hasSetup) {\n    return\n  }\n\n  // Programmatic focus() calls shouldn't affect the current input modality.\n  // However, we need to detect other cases when a focus event occurs without\n  // a preceding user event (e.g. screen reader focus). Overriding the focus\n  // method on HTMLElement.prototype is a bit hacky, but works.\n  const { focus } = HTMLElement.prototype\n  HTMLElement.prototype.focus = function focusElement(...args) {\n    hasEventBeforeFocus = true\n    focus.apply(this, args)\n  }\n\n  document.addEventListener(\"keydown\", onKeyboardEvent, true)\n  document.addEventListener(\"keyup\", onKeyboardEvent, true)\n  document.addEventListener(\"click\", onClickEvent, true)\n\n  // Register focus events on the window so they are sure to happen\n  // before React's event listeners (registered on the document).\n  window.addEventListener(\"focus\", onWindowFocus, true)\n  window.addEventListener(\"blur\", onWindowBlur, false)\n\n  if (typeof PointerEvent !== \"undefined\") {\n    document.addEventListener(\"pointerdown\", onPointerEvent, true)\n    document.addEventListener(\"pointermove\", onPointerEvent, true)\n    document.addEventListener(\"pointerup\", onPointerEvent, true)\n  } else {\n    document.addEventListener(\"mousedown\", onPointerEvent, true)\n    document.addEventListener(\"mousemove\", onPointerEvent, true)\n    document.addEventListener(\"mouseup\", onPointerEvent, true)\n  }\n\n  hasSetup = true\n}\n\nexport function trackFocusVisible(fn: FocusVisibleCallback) {\n  setupGlobalFocusEvents()\n\n  fn(isFocusVisible())\n  const handler = () => fn(isFocusVisible())\n\n  handlers.add(handler)\n  return () => {\n    handlers.delete(handler)\n  }\n}\n\nexport function trackInteractionModality(fn: (value: Modality | null) => void) {\n  setupGlobalFocusEvents()\n\n  fn(modality)\n  const handler = () => fn(modality)\n\n  handlers.add(handler)\n  return () => {\n    handlers.delete(handler)\n  }\n}\n\nexport function setInteractionModality(value: Modality) {\n  modality = value\n  trigger(value, null)\n}\n\nexport function getInteractionModality() {\n  return modality\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAAsB;AAOtB,IAAI,WAAW;AACf,IAAI,WAA4B;AAChC,IAAI,sBAAsB;AAC1B,IAAI,2BAA2B;AAE/B,IAAM,WAAW,oBAAI,IAAa;AAElC,SAAS,QAAQA,WAAoB,OAA4B;AAC/D,WAAS,QAAQ,CAAC,YAAY,QAAQA,WAAU,KAAK,CAAC;AACxD;AAEA,IAAM,QAAQ,OAAO,WAAW,eAAe,OAAO,aAAa,OAAO,OAAO,KAAK,OAAO,UAAU,QAAQ,IAAI;AAEnH,SAAS,WAAW,GAAkB;AACpC,SAAO,EACL,EAAE,WACD,CAAC,SAAS,EAAE,UACb,EAAE,WACF,EAAE,QAAQ,aACV,EAAE,QAAQ,WACV,EAAE,QAAQ;AAEd;AAEA,SAAS,gBAAgB,OAAsB;AAC7C,wBAAsB;AACtB,MAAI,WAAW,KAAK,GAAG;AACrB,eAAW;AACX,YAAQ,YAAY,KAAK;AAAA,EAC3B;AACF;AAEA,SAAS,eAAe,OAAkC;AACxD,aAAW;AAEX,MAAI,MAAM,SAAS,eAAe,MAAM,SAAS,eAAe;AAC9D,0BAAsB;AACtB,UAAM,SAAS,MAAM,eAAe,MAAM,aAAa,EAAE,CAAC,IAAI,MAAM;AAEpE,QAAI,UAAU;AACd,QAAI;AACF,gBAAW,OAAe,QAAQ,gBAAgB;AAAA,IACpD,QAAQ;AAAA,IAAC;AAET,QAAI;AAAS;AACb,YAAQ,WAAW,KAAK;AAAA,EAC1B;AACF;AAEA,SAAS,eAAe,OAA2C;AAEjE,MAAK,MAAc,mBAAmB,KAAK,MAAM;AAAW,WAAO;AACnE,SAAO,MAAM,WAAW,KAAK,CAAE,MAAuB;AACxD;AAEA,SAAS,aAAa,GAAe;AACnC,MAAI,eAAe,CAAC,GAAG;AACrB,0BAAsB;AACtB,eAAW;AAAA,EACb;AACF;AAEA,SAAS,cAAc,OAAmB;AAIxC,MAAI,MAAM,WAAW,UAAU,MAAM,WAAW,UAAU;AACxD;AAAA,EACF;AAIA,MAAI,MAAM,kBAAkB,WAAW,MAAM,OAAO,aAAa,UAAU,GAAG;AAC5E;AAAA,EACF;AAIA,MAAI,CAAC,uBAAuB,CAAC,0BAA0B;AACrD,eAAW;AACX,YAAQ,WAAW,KAAK;AAAA,EAC1B;AAEA,wBAAsB;AACtB,6BAA2B;AAC7B;AAEA,SAAS,eAAe;AAGtB,wBAAsB;AACtB,6BAA2B;AAC7B;AAEA,SAAS,iBAAiB;AACxB,SAAO,aAAa;AACtB;AAEA,SAAS,yBAAyB;AAChC,MAAI,KAAC,wBAAM,KAAK,UAAU;AACxB;AAAA,EACF;AAMA,QAAM,EAAE,MAAM,IAAI,YAAY;AAC9B,cAAY,UAAU,QAAQ,SAAS,gBAAgB,MAAM;AAC3D,0BAAsB;AACtB,UAAM,MAAM,MAAM,IAAI;AAAA,EACxB;AAEA,WAAS,iBAAiB,WAAW,iBAAiB,IAAI;AAC1D,WAAS,iBAAiB,SAAS,iBAAiB,IAAI;AACxD,WAAS,iBAAiB,SAAS,cAAc,IAAI;AAIrD,SAAO,iBAAiB,SAAS,eAAe,IAAI;AACpD,SAAO,iBAAiB,QAAQ,cAAc,KAAK;AAEnD,MAAI,OAAO,iBAAiB,aAAa;AACvC,aAAS,iBAAiB,eAAe,gBAAgB,IAAI;AAC7D,aAAS,iBAAiB,eAAe,gBAAgB,IAAI;AAC7D,aAAS,iBAAiB,aAAa,gBAAgB,IAAI;AAAA,EAC7D,OAAO;AACL,aAAS,iBAAiB,aAAa,gBAAgB,IAAI;AAC3D,aAAS,iBAAiB,aAAa,gBAAgB,IAAI;AAC3D,aAAS,iBAAiB,WAAW,gBAAgB,IAAI;AAAA,EAC3D;AAEA,aAAW;AACb;AAEO,SAAS,kBAAkB,IAA0B;AAC1D,yBAAuB;AAEvB,KAAG,eAAe,CAAC;AACnB,QAAM,UAAU,MAAM,GAAG,eAAe,CAAC;AAEzC,WAAS,IAAI,OAAO;AACpB,SAAO,MAAM;AACX,aAAS,OAAO,OAAO;AAAA,EACzB;AACF;AAEO,SAAS,yBAAyB,IAAsC;AAC7E,yBAAuB;AAEvB,KAAG,QAAQ;AACX,QAAM,UAAU,MAAM,GAAG,QAAQ;AAEjC,WAAS,IAAI,OAAO;AACpB,SAAO,MAAM;AACX,aAAS,OAAO,OAAO;AAAA,EACzB;AACF;AAEO,SAAS,uBAAuB,OAAiB;AACtD,aAAW;AACX,UAAQ,OAAO,IAAI;AACrB;AAEO,SAAS,yBAAyB;AACvC,SAAO;AACT;", "names": ["modality"]}