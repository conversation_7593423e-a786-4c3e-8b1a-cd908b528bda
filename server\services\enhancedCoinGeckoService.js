/**
 * Enhanced CoinGecko API Service
 * 
 * This service provides an improved interface to the CoinGecko API with:
 * - Advanced rate limiting with exponential backoff
 * - Robust caching mechanism
 * - Comprehensive error handling
 * - Fallback to mock data when API is unavailable
 */

const axios = require('axios');
const config = require('../config/coinGecko');
const { mockCoins, mockGlobalData, mockTrendingCoins } = require('../data/mockCryptoData');

// Create axios instance with base URL and timeout
const coinGeckoApi = axios.create({
  baseURL: config.API_URL,
  timeout: config.TIMEOUT,
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json',
  },
});

// Add API key if available
// if (config.API_KEY) {
//   coinGeckoApi.defaults.headers.common['x-cg-pro-api-key'] = config.API_KEY;
// }

// Cache storage
const cache = {
  coinsList: { data: null, timestamp: 0 },
  coinsMarkets: new Map(),
  coinDetails: new Map(),
  marketChart: new Map(),
  global: { data: null, timestamp: 0 },
  trending: { data: null, timestamp: 0 },
  search: new Map(),
};

// Rate limiting variables
let lastRequestTime = 0;
let requestsThisMinute = 0;
let minuteStartTime = Date.now();

/**
 * Delay a request to respect rate limits
 * @returns {Promise<void>}
 */
const respectRateLimit = async () => {
  const now = Date.now();
  
  // Reset counter if a new minute has started
  if (now - minuteStartTime >= 60000) {
    requestsThisMinute = 0;
    minuteStartTime = now;
  }
  
  // Check if we've exceeded the rate limit
  if (requestsThisMinute >= config.RATE_LIMIT.MAX_REQUESTS_PER_MINUTE) {
    const timeToNextMinute = 60000 - (now - minuteStartTime);
    console.log(`Rate limit reached. Waiting ${timeToNextMinute}ms before next request.`);
    await new Promise(resolve => setTimeout(resolve, timeToNextMinute));
    requestsThisMinute = 0;
    minuteStartTime = Date.now();
    return respectRateLimit(); // Recursive call to ensure we're still respecting the rate limit
  }
  
  // Ensure minimum time between requests
  const timeSinceLastRequest = now - lastRequestTime;
  if (timeSinceLastRequest < config.RATE_LIMIT.MIN_REQUEST_INTERVAL) {
    const delay = config.RATE_LIMIT.MIN_REQUEST_INTERVAL - timeSinceLastRequest;
    await new Promise(resolve => setTimeout(resolve, delay));
  }
  
  // Update rate limiting variables
  lastRequestTime = Date.now();
  requestsThisMinute++;
};

/**
 * Make a request to the CoinGecko API with rate limiting and retries
 * @param {string} endpoint - API endpoint
 * @param {Object} params - Query parameters
 * @param {number} retryCount - Current retry count
 * @returns {Promise<any>} - API response data
 */
const makeRequest = async (endpoint, params = {}, retryCount = 0) => {
  try {
    // Respect rate limits
    await respectRateLimit();
    
    // Make the request
    const response = await coinGeckoApi.get(endpoint, { params });
    return response.data;
  } catch (error) {
    // Handle rate limiting errors (429)
    if (error.response && error.response.status === 429) {
      if (retryCount < config.RATE_LIMIT.MAX_RETRIES) {
        // Calculate delay with exponential backoff if enabled
        let delay = config.RATE_LIMIT.RETRY_DELAY;
        if (config.RATE_LIMIT.USE_EXPONENTIAL_BACKOFF) {
          delay = delay * Math.pow(config.RATE_LIMIT.BACKOFF_MULTIPLIER, retryCount);
        }
        
        console.log(`Rate limited by CoinGecko API. Retrying in ${delay}ms (retry ${retryCount + 1}/${config.RATE_LIMIT.MAX_RETRIES})`);
        await new Promise(resolve => setTimeout(resolve, delay));
        return makeRequest(endpoint, params, retryCount + 1);
      }
    }
    
    // Handle other errors
    console.error(`Error making request to ${endpoint}:`, error.message);
    throw error;
  }
};

/**
 * Get list of all coins (minimal data)
 * @returns {Promise<Array>} - List of all coins
 */
const getCoinsList = async () => {
  const cacheKey = 'coinsList';
  const cacheExpiration = config.CACHE.EXPIRATION_TIMES.COINS_LIST;
  
  // Check cache
  if (cache.coinsList.data && (Date.now() - cache.coinsList.timestamp) < cacheExpiration) {
    console.log('Using cached coins list');
    return cache.coinsList.data;
  }
  
  try {
    const data = await makeRequest(config.ENDPOINTS.COINS_LIST);
    
    // Update cache
    cache.coinsList = {
      data,
      timestamp: Date.now(),
    };
    
    return data;
  } catch (error) {
    console.error('Failed to get coins list:', error.message);
    throw error;
  }
};

/**
 * Get list of coins with market data
 * @param {Object} options - Options for the request
 * @returns {Promise<Array>} - List of coins with market data
 */
const getCoinsMarkets = async (options = {}) => {
  const {
    page = config.DEFAULT_PARAMS.COINS_MARKETS.page,
    perPage = config.DEFAULT_PARAMS.COINS_MARKETS.per_page,
    vsCurrency = config.DEFAULT_PARAMS.COINS_MARKETS.vs_currency,
    order = config.DEFAULT_PARAMS.COINS_MARKETS.order,
    sparkline = config.DEFAULT_PARAMS.COINS_MARKETS.sparkline,
    priceChangePercentage = config.DEFAULT_PARAMS.COINS_MARKETS.price_change_percentage,
  } = options;
  
  // Create cache key
  const cacheKey = `${vsCurrency}_${order}_${page}_${perPage}_${sparkline}_${priceChangePercentage}`;
  const cacheExpiration = config.CACHE.EXPIRATION_TIMES.COINS_MARKETS;
  
  // Check cache
  if (cache.coinsMarkets.has(cacheKey)) {
    const cachedData = cache.coinsMarkets.get(cacheKey);
    if ((Date.now() - cachedData.timestamp) < cacheExpiration) {
      console.log(`Using cached coins markets data for ${cacheKey}`);
      return cachedData.data;
    }
  }
  
  try {
    const data = await makeRequest(config.ENDPOINTS.COINS_MARKETS, {
      vs_currency: vsCurrency,
      order,
      per_page: perPage,
      page,
      sparkline,
      price_change_percentage: priceChangePercentage,
    });
    
    // Update cache
    cache.coinsMarkets.set(cacheKey, {
      data,
      timestamp: Date.now(),
    });
    
    // Limit cache size
    if (cache.coinsMarkets.size > config.CACHE.MAX_ITEMS.COINS_MARKETS) {
      // Delete oldest entry
      const oldestKey = [...cache.coinsMarkets.entries()]
        .sort((a, b) => a[1].timestamp - b[1].timestamp)[0][0];
      cache.coinsMarkets.delete(oldestKey);
    }
    
    return data;
  } catch (error) {
    console.error('Failed to get coins markets:', error.message);
    console.log('Using mock coin data as fallback');
    return mockCoins;
  }
};

/**
 * Get detailed data for a specific coin
 * @param {string} id - Coin ID (e.g., 'bitcoin')
 * @param {Object} options - Options for the request
 * @returns {Promise<Object>} - Detailed coin data
 */
const getCoinDetails = async (id, options = {}) => {
  const {
    tickers = false,
    marketData = true,
    communityData = true,
    developerData = true,
    sparkline = false,
  } = options;
  
  // Create cache key
  const cacheKey = `${id}_${tickers}_${marketData}_${communityData}_${developerData}_${sparkline}`;
  const cacheExpiration = config.CACHE.EXPIRATION_TIMES.COIN_DETAILS;
  
  // Check cache
  if (cache.coinDetails.has(cacheKey)) {
    const cachedData = cache.coinDetails.get(cacheKey);
    if ((Date.now() - cachedData.timestamp) < cacheExpiration) {
      console.log(`Using cached coin details for ${id}`);
      return cachedData.data;
    }
  }
  
  try {
    const endpoint = config.ENDPOINTS.COIN_DETAILS.replace('{id}', id);
    const data = await makeRequest(endpoint, {
      tickers,
      market_data: marketData,
      community_data: communityData,
      developer_data: developerData,
      sparkline,
    });
    
    // Update cache
    cache.coinDetails.set(cacheKey, {
      data,
      timestamp: Date.now(),
    });
    
    // Limit cache size
    if (cache.coinDetails.size > config.CACHE.MAX_ITEMS.COIN_DETAILS) {
      // Delete oldest entry
      const oldestKey = [...cache.coinDetails.entries()]
        .sort((a, b) => a[1].timestamp - b[1].timestamp)[0][0];
      cache.coinDetails.delete(oldestKey);
    }
    
    return data;
  } catch (error) {
    console.error(`Failed to get details for coin ${id}:`, error.message);
    
    // Find matching mock coin or use the first one
    const mockCoin = mockCoins.find(coin =>
      coin.id === id || coin.symbol.toLowerCase() === id.toLowerCase()
    ) || mockCoins[0];
    
    // Return mock data in the format expected by the frontend
    return {
      id: mockCoin.id,
      symbol: mockCoin.symbol,
      name: mockCoin.name,
      image: { large: mockCoin.image },
      market_data: {
        current_price: { usd: mockCoin.current_price },
        market_cap: { usd: mockCoin.market_cap },
        total_volume: { usd: mockCoin.total_volume },
        price_change_24h: mockCoin.price_change_24h,
        price_change_percentage_24h: mockCoin.price_change_percentage_24h,
        circulating_supply: mockCoin.circulating_supply,
        total_supply: mockCoin.total_supply,
        max_supply: mockCoin.max_supply,
      },
      last_updated: mockCoin.last_updated,
    };
  }
};

/**
 * Get market chart data for a specific coin
 * @param {string} id - Coin ID (e.g., 'bitcoin')
 * @param {Object} options - Options for the request
 * @returns {Promise<Object>} - Market chart data
 */
const getCoinMarketChart = async (id, options = {}) => {
  const {
    vsCurrency = config.DEFAULT_PARAMS.MARKET_CHART.vs_currency,
    days = config.DEFAULT_PARAMS.MARKET_CHART.days,
    interval = config.DEFAULT_PARAMS.MARKET_CHART.interval,
  } = options;
  
  // Create cache key
  const cacheKey = `${id}_${vsCurrency}_${days}_${interval}`;
  const cacheExpiration = config.CACHE.EXPIRATION_TIMES.MARKET_CHART;
  
  // Check cache
  if (cache.marketChart.has(cacheKey)) {
    const cachedData = cache.marketChart.get(cacheKey);
    if ((Date.now() - cachedData.timestamp) < cacheExpiration) {
      console.log(`Using cached market chart for ${id}`);
      return cachedData.data;
    }
  }
  
  try {
    const endpoint = config.ENDPOINTS.MARKET_CHART.replace('{id}', id);
    const data = await makeRequest(endpoint, {
      vs_currency: vsCurrency,
      days,
      interval,
    });
    
    // Update cache
    cache.marketChart.set(cacheKey, {
      data,
      timestamp: Date.now(),
    });
    
    // Limit cache size
    if (cache.marketChart.size > config.CACHE.MAX_ITEMS.MARKET_CHART) {
      // Delete oldest entry
      const oldestKey = [...cache.marketChart.entries()]
        .sort((a, b) => a[1].timestamp - b[1].timestamp)[0][0];
      cache.marketChart.delete(oldestKey);
    }
    
    return data;
  } catch (error) {
    console.error(`Failed to get market chart for coin ${id}:`, error.message);
    
    // Generate mock market chart data
    const mockCoin = mockCoins.find(coin =>
      coin.id === id || coin.symbol.toLowerCase() === id.toLowerCase()
    ) || mockCoins[0];
    
    const now = Date.now();
    const prices = [];
    const marketCaps = [];
    const totalVolumes = [];
    
    // Generate data points based on the requested days
    const numPoints = parseInt(days) || 30;
    const basePrice = mockCoin.current_price;
    const baseMarketCap = mockCoin.market_cap;
    const baseVolume = mockCoin.total_volume;
    
    for (let i = numPoints; i >= 0; i--) {
      const timestamp = now - (i * 24 * 60 * 60 * 1000);
      const randomFactor = 0.9 + (Math.random() * 0.2); // Random between 0.9 and 1.1
      
      prices.push([timestamp, basePrice * randomFactor]);
      marketCaps.push([timestamp, baseMarketCap * randomFactor]);
      totalVolumes.push([timestamp, baseVolume * randomFactor]);
    }
    
    return {
      prices,
      market_caps: marketCaps,
      total_volumes: totalVolumes,
    };
  }
};

/**
 * Get global cryptocurrency market data
 * @returns {Promise<Object>} - Global market data
 */
const getGlobalData = async () => {
  const cacheExpiration = config.CACHE.EXPIRATION_TIMES.GLOBAL;
  
  // Check cache
  if (cache.global.data && (Date.now() - cache.global.timestamp) < cacheExpiration) {
    console.log('Using cached global data');
    return cache.global.data;
  }
  
  try {
    const data = await makeRequest(config.ENDPOINTS.GLOBAL);
    
    // Update cache
    cache.global = {
      data,
      timestamp: Date.now(),
    };
    
    return data;
  } catch (error) {
    console.error('Failed to get global data:', error.message);
    console.log('Using mock global data as fallback');
    return mockGlobalData;
  }
};

/**
 * Get trending coins
 * @returns {Promise<Object>} - Trending coins data
 */
const getTrendingCoins = async () => {
  const cacheExpiration = config.CACHE.EXPIRATION_TIMES.TRENDING;
  
  // Check cache
  if (cache.trending.data && (Date.now() - cache.trending.timestamp) < cacheExpiration) {
    console.log('Using cached trending coins data');
    return cache.trending.data;
  }
  
  try {
    const data = await makeRequest(config.ENDPOINTS.TRENDING);
    
    // Update cache
    cache.trending = {
      data,
      timestamp: Date.now(),
    };
    
    return data;
  } catch (error) {
    console.error('Failed to get trending coins:', error.message);
    console.log('Using mock trending data as fallback');
    return mockTrendingCoins;
  }
};

/**
 * Search for coins, categories and markets
 * @param {string} query - Search query
 * @returns {Promise<Object>} - Search results
 */
const searchCoins = async (query) => {
  // Create cache key
  const cacheKey = query.toLowerCase();
  const cacheExpiration = config.CACHE.EXPIRATION_TIMES.SEARCH;
  
  // Check cache
  if (cache.search.has(cacheKey)) {
    const cachedData = cache.search.get(cacheKey);
    if ((Date.now() - cachedData.timestamp) < cacheExpiration) {
      console.log(`Using cached search results for "${query}"`);
      return cachedData.data;
    }
  }
  
  try {
    const data = await makeRequest(config.ENDPOINTS.SEARCH, { query });
    
    // Update cache
    cache.search.set(cacheKey, {
      data,
      timestamp: Date.now(),
    });
    
    // Limit cache size
    if (cache.search.size > config.CACHE.MAX_ITEMS.SEARCH) {
      // Delete oldest entry
      const oldestKey = [...cache.search.entries()]
        .sort((a, b) => a[1].timestamp - b[1].timestamp)[0][0];
      cache.search.delete(oldestKey);
    }
    
    return data;
  } catch (error) {
    console.error(`Failed to search for "${query}":`, error.message);
    
    // Filter mock coins based on query
    const lowercaseQuery = query.toLowerCase();
    const filteredMockCoins = mockCoins.filter(coin =>
      coin.name.toLowerCase().includes(lowercaseQuery) ||
      coin.symbol.toLowerCase().includes(lowercaseQuery)
    );
    
    // Format results to match CoinGecko API
    return {
      coins: filteredMockCoins.map(coin => ({
        id: coin.id,
        name: coin.name,
        symbol: coin.symbol,
        market_cap_rank: coin.market_cap_rank,
        large: coin.image,
        thumb: coin.image,
      })),
      categories: [],
      exchanges: [],
      nfts: [],
    };
  }
};

/**
 * Clear all caches or a specific cache
 * @param {string} [cacheType] - Specific cache to clear (optional)
 */
const clearCache = (cacheType) => {
  if (cacheType) {
    switch (cacheType) {
      case 'coinsList':
        cache.coinsList = { data: null, timestamp: 0 };
        console.log('Cleared coins list cache');
        break;
      case 'coinsMarkets':
        cache.coinsMarkets.clear();
        console.log('Cleared coins markets cache');
        break;
      case 'coinDetails':
        cache.coinDetails.clear();
        console.log('Cleared coin details cache');
        break;
      case 'marketChart':
        cache.marketChart.clear();
        console.log('Cleared market chart cache');
        break;
      case 'global':
        cache.global = { data: null, timestamp: 0 };
        console.log('Cleared global data cache');
        break;
      case 'trending':
        cache.trending = { data: null, timestamp: 0 };
        console.log('Cleared trending coins cache');
        break;
      case 'search':
        cache.search.clear();
        console.log('Cleared search cache');
        break;
      default:
        console.log(`Unknown cache type: ${cacheType}`);
    }
  } else {
    // Clear all caches
    cache.coinsList = { data: null, timestamp: 0 };
    cache.coinsMarkets.clear();
    cache.coinDetails.clear();
    cache.marketChart.clear();
    cache.global = { data: null, timestamp: 0 };
    cache.trending = { data: null, timestamp: 0 };
    cache.search.clear();
    console.log('Cleared all caches');
  }
};

/**
 * Get cache statistics
 * @returns {Object} - Cache statistics
 */
const getCacheStats = () => {
  return {
    coinsList: {
      hasData: !!cache.coinsList.data,
      age: cache.coinsList.data ? Date.now() - cache.coinsList.timestamp : null,
    },
    coinsMarkets: {
      size: cache.coinsMarkets.size,
      keys: [...cache.coinsMarkets.keys()],
    },
    coinDetails: {
      size: cache.coinDetails.size,
      keys: [...cache.coinDetails.keys()],
    },
    marketChart: {
      size: cache.marketChart.size,
      keys: [...cache.marketChart.keys()],
    },
    global: {
      hasData: !!cache.global.data,
      age: cache.global.data ? Date.now() - cache.global.timestamp : null,
    },
    trending: {
      hasData: !!cache.trending.data,
      age: cache.trending.data ? Date.now() - cache.trending.timestamp : null,
    },
    search: {
      size: cache.search.size,
      keys: [...cache.search.keys()],
    },
  };
};

module.exports = {
  getCoinsList,
  getCoinsMarkets,
  getCoinDetails,
  getCoinMarketChart,
  getGlobalData,
  getTrendingCoins,
  searchCoins,
  clearCache,
  getCacheStats,
};
