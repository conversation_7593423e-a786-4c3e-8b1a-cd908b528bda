'use client';
import { jsx } from 'react/jsx-runtime';
import { usePresence } from 'framer-motion';
import { useEffect } from 'react';
import { RemoveScroll } from 'react-remove-scroll';
import { useModalContext } from './modal.mjs';
import { useModalManager } from './modal-manager.mjs';
import { FocusLock } from '../focus-lock/focus-lock.mjs';

function ModalFocusScope(props) {
  const {
    autoFocus,
    trapFocus,
    dialogRef,
    initialFocusRef,
    blockScrollOnMount,
    allowPinchZoom,
    finalFocusRef,
    returnFocusOnClose,
    preserveScrollBarGap,
    lockFocusAcrossFrames,
    isOpen
  } = useModalContext();
  const [isPresent, safeToRemove] = usePresence();
  useEffect(() => {
    if (!isPresent && safeToRemove) {
      setTimeout(safeToRemove);
    }
  }, [isPresent, safeToRemove]);
  const index = useModalManager(dialogRef, isOpen);
  return /* @__PURE__ */ jsx(
    FocusLock,
    {
      autoFocus,
      isDisabled: !trapFocus,
      initialFocusRef,
      finalFocusRef,
      restoreFocus: returnFocusOnClose,
      contentRef: dialogRef,
      lockFocusAcrossFrames,
      children: /* @__PURE__ */ jsx(
        RemoveScroll,
        {
          removeScrollBar: !preserveScrollBarGap,
          allowPinchZoom,
          enabled: index === 1 && blockScrollOnMount,
          forwardProps: true,
          children: props.children
        }
      )
    }
  );
}

export { ModalFocusScope };
