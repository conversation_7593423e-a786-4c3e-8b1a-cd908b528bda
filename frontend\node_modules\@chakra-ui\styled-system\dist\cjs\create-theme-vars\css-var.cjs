'use strict';

function replaceWhiteSpace(value, replaceValue = "-") {
  return value.replace(/\s+/g, replaceValue);
}
function escape(value) {
  const valueStr = replaceWhiteSpace(value.toString());
  return escapeSymbol(escapeDot(valueStr));
}
function escapeDot(value) {
  if (value.includes("\\."))
    return value;
  const isDecimal = !Number.isInteger(parseFloat(value.toString()));
  return isDecimal ? value.replace(".", `\\.`) : value;
}
function escapeSymbol(value) {
  return value.replace(/[!-,/:-@[-^`{-~]/g, "\\$&");
}
function addPrefix(value, prefix = "") {
  return [prefix, value].filter(Boolean).join("-");
}
function toVarReference(name, fallback) {
  return `var(${name}${fallback ? `, ${fallback}` : ""})`;
}
function toVarDefinition(value, prefix = "") {
  return escape(`--${addPrefix(value, prefix)}`);
}
function cssVar(name, fallback, cssVarPrefix) {
  const cssVariable = toVarDefinition(name, cssVarPrefix);
  return {
    variable: cssVariable,
    reference: toVarReference(cssVariable, fallback)
  };
}
function defineCssVars(scope, keys) {
  const vars = {};
  for (const key of keys) {
    if (Array.isArray(key)) {
      const [name, fallback] = key;
      vars[name] = cssVar(`${scope}-${name}`, fallback);
      continue;
    }
    vars[key] = cssVar(`${scope}-${key}`);
  }
  return vars;
}

exports.addPrefix = addPrefix;
exports.cssVar = cssVar;
exports.defineCssVars = defineCssVars;
exports.toVarDefinition = toVarDefinition;
exports.toVarReference = toVarReference;
