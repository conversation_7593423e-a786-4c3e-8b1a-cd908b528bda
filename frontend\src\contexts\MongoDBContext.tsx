import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import mongodbService, { MongoDBStatus } from '../services/mongodbService';

// Use the MongoDBStatus type from mongodbService
type MongoDBInfo = MongoDBStatus;

interface MongoDBContextType {
  isConnected: boolean;
  isLoading: boolean;
  connectionInfo: MongoDBInfo | null;
  error: string | null;
  checkConnection: () => Promise<void>;
  lastChecked: Date | null;
}

const MongoDBContext = createContext<MongoDBContextType | undefined>(undefined);

export const useMongoDBConnection = () => {
  const context = useContext(MongoDBContext);
  if (context === undefined) {
    throw new Error('useMongoDBConnection must be used within a MongoDBProvider');
  }
  return context;
};

interface MongoDBProviderProps {
  children: ReactNode;
}

export const MongoDBProvider: React.FC<MongoDBProviderProps> = ({ children }) => {
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [connectionInfo, setConnectionInfo] = useState<MongoDBInfo | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [lastChecked, setLastChecked] = useState<Date | null>(null);

  const checkConnection = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const status = await mongodbService.checkConnection();
      setLastChecked(new Date());

      setConnectionInfo(status);
      setIsConnected(status.connected);

      if (!status.connected) {
        setError(status.error || 'MongoDB connection failed');
      }
    } catch (err) {
      console.error('Error checking MongoDB connection:', err);
      setIsConnected(false);
      setError('Failed to check MongoDB connection status');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Initialize MongoDB connection
    mongodbService.initConnection().then(() => {
      // Check connection on mount
      checkConnection();

      // Set up interval to check connection periodically
      const interval = setInterval(() => {
        checkConnection();
      }, 60000); // Check every minute

      return () => clearInterval(interval);
    });
  }, []);

  const value = {
    isConnected,
    isLoading,
    connectionInfo,
    error,
    checkConnection,
    lastChecked,
  };

  return (
    <MongoDBContext.Provider value={value}>
      {children}
    </MongoDBContext.Provider>
  );
};

export default MongoDBProvider;
