import { useQuery } from '@tanstack/react-query';
import aiService from '../services/aiService';

// Hook for price predictions
export const usePricePrediction = (cryptoId: string) => {
  return useQuery({
    queryKey: ['pricePrediction', cryptoId],
    queryFn: () => aiService.getPricePrediction(cryptoId),
    enabled: !!cryptoId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Hook for trading signals
export const useTradingSignals = (cryptoId: string) => {
  return useQuery({
    queryKey: ['tradingSignals', cryptoId],
    queryFn: () => aiService.getTradingSignals(cryptoId),
    enabled: !!cryptoId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Hook for sentiment analysis
export const useSentimentAnalysis = (cryptoId: string) => {
  return useQuery({
    queryKey: ['sentimentAnalysis', cryptoId],
    queryFn: () => aiService.getSentimentAnalysis(cryptoId),
    enabled: !!cryptoId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Hook for portfolio recommendations
export const usePortfolioRecommendations = (riskTolerance?: string) => {
  return useQuery({
    queryKey: ['portfolioRecommendations', riskTolerance],
    queryFn: () => aiService.getPortfolioRecommendations(riskTolerance),
    staleTime: 15 * 60 * 1000, // 15 minutes
  });
};

// Hook for all crypto insights
export const useCryptoInsights = (cryptoId: string) => {
  return useQuery({
    queryKey: ['cryptoInsights', cryptoId],
    queryFn: () => aiService.getCryptoInsights(cryptoId),
    enabled: !!cryptoId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};
