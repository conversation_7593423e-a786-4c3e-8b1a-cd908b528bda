/**
 * MongoDB Configuration
 *
 * This file handles the MongoDB connection setup using Mongoose.
 * It includes fallback to local MongoDB or mock data if connection fails.
 */

const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');

// Get MongoDB URI from environment variables
const MONGO_ATLAS_URI = process.env.MONGO_URI || 'mongodb+srv://ziqra:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0';
const MONGO_LOCAL_URI = 'mongodb://localhost:27017/crypto_tracker';

// Function to log connection errors to a file for debugging
const logConnectionError = (error, connectionType) => {
  const logDir = path.join(__dirname, '..', 'logs');
  const logFile = path.join(logDir, 'mongodb-connection.log');

  // Create logs directory if it doesn't exist
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }

  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${connectionType} connection error: ${error.message}\n${error.stack}\n\n`;

  // Append to log file
  fs.appendFileSync(logFile, logMessage);

  console.error(`MongoDB ${connectionType} connection error logged to ${logFile}`);
};

// Function to create a mock connection object when MongoDB is not available
const createMockConnection = () => {
  console.log('\n\ud83d\udcca Creating mock MongoDB connection for fallback mode');

  // Create a mock connection object
  const mockConn = {
    connection: {
      host: 'mock-db',
      name: 'mock-crypto-tracker',
      readyState: 1,
      on: (event, callback) => {}, // Mock event listener
    },
  };

  // Set global flag for mock mode
  global.USING_MOCK_DB = true;

  return mockConn;
};

// Connect to MongoDB with fallback options
const connectDB = async () => {
  // Connection options
  const options = {
    serverSelectionTimeoutMS: 10000, // 10 seconds
    socketTimeoutMS: 45000, // 45 seconds
    connectTimeoutMS: 10000, // 10 seconds
    maxPoolSize: 10, // Maximum number of connections in the pool
    minPoolSize: 2, // Minimum number of connections in the pool
    maxIdleTimeMS: 30000, // 30 seconds
    retryWrites: true,
    retryReads: true,
  };

  // Check if we should use mock data directly
  const useMockData = process.env.USE_MOCK_DATA === 'true';
  if (useMockData) {
    console.log('\n\u26a0\ufe0f Using mock data mode as configured in .env file');
    return createMockConnection();
  }

  // Check if we should use local MongoDB directly
  const useLocalMongoDB = process.env.USE_LOCAL_MONGODB === 'true';

  if (useLocalMongoDB) {
    console.log('\n⚠️ Using local MongoDB as configured in .env file');
    try {
      console.log('Attempting to connect to local MongoDB...');
      const conn = await mongoose.connect(MONGO_LOCAL_URI, options);

      console.log('\n✅ Local MongoDB Connected!');
      console.log(`Connected to: ${conn.connection.host}`);
      console.log(`Database name: ${conn.connection.name}`);

      return conn;
    } catch (localError) {
      console.warn(`\n⚠️ Could not connect to local MongoDB: ${localError.message}`);
      logConnectionError(localError, 'Local');

      // Fall through to mock mode
      console.log('\n⚠️ Falling back to mock data mode');
      return createMockConnection();
    }
  }

  // Try connecting to MongoDB Atlas
  try {
    console.log('Attempting to connect to MongoDB Atlas...');
    console.log(`Connection string: ${MONGO_ATLAS_URI?.replace(/\/\/([^:]+):([^@]+)@/, '//***:***@')}`);

    const conn = await mongoose.connect(MONGO_ATLAS_URI, options);

    console.log('\n✅ MongoDB Atlas Connected!');
    console.log(`Connected to: ${conn.connection.host}`);
    console.log(`Database name: ${conn.connection.name}`);
    console.log(`Connection state: ${mongoose.STATES[conn.connection.readyState]}`);

    // Set up connection event listeners
    mongoose.connection.on('error', (err) => {
      console.error('MongoDB connection error:', err);
      logConnectionError(err, 'runtime');
    });

    mongoose.connection.on('disconnected', () => {
      console.warn('MongoDB disconnected. Attempting to reconnect...');
    });

    mongoose.connection.on('reconnected', () => {
      console.log('MongoDB reconnected successfully!');
    });

    return conn;
  } catch (atlasError) {
    console.warn(`\n⚠️ Could not connect to MongoDB Atlas: ${atlasError.message}`);
    logConnectionError(atlasError, 'Atlas');

    // Try connecting to local MongoDB
    try {
      console.log('\nAttempting to connect to local MongoDB...');
      const conn = await mongoose.connect(MONGO_LOCAL_URI, options);

      console.log('\n✅ Local MongoDB Connected!');
      console.log(`Connected to: ${conn.connection.host}`);
      console.log(`Database name: ${conn.connection.name}`);

      return conn;
    } catch (localError) {
      console.warn(`\n⚠️ Could not connect to local MongoDB: ${localError.message}`);
      logConnectionError(localError, 'Local');

      console.error('\n❌ All MongoDB connection attempts failed.');
      console.log('\n⚠️ Continuing without MongoDB - some features will be limited');
      console.log('\n📝 Please check the connection string in your .env file and ensure MongoDB is running.');
      console.log('\n📋 Connection troubleshooting steps:');
      console.log('1. Verify the username and password in your connection string');
      console.log('2. Check if your IP address is whitelisted in MongoDB Atlas');
      console.log('3. Ensure the cluster name and database name are correct');
      console.log('4. Try installing and running MongoDB locally as a fallback');
      console.log('\n📊 The application will run with mock data for now.');

      // Return a mock connection
      return createMockConnection();
    }
  }
};

module.exports = connectDB;
