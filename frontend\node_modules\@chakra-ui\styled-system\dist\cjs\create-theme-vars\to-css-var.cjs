'use strict';

var utils = require('@chakra-ui/utils');
var createThemeVars = require('./create-theme-vars.cjs');
var themeTokens = require('./theme-tokens.cjs');

function toCSSVar(rawTheme) {
  const theme = themeTokens.omitVars(rawTheme);
  const {
    /**
     * This is more like a dictionary of tokens users will type `green.500`,
     * and their equivalent css variable.
     */
    cssMap,
    /**
     * The extracted css variables will be stored here, and used in
     * the emotion's <Global/> component to attach variables to `:root`
     */
    cssVars
  } = createThemeVars.createThemeVars(theme);
  const defaultCssVars = {
    "--chakra-ring-inset": "var(--chakra-empty,/*!*/ /*!*/)",
    "--chakra-ring-offset-width": "0px",
    "--chakra-ring-offset-color": "#fff",
    "--chakra-ring-color": "rgba(66, 153, 225, 0.6)",
    "--chakra-ring-offset-shadow": "0 0 #0000",
    "--chakra-ring-shadow": "0 0 #0000",
    "--chakra-space-x-reverse": "0",
    "--chakra-space-y-reverse": "0"
  };
  Object.assign(theme, {
    __cssVars: { ...defaultCssVars, ...cssVars },
    __cssMap: cssMap,
    __breakpoints: utils.analyzeBreakpoints(theme.breakpoints)
  });
  return theme;
}

exports.toCSSVar = toCSSVar;
