'use client';
'use strict';

const STORAGE_KEY = "chakra-ui-color-mode";
function createLocalStorageManager(key) {
  return {
    ssr: false,
    type: "localStorage",
    get(init) {
      if (!globalThis?.document)
        return init;
      let value;
      try {
        value = localStorage.getItem(key) || init;
      } catch (e) {
      }
      return value || init;
    },
    set(value) {
      try {
        localStorage.setItem(key, value);
      } catch (e) {
      }
    }
  };
}
const localStorageManager = createLocalStorageManager(STORAGE_KEY);
function parseCookie(cookie, key) {
  const match = cookie.match(new RegExp(`(^| )${key}=([^;]+)`));
  return match?.[2];
}
function createCookieStorageManager(key, cookie) {
  return {
    ssr: !!cookie,
    type: "cookie",
    get(init) {
      if (cookie)
        return parseCookie(cookie, key);
      if (!globalThis?.document)
        return init;
      return parseCookie(document.cookie, key) || init;
    },
    set(value) {
      document.cookie = `${key}=${value}; max-age=31536000; path=/`;
    }
  };
}
const cookieStorageManager = createCookieStorageManager(STORAGE_KEY);
const cookieStorageManagerSSR = (cookie) => createCookieStorageManager(STORAGE_KEY, cookie);

exports.STORAGE_KEY = STORAGE_KEY;
exports.cookieStorageManager = cookieStorageManager;
exports.cookieStorageManagerSSR = cookieStorageManagerSSR;
exports.createCookieStorageManager = createCookieStorageManager;
exports.createLocalStorageManager = createLocalStorageManager;
exports.localStorageManager = localStorageManager;
