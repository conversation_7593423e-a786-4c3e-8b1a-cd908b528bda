/**
 * Crypto Tracker API Server
 *
 * This is the main entry point for the Crypto Tracker API server.
 * It sets up Express, middleware, routes, and starts the server.
 */

const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const compression = require('compression');
const path = require('path');
const dotenv = require('dotenv');
const connectDB = require('./config/db');
const { errorHandler } = require('./middleware/errorMiddleware');

// Load environment variables
dotenv.config();

// Import routes
const cryptoRoutes = require('./routes/cryptoRoutes');
const authRoutes = require('./routes/authRoutes');
const portfolioRoutes = require('./routes/portfolioRoutes');
const watchlistRoutes = require('./routes/watchlistRoutes');
const aiRoutes = require('./routes/aiRoutes');

// Create Express app
const app = express();

// Set environment variables
const PORT = process.env.PORT || 3001;
const NODE_ENV = process.env.NODE_ENV || 'development';

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cors());
app.use(helmet());
app.use(compression());

// Logging
if (NODE_ENV === 'development') {
  app.use(morgan('dev'));
} else {
  app.use(morgan('combined'));
}

// Rate limiting
const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  standardHeaders: true,
  legacyHeaders: false,
  message: {
    success: false,
    message: 'Too many requests, please try again later.',
  },
});

// Apply rate limiting to API routes
app.use('/api', apiLimiter);

// API Routes
app.use('/api/cryptos', cryptoRoutes);
app.use('/api/auth', authRoutes);
app.use('/api/portfolios', portfolioRoutes);
app.use('/api/watchlists', watchlistRoutes);
app.use('/api/ai', aiRoutes);

// API Health Check
app.get('/api/health', (req, res) => {
  const mongoose = require('mongoose');
  const isConnected = global.isMongoDBConnected && mongoose.connection.readyState === 1;
  const usingMockData = global.USING_MOCK_DB === true;

  res.json({
    success: true,
    message: 'API is running',
    timestamp: new Date().toISOString(),
    environment: NODE_ENV,
    version: '1.0.0',
    mongodb: {
      connected: isConnected,
      host: isConnected ? mongoose.connection.host : (usingMockData ? 'mock-db' : null),
      name: isConnected ? mongoose.connection.name : (usingMockData ? 'mock-crypto-tracker' : null),
      error: !isConnected ? 'MongoDB not connected - running with limited functionality' : null,
      mockMode: usingMockData,
      fallbackMode: !isConnected && !usingMockData,
    },
    features: {
      cryptoMarket: true, // Always available (uses CoinGecko API or mock data)
      aiInsights: true,   // Always available (uses mock data if needed)
      authentication: true, // Basic auth works even in mock mode
      portfolios: isConnected, // Requires MongoDB
      watchlists: isConnected, // Requires MongoDB
      userRegistration: isConnected, // Requires MongoDB
    }
  });
});

// Serve static assets in production
if (NODE_ENV === 'production') {
  // Set static folder
  app.use(express.static(path.join(__dirname, '../frontend/dist')));

  // Any route that is not an API route will be served the React app
  app.get('*', (req, res) => {
    res.sendFile(path.resolve(__dirname, '../frontend/dist', 'index.html'));
  });
}

// Error handling middleware
app.use(errorHandler);

// Connect to MongoDB and start server
connectDB().then((connection) => {
  // Set a global flag for MongoDB connection status
  global.isMongoDBConnected = !!connection;

  // Start the server regardless of MongoDB connection status
  app.listen(PORT, () => {
    console.log(`\n\u2705 Server running in ${NODE_ENV} mode on port ${PORT}`);

    if (global.USING_MOCK_DB) {
      console.log('\n\u26a0\ufe0f MOCK DATA MODE: Using simulated data instead of MongoDB');
      console.log('\n\ud83d\udcdd The following features will work with mock data:');
      console.log('- Cryptocurrency market data');
      console.log('- AI insights and predictions');
      console.log('- User authentication (with predefined users)');

      console.log('\n\u26d4 The following features require MongoDB and will be limited:');
      console.log('- Portfolio management');
      console.log('- Watchlist management');
      console.log('- User registration');
    } else if (!global.isMongoDBConnected) {
      console.warn('\n\u26a0\ufe0f Running with limited functionality - MongoDB connection failed');
      console.warn('Some features requiring database access will not work properly');
    } else {
      console.log('\n\u2705 All features are available with MongoDB connection');
    }

    console.log('\n\ud83c\udf10 API available at: http://localhost:' + PORT + '/api');
    console.log('\ud83c\udf10 Frontend available at: http://localhost:3002');
  });
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
  console.error('Unhandled Promise Rejection:', err);
  // In production, we might want to exit the process and let a process manager restart it
  // process.exit(1);
});

module.exports = app;
