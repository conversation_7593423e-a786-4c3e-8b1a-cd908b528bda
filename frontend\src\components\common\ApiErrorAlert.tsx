import React, { useEffect, useState } from 'react';
import {
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Box,
  Button,
  CloseButton,
  Collapse,
  Flex,
  Spinner,
  Text,
  useDisclosure,
} from '@chakra-ui/react';
import { checkApiHealth } from '../../services/api';

interface ApiErrorState {
  isVisible: boolean;
  message: string;
  isRetrying: boolean;
  retryCount: number;
}

const ApiErrorAlert: React.FC = () => {
  const [errorState, setErrorState] = useState<ApiErrorState>({
    isVisible: false,
    message: '',
    isRetrying: false,
    retryCount: 0,
  });
  const { isOpen, onClose, onOpen } = useDisclosure({ defaultIsOpen: false });

  useEffect(() => {
    // Listen for API connection errors
    const handleConnectionError = (event: CustomEvent) => {
      setErrorState({
        isVisible: true,
        message: event.detail.message || 'Cannot connect to the server',
        isRetrying: false,
        retryCount: event.detail.retry || 0,
      });
      onOpen();
    };

    // Listen for API health check events
    const handleHealthCheck = (event: CustomEvent) => {
      if (!event.detail.isAvailable) {
        setErrorState({
          isVisible: true,
          message: event.detail.message || 'API server is not available',
          isRetrying: false,
          retryCount: 0,
        });
        onOpen();
      } else {
        // If the API is available again, close the alert
        setErrorState(prev => ({ ...prev, isVisible: false }));
        onClose();
      }
    };

    // Add event listeners
    window.addEventListener('api-connection-error', handleConnectionError as EventListener);
    window.addEventListener('api-health-check', handleHealthCheck as EventListener);
    window.addEventListener('api-network-error', handleConnectionError as EventListener);

    // Remove event listeners on cleanup
    return () => {
      window.removeEventListener('api-connection-error', handleConnectionError as EventListener);
      window.removeEventListener('api-health-check', handleHealthCheck as EventListener);
      window.removeEventListener('api-network-error', handleConnectionError as EventListener);
    };
  }, [onOpen, onClose]);

  const handleRetry = async () => {
    setErrorState(prev => ({ ...prev, isRetrying: true }));

    try {
      const result = await checkApiHealth();

      if (result.isAvailable) {
        setErrorState({
          isVisible: false,
          message: '',
          isRetrying: false,
          retryCount: 0,
        });
        onClose();

        // Reload the page to refresh data
        window.location.reload();
      } else {
        setErrorState(prev => ({
          ...prev,
          isRetrying: false,
          retryCount: prev.retryCount + 1,
          message: result.message || 'API server is still not available',
        }));
      }
    } catch (error) {
      setErrorState(prev => ({
        ...prev,
        isRetrying: false,
        retryCount: prev.retryCount + 1,
        message: 'Failed to check API health',
      }));
    }
  };

  if (!errorState.isVisible) {
    return null;
  }

  return (
    <Collapse in={isOpen} animateOpacity>
      <Box position="fixed" top="1rem" left="50%" transform="translateX(-50%)" zIndex="toast" width="90%" maxW="600px">
        <Alert status="warning" variant="solid" borderRadius="md" boxShadow="lg">
          <AlertIcon />
          <Box flex="1">
            <AlertTitle fontSize="md">API Connection Issue</AlertTitle>
            <AlertDescription fontSize="sm">
              {errorState.message}
              <Text fontSize="xs" mt={1}>
                {errorState.retryCount > 0
                  ? `Retry attempts: ${errorState.retryCount}`
                  : 'Using mock data as fallback'}
              </Text>
              <Text fontSize="xs" mt={1}>
                You can continue using the app with limited functionality.
              </Text>
            </AlertDescription>
          </Box>
          <Flex alignItems="center">
            <Button
              size="sm"
              colorScheme="orange"
              variant="outline"
              mr={2}
              onClick={handleRetry}
              isLoading={errorState.isRetrying}
              loadingText="Checking"
              spinner={<Spinner size="sm" />}
            >
              Retry
            </Button>
            <CloseButton size="sm" onClick={onClose} />
          </Flex>
        </Alert>
      </Box>
    </Collapse>
  );
};

export default ApiErrorAlert;
