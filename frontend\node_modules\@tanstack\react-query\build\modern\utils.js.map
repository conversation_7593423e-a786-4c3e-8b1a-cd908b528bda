{"version": 3, "sources": ["../../src/utils.ts"], "sourcesContent": ["export function shouldThrowError<T extends (...args: Array<any>) => boolean>(\n  throwError: boolean | T | undefined,\n  params: Parameters<T>,\n): boolean {\n  // Allow throwError function to override throwing behavior on a per-error basis\n  if (typeof throwError === 'function') {\n    return throwError(...params)\n  }\n\n  return !!throwError\n}\n\nexport function noop(): void {}\n"], "mappings": ";AAAO,SAAS,iBACd,YACA,QACS;AAET,MAAI,OAAO,eAAe,YAAY;AACpC,WAAO,WAAW,GAAG,MAAM;AAAA,EAC7B;AAEA,SAAO,CAAC,CAAC;AACX;AAEO,SAAS,OAAa;AAAC;", "names": []}