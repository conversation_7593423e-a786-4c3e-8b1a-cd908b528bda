/**
 * Watchlist Controller
 * 
 * This controller handles user watchlist management, including
 * creating, updating, and deleting watchlists and watchlist items.
 */

// Mock watchlist database (replace with real database in production)
const watchlists = [];

/**
 * Get all watchlists for a user
 * @route GET /api/watchlists
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} - JSON response with watchlists
 */
const getWatchlists = async (req, res) => {
  try {
    // Get user ID from authenticated request
    const userId = req.user.id;
    
    // Find watchlists for user
    const userWatchlists = watchlists.filter(watchlist => watchlist.userId === userId);
    
    return res.json({
      success: true,
      data: userWatchlists,
    });
  } catch (error) {
    console.error('Error in getWatchlists controller:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to get watchlists',
      error: error.message,
    });
  }
};

/**
 * Create a new watchlist
 * @route POST /api/watchlists
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} - JSON response with created watchlist
 */
const createWatchlist = async (req, res) => {
  try {
    const { name } = req.body;
    
    // Validate input
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Watchlist name is required',
      });
    }
    
    // Create new watchlist
    const newWatchlist = {
      id: (watchlists.length + 1).toString(),
      userId: req.user.id,
      name,
      cryptos: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    // Add watchlist to mock database
    watchlists.push(newWatchlist);
    
    return res.status(201).json({
      success: true,
      data: newWatchlist,
    });
  } catch (error) {
    console.error('Error in createWatchlist controller:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to create watchlist',
      error: error.message,
    });
  }
};

/**
 * Get a specific watchlist by ID
 * @route GET /api/watchlists/:id
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} - JSON response with watchlist
 */
const getWatchlistById = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Find watchlist by ID
    const watchlist = watchlists.find(w => w.id === id && w.userId === req.user.id);
    
    if (!watchlist) {
      return res.status(404).json({
        success: false,
        message: 'Watchlist not found',
      });
    }
    
    return res.json({
      success: true,
      data: watchlist,
    });
  } catch (error) {
    console.error('Error in getWatchlistById controller:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to get watchlist',
      error: error.message,
    });
  }
};

/**
 * Update a watchlist
 * @route PUT /api/watchlists/:id
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} - JSON response with updated watchlist
 */
const updateWatchlist = async (req, res) => {
  try {
    const { id } = req.params;
    const { name } = req.body;
    
    // Find watchlist by ID
    const watchlistIndex = watchlists.findIndex(w => w.id === id && w.userId === req.user.id);
    
    if (watchlistIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Watchlist not found',
      });
    }
    
    // Update watchlist
    const watchlist = watchlists[watchlistIndex];
    
    if (name) {
      watchlist.name = name;
    }
    
    watchlist.updatedAt = new Date();
    
    // Update watchlist in mock database
    watchlists[watchlistIndex] = watchlist;
    
    return res.json({
      success: true,
      data: watchlist,
    });
  } catch (error) {
    console.error('Error in updateWatchlist controller:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to update watchlist',
      error: error.message,
    });
  }
};

/**
 * Delete a watchlist
 * @route DELETE /api/watchlists/:id
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} - JSON response with success message
 */
const deleteWatchlist = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Find watchlist by ID
    const watchlistIndex = watchlists.findIndex(w => w.id === id && w.userId === req.user.id);
    
    if (watchlistIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Watchlist not found',
      });
    }
    
    // Delete watchlist from mock database
    watchlists.splice(watchlistIndex, 1);
    
    return res.json({
      success: true,
      message: 'Watchlist deleted successfully',
    });
  } catch (error) {
    console.error('Error in deleteWatchlist controller:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to delete watchlist',
      error: error.message,
    });
  }
};

/**
 * Add a cryptocurrency to a watchlist
 * @route POST /api/watchlists/:id/cryptos
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} - JSON response with updated watchlist
 */
const addCryptoToWatchlist = async (req, res) => {
  try {
    const { id } = req.params;
    const { cryptoId } = req.body;
    
    // Validate input
    if (!cryptoId) {
      return res.status(400).json({
        success: false,
        message: 'Crypto ID is required',
      });
    }
    
    // Find watchlist by ID
    const watchlistIndex = watchlists.findIndex(w => w.id === id && w.userId === req.user.id);
    
    if (watchlistIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Watchlist not found',
      });
    }
    
    // Check if crypto is already in watchlist
    const watchlist = watchlists[watchlistIndex];
    
    if (watchlist.cryptos.includes(cryptoId)) {
      return res.status(400).json({
        success: false,
        message: 'Cryptocurrency is already in watchlist',
      });
    }
    
    // Add crypto to watchlist
    watchlist.cryptos.push(cryptoId);
    watchlist.updatedAt = new Date();
    
    // Update watchlist in mock database
    watchlists[watchlistIndex] = watchlist;
    
    return res.json({
      success: true,
      data: watchlist,
    });
  } catch (error) {
    console.error('Error in addCryptoToWatchlist controller:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to add cryptocurrency to watchlist',
      error: error.message,
    });
  }
};

/**
 * Remove a cryptocurrency from a watchlist
 * @route DELETE /api/watchlists/:id/cryptos/:cryptoId
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} - JSON response with updated watchlist
 */
const removeCryptoFromWatchlist = async (req, res) => {
  try {
    const { id, cryptoId } = req.params;
    
    // Find watchlist by ID
    const watchlistIndex = watchlists.findIndex(w => w.id === id && w.userId === req.user.id);
    
    if (watchlistIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Watchlist not found',
      });
    }
    
    // Check if crypto is in watchlist
    const watchlist = watchlists[watchlistIndex];
    const cryptoIndex = watchlist.cryptos.indexOf(cryptoId);
    
    if (cryptoIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Cryptocurrency not found in watchlist',
      });
    }
    
    // Remove crypto from watchlist
    watchlist.cryptos.splice(cryptoIndex, 1);
    watchlist.updatedAt = new Date();
    
    // Update watchlist in mock database
    watchlists[watchlistIndex] = watchlist;
    
    return res.json({
      success: true,
      data: watchlist,
    });
  } catch (error) {
    console.error('Error in removeCryptoFromWatchlist controller:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to remove cryptocurrency from watchlist',
      error: error.message,
    });
  }
};

/**
 * Get default watchlist for a user
 * @route GET /api/watchlists/default
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} - JSON response with default watchlist
 */
const getDefaultWatchlist = async (req, res) => {
  try {
    // Get user ID from authenticated request
    const userId = req.user.id;
    
    // Find default watchlist for user
    let defaultWatchlist = watchlists.find(w => w.userId === userId && w.name === 'Default');
    
    // If no default watchlist exists, create one
    if (!defaultWatchlist) {
      defaultWatchlist = {
        id: (watchlists.length + 1).toString(),
        userId,
        name: 'Default',
        cryptos: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      
      // Add watchlist to mock database
      watchlists.push(defaultWatchlist);
    }
    
    return res.json({
      success: true,
      data: defaultWatchlist,
    });
  } catch (error) {
    console.error('Error in getDefaultWatchlist controller:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to get default watchlist',
      error: error.message,
    });
  }
};

module.exports = {
  getWatchlists,
  createWatchlist,
  getWatchlistById,
  updateWatchlist,
  deleteWatchlist,
  addCryptoToWatchlist,
  removeCryptoFromWatchlist,
  getDefaultWatchlist,
};
