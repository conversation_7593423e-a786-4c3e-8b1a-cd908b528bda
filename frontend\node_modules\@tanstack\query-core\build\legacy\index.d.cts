export { I as AnyDataTag, b1 as CancelOptions, C as CancelledError, J as DataTag, z as DefaultError, b0 as DefaultOptions, af as DefaultedInfiniteQueryObserverOptions, ad as DefaultedQueryObserverOptions, aK as DefinedInfiniteQueryObserverResult, aB as DefinedQueryObserverResult, D as DehydrateOptions, x as DehydratedState, V as Enabled, ai as EnsureInfiniteQueryDataOptions, ah as EnsureQueryDataOptions, aj as FetchInfiniteQueryOptions, aq as FetchNextPageOptions, ar as FetchPreviousPageOptions, ag as FetchQueryOptions, at as FetchStatus, a1 as GetNextPageParamFunction, a0 as GetPreviousPageParamFunction, H as HydrateOptions, K as InferDataFromTag, L as InferErrorFromTag, a2 as InfiniteData, aD as InfiniteQueryObserverBaseResult, aG as InfiniteQueryObserverLoadingErrorResult, aF as InfiniteQueryObserverLoadingResult, ae as InfiniteQueryObserverOptions, aE as InfiniteQueryObserverPendingResult, aJ as InfiniteQueryObserverPlaceholderResult, aH as InfiniteQueryObserverRefetchErrorResult, aL as InfiniteQueryObserverResult, aI as InfiniteQueryObserverSuccessResult, a8 as InfiniteQueryPageParamsOptions, Y as InitialDataFunction, a7 as InitialPageParam, ao as InvalidateOptions, am as InvalidateQueryFilters, aU as MutateFunction, aT as MutateOptions, w as Mutation, M as MutationCache, d as MutationCacheNotifyEvent, g as MutationFilters, aQ as MutationFunction, aM as MutationKey, aP as MutationMeta, e as MutationObserver, aV as MutationObserverBaseResult, aY as MutationObserverErrorResult, aW as MutationObserverIdleResult, aX as MutationObserverLoadingResult, aS as MutationObserverOptions, a_ as MutationObserverResult, aZ as MutationObserverSuccessResult, aR as MutationOptions, aO as MutationScope, v as MutationState, aN as MutationStatus, a4 as NetworkMode, N as NoInfer, b4 as NotifyEvent, b3 as NotifyEventType, a5 as NotifyOnChangeProps, O as OmitKeyof, ac as Optional, y as Override, Z as PlaceholderDataFunction, _ as QueriesPlaceholderDataFunction, u as Query, Q as QueryCache, a as QueryCacheNotifyEvent, b as QueryClient, a$ as QueryClientConfig, j as QueryFilters, P as QueryFunction, X as QueryFunctionContext, A as QueryKey, $ as QueryKeyHashFunction, a3 as QueryMeta, c as QueryObserver, au as QueryObserverBaseResult, ax as QueryObserverLoadingErrorResult, aw as QueryObserverLoadingResult, aa as QueryObserverOptions, av as QueryObserverPendingResult, aA as QueryObserverPlaceholderResult, ay as QueryObserverRefetchErrorResult, aC as QueryObserverResult, az as QueryObserverSuccessResult, a6 as QueryOptions, W as QueryPersister, t as QueryState, as as QueryStatus, al as RefetchOptions, an as RefetchQueryFilters, R as Register, ap as ResetOptions, ak as ResultOptions, b2 as SetDataOptions, S as SkipToken, T as StaleTime, a9 as ThrowOnError, G as UnsetMarker, U as Updater, ab as WithRequired, E as dataTagErrorSymbol, B as dataTagSymbol, q as defaultShouldDehydrateMutation, p as defaultShouldDehydrateQuery, n as dehydrate, h as hashKey, o as hydrate, l as isCancelledError, i as isServer, k as keepPreviousData, f as matchMutation, m as matchQuery, r as replaceEqualDeep, s as skipToken, F as unsetMarker } from './hydration-Dmdl5wo-.cjs';
export { QueriesObserver, QueriesObserverOptions } from './queriesObserver.cjs';
export { InfiniteQueryObserver } from './infiniteQueryObserver.cjs';
export { defaultScheduler, notifyManager } from './notifyManager.cjs';
export { focusManager } from './focusManager.cjs';
export { onlineManager } from './onlineManager.cjs';
export { streamedQuery as experimental_streamedQuery } from './streamedQuery.cjs';
import './removable.cjs';
import './subscribable.cjs';
